#!/bin/bash

# HMP7 Video Renderer - Linux Build Script
# This script helps build the project on various Linux distributions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect Linux distribution
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo $ID
    elif [ -f /etc/redhat-release ]; then
        echo "rhel"
    elif [ -f /etc/debian_version ]; then
        echo "debian"
    else
        echo "unknown"
    fi
}

# Function to install dependencies based on distribution
install_dependencies() {
    local distro=$(detect_distro)
    print_status "Detected distribution: $distro"
    
    case $distro in
        "ubuntu"|"debian")
            print_status "Installing dependencies for Ubuntu/Debian..."
            sudo apt update
            sudo apt install -y \
                build-essential \
                cmake \
                ninja-build \
                pkg-config \
                libskia-dev \
                libffmpeg-dev \
                libfreetype6-dev \
                libfontconfig1-dev \
                libjpeg-dev \
                libpng-dev \
                libwebp-dev \
                libgl1-mesa-dev \
                libegl1-mesa-dev \
                libx11-dev \
                libxext-dev \
                libxrender-dev \
                libxrandr-dev \
                libxi-dev \
                libxcursor-dev \
                libxinerama-dev \
                libxxf86vm-dev \
                libasound2-dev \
                libpulse-dev \
                zenity \
                gdb \
                valgrind
            ;;
        "fedora"|"rhel"|"centos")
            print_status "Installing dependencies for Fedora/RHEL/CentOS..."
            sudo dnf install -y \
                gcc \
                gcc-c++ \
                cmake \
                ninja-build \
                pkgconfig \
                skia-devel \
                ffmpeg-devel \
                freetype-devel \
                fontconfig-devel \
                libjpeg-turbo-devel \
                libpng-devel \
                libwebp-devel \
                mesa-libGL-devel \
                mesa-libEGL-devel \
                libX11-devel \
                libXext-devel \
                libXrender-devel \
                libXrandr-devel \
                libXi-devel \
                libXcursor-devel \
                libXinerama-devel \
                libXxf86vm-devel \
                alsa-lib-devel \
                pulseaudio-libs-devel \
                zenity \
                gdb \
                valgrind
            ;;
        "arch"|"manjaro")
            print_status "Installing dependencies for Arch Linux..."
            sudo pacman -S --needed \
                base-devel \
                cmake \
                ninja \
                pkgconf \
                skia \
                ffmpeg \
                freetype2 \
                fontconfig \
                libjpeg-turbo \
                libpng \
                libwebp \
                mesa \
                libx11 \
                libxext \
                libxrender \
                libxrandr \
                libxi \
                libxcursor \
                libxinerama \
                libxxf86vm \
                alsa-lib \
                libpulse \
                zenity \
                gdb \
                valgrind
            ;;
        "opensuse"|"sles")
            print_status "Installing dependencies for openSUSE..."
            sudo zypper install -y \
                gcc \
                gcc-c++ \
                cmake \
                ninja \
                pkg-config \
                libskia-devel \
                ffmpeg-devel \
                freetype2-devel \
                fontconfig-devel \
                libjpeg8-devel \
                libpng16-devel \
                libwebp-devel \
                Mesa-libGL-devel \
                Mesa-libEGL-devel \
                libX11-devel \
                libXext-devel \
                libXrender-devel \
                libXrandr-devel \
                libXi-devel \
                libXcursor-devel \
                libXinerama-devel \
                libXxf86vm-devel \
                alsa-devel \
                libpulse-devel \
                zenity \
                gdb \
                valgrind
            ;;
        *)
            print_warning "Unknown distribution. Please install dependencies manually:"
            echo "  - Build tools: gcc, g++, cmake, ninja, pkg-config"
            echo "  - Graphics: skia, ffmpeg, freetype, fontconfig"
            echo "  - Image: libjpeg, libpng, libwebp"
            echo "  - OpenGL: mesa-gl, mesa-egl"
            echo "  - X11: libx11 and related libraries"
            echo "  - Audio: alsa, pulseaudio"
            echo "  - Tools: zenity, gdb, valgrind"
            ;;
    esac
}

# Function to install xmake
install_xmake() {
    if command -v xmake &> /dev/null; then
        print_success "xmake is already installed"
        return
    fi
    
    print_status "Installing xmake..."
    curl -fsSL https://xmake.io/shget.text | bash
    
    # Add xmake to PATH for current session
    export PATH="$HOME/.local/bin:$PATH"
    
    if command -v xmake &> /dev/null; then
        print_success "xmake installed successfully"
    else
        print_error "Failed to install xmake"
        exit 1
    fi
}

# Function to configure the project
configure_project() {
    print_status "Configuring project for Linux..."
    
    # Configure for Linux platform
    xmake config --plat=linux --arch=x86_64
    
    # Enable OpenMP if available
    if pkg-config --exists openmp; then
        print_status "OpenMP found, enabling parallel processing"
        export OPENMP_ENABLED=1
    fi
    
    print_success "Project configured successfully"
}

# Function to build the project
build_project() {
    local mode=${1:-release}
    
    print_status "Building project in $mode mode..."
    
    # Set build mode
    xmake config --mode=$mode
    
    # Build the project
    xmake build
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to run the project
run_project() {
    print_status "Running the project..."
    xmake run
}

# Main script logic
main() {
    echo "🎵 HMP7 Video Renderer - Linux Build Script"
    echo "============================================"
    echo ""
    
    # Parse command line arguments
    INSTALL_DEPS=false
    BUILD_MODE="release"
    RUN_AFTER_BUILD=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --install-deps)
                INSTALL_DEPS=true
                shift
                ;;
            --debug)
                BUILD_MODE="debug"
                shift
                ;;
            --run)
                RUN_AFTER_BUILD=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --install-deps    Install system dependencies"
                echo "  --debug          Build in debug mode"
                echo "  --run            Run the project after building"
                echo "  --help           Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Install dependencies if requested
    if [ "$INSTALL_DEPS" = true ]; then
        install_dependencies
    fi
    
    # Install xmake
    install_xmake
    
    # Configure project
    configure_project
    
    # Build project
    build_project $BUILD_MODE
    
    # Run project if requested
    if [ "$RUN_AFTER_BUILD" = true ]; then
        run_project
    fi
    
    print_success "All done! 🎉"
    echo ""
    echo "To run the project manually:"
    echo "  xmake run"
    echo ""
    echo "To build in debug mode:"
    echo "  xmake config --mode=debug && xmake build"
    echo ""
}

# Run main function with all arguments
main "$@"
