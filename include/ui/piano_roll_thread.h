#ifndef PIANO_ROLL_THREAD_H
#define PIANO_ROLL_THREAD_H

#include <include/core/SkCanvas.h>
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// スレッドプールの状態
typedef enum {
    THREAD_POOL_IDLE,       // アイドル状態
    THREAD_POOL_RUNNING,    // 実行中
    THREAD_POOL_STOPPING    // 停止処理中
} ThreadPoolState;

// ピアノロールのスレッド処理を初期化
// threadCount: 使用するスレッド数（0以下の場合は自動設定）
bool initPianoRollThreadPool(int threadCount);

// ピアノロールのスレッド処理をクリーンアップ
void cleanupPianoRollThreadPool(void);

// スレッド数を設定（0以下の場合は自動設定）
void setPianoRollThreadCount(int threadCount);

// 現在のスレッド数を取得
int getPianoRollThreadCount(void);

// マルチスレッド処理が有効かどうかを確認
bool isPianoRollMultithreaded(void);

// マルチスレッド処理を有効/無効にする
void enablePianoRollMultithreading(bool enable);

// マルチスレッドでピアノロールを描画
// canvas: 描画先のSkiaキャンバス
// currentTime: 現在の時間
// startIndex: 処理開始インデックス
// endIndex: 処理終了インデックス
// 戻り値: 成功した場合はtrue、失敗した場合はfalse
bool drawPianoRollMultithreaded(SkCanvas* canvas, float currentTime, int startIndex, int endIndex);

#ifdef __cplusplus
}
#endif

#endif // PIANO_ROLL_THREAD_H
