#ifndef PIANO_ROLL_H
#define PIANO_ROLL_H

#include "../../include/skia/include/core/SkCanvas.h"
#include "../../include/skia/include/core/SkSurface.h"
#include "../../include/skia/include/core/SkImage.h"
#include <stdbool.h>
#include <stdint.h>

// ピアノロールの定数
#define PIANO_ROLL_TIME_RANGE 0.5f     // 表示する時間範囲（秒）- 0.5が一番ちょうど良い
#define PIANO_ROLL_NOTE_HEIGHT 0       // ノートの高さ（ピクセル）
#define PIANO_ROLL_MIN_NOTE 0          // 表示する最低ノート番号
#define PIANO_ROLL_MAX_NOTE 127        // 表示する最高ノート番号
#define PIANO_ROLL_ALPHA 1.0f          // ピアノロールの透明度
#define PIANO_ROLL_CACHE_ENABLED false // キャッシュ機能を無効にしてスムーズなFPSを実現
#define PIANO_ROLL_CACHE_UPDATE_INTERVAL 0.016f // キャッシュ更新間隔（秒）- 60FPS対応

// ピアノロールのキャッシュ構造体
typedef struct {
    sk_sp<SkSurface> surface;      // キャッシュ用サーフェス
    sk_sp<SkImage> image;          // キャッシュ済みイメージ
    float lastUpdateTime;          // 最後に更新した時間
    bool valid;                    // キャッシュが有効かどうか
    int width;                     // キャッシュの幅
    int height;                    // キャッシュの高さ
} PianoRollCache;

// ピアノロールの表示/非表示フラグ
extern bool showPianoRoll;

// ピアノロールの初期化関数
void initPianoRoll(void);

// ピアノロールの更新関数
void updatePianoRoll(float currentTime);

// ピアノロールの描画関数（Skia版）
void drawPianoRoll(SkCanvas* canvas, float currentTime);

// 指定された範囲のノートを描画する関数
void drawPianoRollRange(SkCanvas* canvas, float currentTime, int startIndex, int endIndex);

// ノートが無効化されているかチェックする関数
bool isNoteInvalidated(uint32_t noteIndex);

// ピアノロールのクリーンアップ関数
void cleanupPianoRoll(void);

// 無効化されたノートの総数を取得する関数
uint32_t getPianoRollInvalidatedNoteCount(void);

// 現在有効なノート数を取得する関数
uint32_t getPianoRollActiveNoteCount(void);

// 総ノート数を取得する関数（MIDIファイルの総ノート数を返す）
uint32_t getPianoRollTotalNoteCount(void);

// 累積ノートカウントを取得する関数（現在時刻までのノートオンイベントの総数）
// このカウンターはノートオンイベントのみをカウントし、ノートオフイベントでは減少しない
uint32_t getPianoRollCumulativeNoteCount(void);

// 累積ノートカウントの補正値を取得する関数
uint32_t getPianoRollCumulativeNoteCountOffset(void);

// ピアノロールのキャッシュを初期化する関数
void initPianoRollCache(int width, int height);

// ピアノロールのキャッシュを更新する関数
void updatePianoRollCache(float currentTime);

// ピアノロールのキャッシュを描画する関数
void drawPianoRollCache(SkCanvas* canvas);

// ピアノロールのキャッシュをクリーンアップする関数
void cleanupPianoRollCache(void);

// ピアノロールのキャッシュが有効かどうかを取得する関数
bool isPianoRollCacheValid(void);

// ピアノロールのキャッシュを明示的に無効化する関数
void invalidatePianoRollCache(void);

// ピアノロールのコンテンツを指定されたキャンバスに描画する内部関数
void renderPianoRollToCanvas(SkCanvas* targetCanvas, float timeForRender);

#endif // PIANO_ROLL_H
