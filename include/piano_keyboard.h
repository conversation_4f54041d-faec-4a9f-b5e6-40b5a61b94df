#ifndef PIANO_KEYBOARD_H
#define PIANO_KEYBOARD_H

#include <SkCanvas.h>
#include <stdbool.h>
#include <stdint.h>

// ピアノキーボードの定数
#define PIANO_KEY_COUNT 128       // 全MIDIノート範囲（0-127）
#define PIANO_FIRST_NOTE 0        // 最初の鍵盤のMIDIノート番号（0）
#define PIANO_WHITE_KEY_HEIGHT 130 // 白鍵の高さ（より大きく）
#define PIANO_BLACK_KEY_HEIGHT 90 // 黒鍵の高さ（より大きく）
#define PIANO_BLACK_KEY_WIDTH_RATIO 0.6f // 黒鍵の幅の比率（白鍵に対して）
#define PIANO_FADE_TIME 0.2f      // フェードアウト時間（秒）

// ピアノキーボードの表示/非表示フラグ
extern bool showPianoKeyboard;

// ピアノキーボードの初期化関数
void initPianoKeyboard(void);

// ピアノキーボードの更新関数
void updatePianoKeyboard(float currentTime);

// ピアノキーボードの描画関数（Skia版）
void drawPianoKeyboard(SkCanvas* canvas);

// ノートオン/オフイベントのハンドリング関数
void handleNoteOn(uint8_t note, uint8_t velocity, uint8_t channel);
void handleNoteOff(uint8_t note, uint8_t channel);

// ピアノキーボードの表示/非表示を切り替える関数
void togglePianoKeyboardVisibility(void);

// ピアノキーボードのクリーンアップ関数
void cleanupPianoKeyboard(void);

#endif // PIANO_KEYBOARD_H
