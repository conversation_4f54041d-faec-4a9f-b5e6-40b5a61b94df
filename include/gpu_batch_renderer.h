#ifndef GPU_BATCH_RENDERER_H
#define GPU_BATCH_RENDERER_H

#include <stdbool.h>
#include <stdint.h>
#include <include/core/SkCanvas.h>
#include <include/core/SkPaint.h>
#include <include/core/SkRect.h>
#include <include/core/SkPath.h>
#include <vector>

#ifdef __cplusplus
extern "C" {
#endif

// バッチレンダリング用の構造体
typedef struct {
    float x, y, width, height;
    uint32_t color;
} BatchRect;

typedef struct {
    float x1, y1, x2, y2;
    uint32_t color;
    float strokeWidth;
} BatchLine;

typedef struct {
    const char* text;
    float x, y;
    float fontSize;
    uint32_t color;
} BatchText;

// バッチレンダラー構造体
typedef struct {
    // 矩形バッチ
    std::vector<BatchRect>* rects;
    std::vector<BatchRect>* filledRects;
    
    // 線バッチ
    std::vector<BatchLine>* lines;
    
    // テキストバッチ
    std::vector<BatchText>* texts;
    
    // 共通ペイントオブジェクト
    SkPaint* rectPaint;
    SkPaint* linePaint;
    SkPaint* textPaint;
    
    // 初期化フラグ
    bool initialized;
} GPUBatchRenderer;

// グローバルバッチレンダラー
extern GPUBatchRenderer batchRenderer;

// バッチレンダラーの初期化
bool initBatchRenderer();

// バッチレンダラーのクリーンアップ
void cleanupBatchRenderer();

// バッチに追加する関数群
void addRectToBatch(float x, float y, float width, float height, uint32_t color, bool filled);
void addLineToBatch(float x1, float y1, float x2, float y2, uint32_t color, float strokeWidth);
void addTextToBatch(const char* text, float x, float y, float fontSize, uint32_t color);

// バッチを実行する関数
void flushRectBatch(SkCanvas* canvas);
void flushLineBatch(SkCanvas* canvas);
void flushTextBatch(SkCanvas* canvas);
void flushAllBatches(SkCanvas* canvas);

// バッチをクリアする関数
void clearBatches();

// 最適化されたピアノロール描画
void drawPianoRollBatched(SkCanvas* canvas, float currentTime);

// 最適化されたグラフ描画
void drawGraphsBatched(SkCanvas* canvas);

#ifdef __cplusplus
}
#endif

#endif // GPU_BATCH_RENDERER_H
