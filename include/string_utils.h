#ifndef STRING_UTILS_H
#define STRING_UTILS_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 数値をカンマ区切りの文字列に変換する関数
void formatNumberWithCommas(char *buffer, size_t bufferSize, uint32_t number);

// ファイル名から拡張子を取得する関数
const char* getFileExtension(const char* filePath);

// ファイル名からベース名を取得する関数
void getBaseName(const char* filePath, char* baseNameBuffer, size_t bufferSize);

// 文字列に日本語文字が含まれているかを確認する関数
bool containsJapaneseChars(const char* path);

#ifdef __cplusplus
}
#endif

#endif // STRING_UTILS_H
