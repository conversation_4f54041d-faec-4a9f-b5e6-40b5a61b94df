#ifndef APP_STATE_H
#define APP_STATE_H

#include <stdint.h>
#include <stdbool.h>
#include "../include/midi_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

// 定数定義
#define WIDTH 1920
#define HEIGHT 1080
#define FPS 60
#define DURATION 10  // 動画のデフォルト長さ（秒）
#define MAX_PATH_LENGTH 4096
#define JAPANESE_FONT_PATH "C:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test\\NotoSansJP-SemiBold.ttf"
#define MAX_NOTE_EVENTS 1000000000  // 最大100万ノートまで対応

// 一時的なノートイベント配列（ノートONとノートOFFの対応付け用）
typedef struct {
    float onTime;      // ノートON時間
    float offTime;     // ノートOFF時間（デフォルトは0）
    uint32_t onTick;   // ノートONティック
    uint32_t offTick;  // ノートOFFティック
    uint8_t velocity;  // ベロシティ
    uint8_t channel;   // チャンネル
    uint8_t note;      // ノート番号
    uint8_t track;     // トラック番号
    bool hasOff;       // ノートOFFが見つかったかどうか
} TempNoteEvent;

// アプリケーション状態構造体
typedef struct {
    // ファイルパス
    char midiFilePath[MAX_PATH_LENGTH];
    char audioFilePath[MAX_PATH_LENGTH];
    char outputFilePath[MAX_PATH_LENGTH];
    bool hasAudio;

    // MIDIデータ
    MidiFile *midiFile;
    NoteTimeEvent *noteTimeEvents;
    uint32_t noteTimeEventCount;
    uint32_t noteTimeEventCapacity;
    uint32_t maxPolyphony;
    float maxNps;

    // ビデオ設定
    float videoDuration;
    int totalFrames;

    // 処理状態
    bool initialized;

    // UI表示モード
    bool previewMode;  // プレビューモード（レンダリングせずにUIのみ表示）
} AppState;

// グローバルアプリケーション状態
extern AppState appState;

// 関数プロトタイプ
void initAppState();
void cleanupAppState();
void updateMaxValues(uint32_t currentNps, uint16_t currentPolyphony);

#ifdef __cplusplus
}
#endif

#endif // APP_STATE_H
