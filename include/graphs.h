#ifndef GRAPHS_H
#define GRAPHS_H

#include "../include/skia/include/core/SkCanvas.h"
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// グラフ関連の定数
#define GRAPH_HISTORY_LENGTH 240 // 履歴長 (約4秒分 @ 60Hz更新)
#define GRAPH_LINE_THICKNESS 3.2f
#define GRAPH_SCALE_SMOOTH_FACTOR 5.0f // スムージングの速さ
#define GRAPH_UPDATE_INTERVAL (1.0f / 60.0f) // グラフ更新間隔 (秒) - 60Hz相当

// グラフの位置とサイズを格納する構造体
typedef struct {
    int x_notes;
    int y_notes;
    int width_notes;
    int height_notes;

    int x_nps;
    int y_nps;
    int width_nps;
    int height_nps;

    int x_poly;
    int y_poly;
    int width_poly;
    int height_poly;

    int x_bpm;
    int y_bpm;
    int width_bpm;
    int height_bpm;

    int x_pan;
    int y_pan;
    int width_pan;
    int height_pan;

    int x_pb;
    int y_pb;
    int width_pb;
    int height_pb;

    int x_volume;
    int y_volume;
    int width_volume;
    int height_volume;

    int x_sustain;
    int y_sustain;
    int width_sustain;
    int height_sustain;

    int x_filter_cutoff;
    int y_filter_cutoff;
    int width_filter_cutoff;
    int height_filter_cutoff;

    int x_filter_resonance;
    int y_filter_resonance;
    int width_filter_resonance;
    int height_filter_resonance;

    int x_release_time;
    int y_release_time;
    int width_release_time;
    int height_release_time;
} GraphPositions;

// グラフの位置情報（外部からアクセス可能）
extern GraphPositions graphPos;

// グラフ初期化関数
void initGraphs(int width, int height);

// グラフ更新関数
void updateGraphs(float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                 uint32_t currentNps, uint16_t currentPolyphony);

// グラフの位置とサイズを更新する関数
void updateGraphPositions(int width, int height);

// グラフ描画関数
void drawGraphs(SkCanvas* canvas);

// 個別グラフ描画関数
void drawGraphNotes(SkCanvas* canvas);
void drawGraphNPS(SkCanvas* canvas);
void drawGraphPolyphony(SkCanvas* canvas);
void drawGraphBPM(SkCanvas* canvas);
void drawGraphPan(SkCanvas* canvas);
void drawGraphPitchBend(SkCanvas* canvas);
void drawGraphVolume(SkCanvas* canvas);
void drawGraphSustain(SkCanvas* canvas);
void drawGraphFilterCutoff(SkCanvas* canvas);
void drawGraphFilterResonance(SkCanvas* canvas);
void drawGraphReleaseTime(SkCanvas* canvas);

// 外部関数（他のファイルで定義）
float getCurrentBPM(void);
int getCurrentPan(int channel);
int getCurrentPitchBend(int channel);
int getCurrentVolume(int channel);
int getCurrentSustain(int channel);
int getCurrentFilterCutoff(int channel);
int getCurrentFilterResonance(int channel);
int getCurrentReleaseTime(int channel);

#ifdef __cplusplus
}
#endif

#endif // GRAPHS_H
