#ifndef PERFORMANCE_H
#define PERFORMANCE_H

#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// パフォーマンス測定構造体
typedef struct {
    clock_t totalStartTime;
    clock_t frameStartTime;
    double totalElapsedSeconds;
    double frameElapsedSeconds;
    double averageFrameTime;
    double currentFps;
    double cpuUsage;
} PerformanceMetrics;

// グローバルパフォーマンスメトリクス
extern PerformanceMetrics perfMetrics;

// 関数プロトタイプ
void initPerformanceMetrics();
void startFrameTimer();
void endFrameTimer(int frameCounter, int totalFrames);
double getCPUUsage();
double getEstimatedRemainingTime(int frameCounter, int totalFrames);

// 詳細なパフォーマンス計測関数
void startPerformanceMeasurement(const char* name);
void endPerformanceMeasurement(const char* name);

// 進捗表示関数
void initProgressDisplay();
void updateProgressDisplay(int frameCounter, int totalFrames, float currentTime,
                          uint32_t currentNotes, uint32_t totalNotes,
                          uint32_t currentNps, uint32_t maxNps,
                          uint16_t currentPolyphony, uint16_t maxPolyphony);

#ifdef __cplusplus
}
#endif

#endif // PERFORMANCE_H
