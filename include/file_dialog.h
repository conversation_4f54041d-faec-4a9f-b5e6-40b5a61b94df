#ifndef FILE_DIALOG_H
#define FILE_DIALOG_H

#include <stdbool.h>
#include "string_utils.h"

// ファイルダイアログを表示してMIDIファイルを選択する関数
char* openMidiFileDialog();

// ファイルダイアログを表示してオーディオファイルを選択する関数
char* openAudioFileDialog();

// ファイル保存ダイアログを表示して出力ファイルを選択する関数
char* saveOutputFileDialog(const char* defaultFileName);

// 日本語パスを処理する関数
char* createNonJapanesePath(const char* originalPath, const char* extension, const char* prefix);

#endif // FILE_DIALOG_H
