#ifndef VIDEO_ENCODER_H
#define VIDEO_ENCODER_H

#include <stdbool.h>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswscale/swscale.h>
}

#include <include/core/SkImage.h>
#include "../include/app_state.h"

// ビデオエンコーダーコンテキスト
typedef struct {
    // FFmpeg関連の変数
    AVFormatContext *formatContext;
    AVCodecContext *videoCodecContext;
    AVStream *videoStream;
    AVFrame *frame;
    AVPacket *packet;

    // オーディオ関連の変数
    AVFormatContext *audioFormatContext;
    AVCodecContext *audioCodecContext;      // エンコーダー
    AVCodecContext *audioDecoderContext;    // デコーダー
    AVStream *audioStream;
    AVPacket *audioPacket;
    AVFrame *audioFrame;                    // デコードされたオーディオフレーム
    AVFrame *audioFrameResampled;           // リサンプル後のオーディオフレーム
    struct SwrContext *swrContext;          // オーディオリサンプラー

    // 変換コンテキスト
    struct SwsContext *swsContext;

    // バッファ
    uint8_t *rgbaBuffer;

    // 状態
    bool initialized;
    int frameCounter;
    int errorCount;  // エンコードエラーのカウンター
    float videoDuration; // ビデオの総時間（秒）
    bool audioEOF; // オーディオファイルの終端に達したかどうか
} VideoEncoder;

// グローバルビデオエンコーダー
extern VideoEncoder videoEncoder;

// 関数プロトタイプ
bool initVideoEncoder(const char* outputFilePath, const char* audioFilePath, bool hasAudio, float videoDuration);
bool encodeFrame(sk_sp<SkImage> image, float currentTime);
bool finalizeVideoEncoder();
void cleanupVideoEncoder();

#endif // VIDEO_ENCODER_H
