#ifndef THREADING_H
#define THREADING_H

#include <stdbool.h>
#include <stdint.h>
#include <include/core/SkImage.h>

#ifdef __cplusplus
extern "C" {
#endif

// スレッド状態の定義
typedef enum {
    THREAD_STATE_IDLE,       // スレッドはアイドル状態
    THREAD_STATE_RUNNING,    // スレッドは実行中
    THREAD_STATE_PAUSED,     // スレッドは一時停止中
    THREAD_STATE_STOPPING,   // スレッドは停止処理中
    THREAD_STATE_STOPPED     // スレッドは停止済み
} ThreadState;

// レンダリングジョブの定義
typedef struct {
    int frameCounter;            // フレーム番号
    float currentTime;           // 現在の時間
    uint32_t currentNotes;       // 現在のノート数
    uint32_t totalNotes;         // 総ノート数
    uint32_t currentNps;         // 現在のNPS
    uint32_t maxNps;             // 最大NPS
    uint16_t currentPolyphony;   // 現在のポリフォニー
    uint16_t maxPolyphony;       // 最大ポリフォニー
    sk_sp<SkImage> image;        // レンダリングされたフレーム
} RenderJob;

// スレッド間で共有するデータ
typedef struct {
    ThreadState state;           // スレッドの状態
    RenderJob currentJob;        // 現在処理中のジョブ
    bool jobAvailable;           // 新しいジョブが利用可能か
    bool jobCompleted;           // ジョブが完了したか

    // 進捗情報
    float progress;              // 進捗率（0.0〜1.0）
    char statusMessage[256];     // ステータスメッセージ

    // 同期用
    void* mutex;                 // ミューテックス
    void* condition;             // 条件変数
} ThreadData;

// グローバルスレッドデータ
extern ThreadData encoderThreadData;

// 関数プロトタイプ
// スレッド初期化・終了
bool initThreading(void);
void cleanupThreading(void);

// エンコーダースレッド制御
bool startEncoderThread(void);
void stopEncoderThread(void);
void pauseEncoderThread(void);
void resumeEncoderThread(void);

// ジョブ管理
bool submitRenderJob(int frameCounter, float currentTime,
                    uint32_t currentNotes, uint32_t totalNotes,
                    uint32_t currentNps, uint32_t maxNps,
                    uint16_t currentPolyphony, uint16_t maxPolyphony,
                    sk_sp<SkImage> image);
bool isEncoderThreadBusy(void);
float getEncoderThreadProgress(void);
const char* getEncoderThreadStatus(void);

#ifdef __cplusplus
}
#endif

#endif // THREADING_H
