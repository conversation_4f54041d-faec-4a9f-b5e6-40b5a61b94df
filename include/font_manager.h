#ifndef FONT_MANAGER_H
#define FONT_MANAGER_H

#include <stdbool.h>
#include "../include/skia/include/core/SkCanvas.h"
#include "../include/skia/include/core/SkFont.h"
#include "../include/skia/include/core/SkTypeface.h"

// フォントの種類
typedef enum {
    FONT_DEFAULT,
    FONT_JAPANESE
} FontType;

// フォントコンテキスト
typedef struct {
    sk_sp<SkTypeface> default_font;
    sk_sp<SkTypeface> japanese_font;
    bool initialized;
} FontContext;

// グローバルフォントコンテキスト
extern FontContext fontContext;

// フォントコンテキストを初期化する関数
bool initFontContext();

// フォントを設定する関数
SkFont setFont(FontType type, float size);

// フォントリソースを解放する関数
void cleanupFontContext();

#endif // FONT_MANAGER_H
