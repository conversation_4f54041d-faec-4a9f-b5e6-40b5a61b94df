#ifndef COLOR_H
#define COLOR_H

#include <stdint.h>

// チャンネルカラーの配列（外部から参照可能）
extern unsigned int channel_colors[16];

// カラー操作用の関数
void initColors(void);

// RGBからunsigned intへの変換
unsigned int rgb_to_uint(uint8_t r, uint8_t g, uint8_t b);

// unsigned intからRGBへの変換
void uint_to_rgb(unsigned int color, uint8_t* r, uint8_t* g, uint8_t* b);

// カラーの明るさを調整する関数
unsigned int adjust_brightness(unsigned int color, float factor);

#endif // COLOR_H
