#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <string>
#include <stdint.h>
#include <toml++/toml.h>
#include <nlohmann/json.hpp>

// 設定ファイル形式の列挙型
enum ConfigFormat {
    FORMAT_TOML,
    FORMAT_JSON,
    FORMAT_UNKNOWN
};

// 色モードの列挙型
typedef enum {
    COLOR_MODE_CHANNEL = 0,  // チャンネルベースの色分け
    COLOR_MODE_TRACK = 1     // トラックベースの色分け
} ColorMode;

// 色設定用の構造体
typedef struct {
    // 色モード設定
    ColorMode colorMode;            // 色分けモード（チャンネル or トラック）

    // MIDIチャンネル色
    uint32_t channelColors[16];
      // UI色
    uint32_t backgroundColor;       // 背景色
    uint32_t gridLineColor;         // グリッド線の色
    uint32_t borderColor;           // ボーダー色
    uint32_t graphBackgroundColor;  // グラフ背景色
    uint32_t counter_textColor;     // カウンター表示用テキスト色（Time, Notes, NPS, Polyphony, Markerなど）
    uint32_t graph_textColor;       // グラフ内のテキスト色
    uint32_t graphProgressBarColor; // グラフ進捗バー色
    uint32_t graphCenterLineColor;  // グラフ中央線色
} ColorConfig;

// Visual設定用の構造体
typedef struct {
    // マスタートグル
    bool showAll;                   // 全ビジュアル要素の表示/非表示（マスタートグル）

    // メインUIコンポーネントの表示設定
    bool showPiano;                 // ピアノキーボードの表示/非表示
    bool showPianoroll;             // ピアノロールの表示/非表示
    bool showHighlight;             // ノートハイライト効果の表示/非表示（アクティブノートの明度上昇）
    bool showGraphs;                // 全グラフの表示/非表示（マスタートグル）
    bool showCounters;              // 全カウンターの表示/非表示（マスタートグル）
    bool showTextEvents;            // 全テキストイベントの表示/非表示（マスタートグル）
    bool showDebug;                 // デバッグ情報の表示/非表示（マスタートグル）

    // 個別グラフの表示設定
    struct {
        bool showNotes;             // ノートグラフの表示/非表示
        bool showPolyphony;         // ポリフォニーグラフの表示/非表示
        bool showNps;               // NPSグラフの表示/非表示
        bool showBpm;               // BPMグラフの表示/非表示
        bool showPan;               // パングラフの表示/非表示
        bool showPitchBend;         // ピッチベンドグラフの表示/非表示
        bool showVolume;            // ボリュームグラフの表示/非表示
        bool showSustain;           // サステイングラフの表示/非表示
        bool showFilterCutoff;      // フィルターカットオフグラフの表示/非表示
        bool showFilterResonance;   // フィルターレゾナンスグラフの表示/非表示
        bool showReleaseTime;       // リリースタイムグラフの表示/非表示
    } graphs;

    // カウンター表示設定
    struct {
        bool showTime;              // 時間カウンターの表示/非表示
        bool showNotes;             // ノートカウンターの表示/非表示
        bool showNps;               // NPSカウンターの表示/非表示
        bool showPolyphony;         // ポリフォニーカウンターの表示/非表示
    } counters;

    // テキストイベント表示設定
    struct {
        bool showMarkers;           // マーカーイベントの表示/非表示
        bool showText;              // テキストイベントの表示/非表示
        bool showLyrics;            // 歌詞イベントの表示/非表示
    } textEvents;
} VisualConfig;

// Funny/Debug Mode設定構造体
typedef struct {
    bool bugMode;                   // バグモード: 頂点データのバグや不正なデータが発生した時の動作を有効にする
} FunnyConfig;

// 設定マネージャークラス
class ConfigManager {
private:
    static ConfigManager* instance;
    
    std::string configFilePath;
    toml::table tomlData;
    nlohmann::json jsonData;
    ColorConfig colorConfig;
    VisualConfig visualConfig;
    FunnyConfig funnyConfig;
    ConfigFormat configFormat;
    
    // 設定のデフォルト値を設定
    void setDefaults();
    
    // プライベートコンストラクタ (シングルトン)
    ConfigManager();
    
    // ファイル形式を検出
    ConfigFormat detectFileFormat(const std::string& filePath);
    
    // TOMLファイルから読み込む
    bool loadFromToml(const std::string& filePath);
    
    // JSONファイルから読み込む
    bool loadFromJson(const std::string& filePath);
    
    // TOMLファイルとして保存
    bool saveToToml(const std::string& filePath);
    
    // JSONファイルとして保存
    bool saveToJson(const std::string& filePath);
    
public:
    // インスタンスを取得
    static ConfigManager* getInstance();
    
    // 設定ファイルを読み込む
    bool loadConfig(const std::string& filePath = "");
    
    // 設定ファイルを保存する
    bool saveConfig(const std::string& filePath = "", ConfigFormat format = FORMAT_UNKNOWN);
    
    // 色設定を取得
    const ColorConfig& getColorConfig() const;

    // 色設定を更新
    void updateColorConfig(const ColorConfig& newColors);

    // Visual設定を取得
    const VisualConfig& getVisualConfig() const;

    // Visual設定を更新
    void updateVisualConfig(const VisualConfig& newVisual);

    // Funny設定を取得
    const FunnyConfig& getFunnyConfig() const;

    // Funny設定を更新
    void updateFunnyConfig(const FunnyConfig& newFunny);

    // 特定のチャンネル色を取得
    uint32_t getChannelColor(int channel) const;
      // 特定のチャンネル色を設定
    void setChannelColor(int channel, uint32_t color);

    // トラックベースの色を取得（トラック番号に基づく）
    uint32_t getTrackColor(int trackIndex) const;

    // 色モードに基づいて適切な色を取得（ピアノロール・ピアノキーボード用）
    uint32_t getPianoColor(int channel, int trackIndex) const;
    
    // 16進数文字列から色を解析
    static uint32_t parseHexColor(const std::string& hexColor);
    
    // 色を16進数文字列に変換
    static std::string colorToHexString(uint32_t color);
    
    // スキーマファイルを検証 (JSON用)
    bool validateJsonSchema(const std::string& jsonFilePath, const std::string& schemaFilePath);
    
    // JSONスキーマファイルを生成
    bool generateJsonSchema(const std::string& schemaFilePath = "");
    
    // 実行ファイルと同じディレクトリの設定ファイルパスを取得
    static std::string getDefaultConfigFilePath();
};

#endif // CONFIG_MANAGER_H
