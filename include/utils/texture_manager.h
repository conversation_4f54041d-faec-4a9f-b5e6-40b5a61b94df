#ifndef TEXTURE_MANAGER_H
#define TEXTURE_MANAGER_H

#include <include/core/SkCanvas.h>
#include <include/core/SkImage.h>
#include <stdbool.h>

// テクスチャの種類を定義
typedef enum {
    TEXTURE_KEY_WHITE,
    TEXTURE_KEY_WHITE_PRESSED,
    TEXTURE_KEY_BLACK,
    TEXTURE_KEY_BLACK_PRESSED,
    TEXTURE_NOTE,
    TEXTURE_COUNT // テクスチャの総数
} TextureType;

// バッファリングシステムを削除して最適化

// テクスチャマネージャーの初期化
bool initTextureManager(void);

// テクスチャマネージャーのクリーンアップ
void cleanupTextureManager(void);

// 指定されたテクスチャを取得
sk_sp<SkImage> getTexture(TextureType type);

// テクスチャを描画する（指定された位置とサイズ）
void drawTexture(SkCanvas* canvas, TextureType type, float x, float y, float width, float height);

// テクスチャを描画する（色付き）- 最適化版
void drawTextureWithColor(SkCanvas* canvas, TextureType type, float x, float y, float width, float height,
                         float r, float g, float b, float a);

#endif // TEXTURE_MANAGER_H
