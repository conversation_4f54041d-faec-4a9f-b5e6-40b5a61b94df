#ifndef GPU_CONTEXT_H
#define GPU_CONTEXT_H

#include <stdbool.h>
#include <pthread.h>
#include <include/core/SkSurface.h>
#include <include/gpu/GrDirectContext.h>
#include <include/gpu/gl/GrGLInterface.h>

#ifdef WINDOWS_PLATFORM
#include <windows.h>
#include <GL/gl.h>
#include <wingdi.h>
#elif defined(LINUX_PLATFORM)
#include <GL/gl.h>
#include <EGL/egl.h>
#include <X11/Xlib.h>
#endif

// GPU コンテキスト構造体
typedef struct {
    // Skia GPU コンテキスト
    sk_sp<GrDirectContext> grContext;

    // OpenGL インターフェース
    sk_sp<const GrGLInterface> glInterface;

    // プラットフォーム固有のコンテキスト
#ifdef WINDOWS_PLATFORM
    HDC hdc;
    HGLRC hglrc;
    HW<PERSON> hwnd;
    PIXELFORMATDESCRIPTOR pfd;
#elif defined(LINUX_PLATFORM)
    Display* display;
    EGLDisplay eglDisplay;
    EGLContext eglContext;
    EGLSurface eglSurface;
    EGLConfig eglConfig;
#endif

    // 初期化状態
    bool initialized;

    // サーフェスサイズ
    int width;
    int height;

    // スレッド安全性のためのミューテックス
    pthread_mutex_t mutex;
} GPUContext;

// グローバルGPUコンテキスト
extern GPUContext gpuContext;

// GPU コンテキストを初期化する関数
bool initGPUContext(int width, int height);

// GPU サーフェスを作成する関数
sk_sp<SkSurface> createGPUSurface(int width, int height);

// GPU コンテキストをフラッシュする関数
void flushGPUContext();

// GPU コンテキストを解放する関数
void cleanupGPUContext();

// GPU が利用可能かチェックする関数
bool isGPUAvailable();

// OpenGL エラーをチェックする関数
bool checkGLError(const char* operation);

#endif // GPU_CONTEXT_H
