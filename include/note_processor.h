#ifndef NOTE_PROCESSOR_H
#define NOTE_PROCESSOR_H

#include <stdint.h>
#include "../include/midi_utils.h"
#include "../include/app_state.h"

// ノート時間イベントを時間順にソートするための比較関数
int compareNoteTimeEvents(const void *a, const void *b);

// 二分探索で指定した時間以下のノートオンイベント数を取得する関数
// このカウンターはノートオンイベントのみをカウントし、ノートオフイベントでは減少しない
uint32_t countNotesUpToTime(float targetTime);

// 指定した時間ウィンドウ内のノート数を取得する関数
uint32_t countNotesInTimeWindow(float startTime, float endTime);

// 指定した時間での同時発音数を計算する関数
uint16_t calculatePolyphonyAtTime(float currentTime);

// ノートイベントを収集する関数
bool collectNoteEvents(MidiFile* midiFile, NoteTimeEvent** outEvents, uint32_t* outCount);

// ノートイベントをソートする関数
void sortNoteEvents(NoteTimeEvent* events, uint32_t count);

// 時間（秒）からティックを逆算する関数
uint32_t secondsToTick(float seconds, uint16_t division);

#endif // NOTE_PROCESSOR_H
