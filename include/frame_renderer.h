#ifndef FRAME_RENDERER_H
#define FRAME_RENDERER_H

#include <include/core/SkCanvas.h>
#include <include/core/SkSurface.h>
#include <include/core/SkImage.h>
#include <include/core/SkData.h>
#include "../include/app_state.h"
#include "../include/midi_utils.h"
#include "../include/performance.h"

// フレームレンダリング関数
sk_sp<SkImage> renderFrame(float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                          uint32_t currentNps, uint16_t currentPolyphony);

// 統計情報を描画する関数
void drawStatistics(SkCanvas* canvas, float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                   uint32_t currentNps, uint32_t maxNps,
                   uint16_t currentPolyphony, uint16_t maxPolyphony);

// テキストイベントを描画する関数
void drawTextEvents(SkCanvas* canvas, MidiFile* midiFile, float currentTime);

// レンダリングステータスを描画する関数
void drawRenderingStatus(SkCanvas* canvas, float currentTime);

// 再生位置を更新する関数
void updatePlaybackPosition(float currentTime);

// レンダリング中のノート数を設定する関数
void setCurrentRenderingNotes(uint32_t count);

// レンダリング中のノート数を取得する関数
uint32_t getCurrentRenderingNotes(void);

#endif // FRAME_RENDERER_H
