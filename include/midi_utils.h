#ifndef MIDI_UTILS_H
#define MIDI_UTILS_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// MIDIファイルのヘッダー構造体
typedef struct {
    uint16_t format;       // フォーマット（0, 1, 2）
    uint16_t tracks;       // トラック数
    uint16_t division;     // 分解能（1拍あたりのティック数）
} MidiHeader;

// MIDIトラック構造体
typedef struct {
    uint8_t *data;         // トラックデータ
    uint32_t length;       // トラックの長さ
    uint32_t position;     // 現在の読み取り位置
    bool ended;            // トラックが終了したかどうか
} MidiTrack;

// イベントタイプの定義
typedef enum {
    EVENT_MARKER = 0,      // マーカーイベント (0x06)
    EVENT_TEXT = 1,        // テキストイベント (0x01)
    EVENT_LYRIC = 2,       // 歌詞イベント (0x05)
    EVENT_UNKNOWN = 3      // 不明なイベント
} TextEventType;

// テキストイベント構造体（マーカー、テキスト、歌詞）
typedef struct {
    uint32_t tick;         // イベント発生ティック
    TextEventType type;    // イベントタイプ
    char* text;            // テキスト内容（UTF-8に変換済み）
    float time;            // 発生時間（秒）
} TextEvent;

// テキストイベントリスト
typedef struct {
    TextEvent* events;     // テキストイベント配列
    uint32_t count;        // イベント数
    uint32_t capacity;     // 配列容量
} TextEventList;

// MIDIファイル構造体
typedef struct {
    MidiHeader header;     // MIDIヘッダー
    MidiTrack *tracks;     // トラック配列
    uint32_t totalTicks;   // 総ティック数
    uint32_t totalNotes;   // 総ノート数
    float durationSeconds; // 再生時間（秒）
    TextEventList textEvents; // テキストイベントリスト
} MidiFile;

// テンポ変更イベント
typedef struct {
    uint32_t tick;         // イベント発生ティック
    uint32_t tempo;        // テンポ（マイクロ秒/四分音符）
} TempoEvent;

// テンポマップ
typedef struct {
    TempoEvent *events;    // テンポイベント配列
    uint32_t count;        // イベント数
    uint32_t capacity;     // 配列容量
} TempoMap;

// テンポマップのグローバル変数（外部参照）
extern TempoMap tempoMap;

// ノートイベント
typedef struct {
    uint32_t tick;         // イベント発生ティック
    uint8_t type;          // イベントタイプ（ノートオン/オフ）
    uint8_t channel;       // MIDIチャンネル
    uint8_t note;          // ノート番号
    uint8_t velocity;      // ベロシティ
} NoteEvent;

// 時間ベースのノートイベント（描画用）
typedef struct {
    float time;            // 発生時間（秒）
    float duration;        // 音の長さ（秒）
    uint32_t tick;         // 発生ティック
    uint32_t tickDuration; // ティック単位の長さ
    uint8_t velocity;      // ベロシティ
    uint8_t channel;       // MIDIチャンネル
    uint8_t note;          // ノート番号
    uint8_t track;         // トラック番号
} NoteTimeEvent;

// NPS（Notes Per Second）計算用のエントリ
typedef struct {
    int notes;             // ノート数
    uint64_t timestamp;    // タイムスタンプ（ナノ秒）
} NPSEntry;

// 関数プロトタイプ
// MIDIファイル操作
MidiFile* loadMidiFile(const char* filename);
MidiFile* loadMidiFileHeaderAndTracks(const char* filename);
void processMidiFileTracks(MidiFile* midiFile);
void freeMidiFile(MidiFile* midiFile);
void analyzeMidiFile(MidiFile* midiFile);

// テンポマップ操作
void initTempoMap();
void addTempoChange(uint32_t tick, uint32_t tempo);
float tickToSeconds(uint32_t tick, uint16_t division);

// ノート解析
void scanTempoEvents(MidiFile* midiFile);
void countNotes(MidiFile* midiFile);
float calculateDuration(MidiFile* midiFile);

// 高速ノートイベント抽出（最適化版）
// 戻り値: 抽出されたノートイベントの数、エラー時は0
uint32_t extractNoteEvents(MidiFile* midiFile, NoteTimeEvent** outEvents, bool showProgress);

// ストリーム処理によるノートイベント抽出（新アルゴリズム）
// 戻り値: 抽出されたノートイベントの数、エラー時は0
uint32_t streamExtractNoteEvents(MidiFile* midiFile, NoteTimeEvent** outEvents, bool showProgress);

// 進捗表示用のコールバック関数型
typedef void (*ProgressCallback)(float progress, const char* message);

// テキストイベント操作
void initTextEventList(TextEventList* list);
void addTextEvent(TextEventList* list, uint32_t tick, TextEventType type, const char* text, float time);
void freeTextEventList(TextEventList* list);
void scanTextEvents(MidiFile* midiFile);
char* convertToUTF8(const char* input, size_t length);
TextEvent* getCurrentTextEvent(MidiFile* midiFile, float currentTime, TextEventType type);
bool isValidTextEvent(const char* text);

// ユーティリティ関数
uint32_t readVariableLengthValue(uint8_t *data, uint32_t *position, uint32_t maxLength);

// MIDIチャンネル状態管理
void initMidiChannelStates(void);
void updatePlaybackPosition(float currentTime);
void processMidiEventsAtTime(MidiFile* midiFile, float currentTime);
float getCurrentBPM(void);
int getCurrentPan(int channel);
int getCurrentPitchBend(int channel);
int getCurrentVolume(int channel);
int getCurrentSustain(int channel);
int getCurrentFilterCutoff(int channel);
int getCurrentFilterResonance(int channel);
int getCurrentReleaseTime(int channel);

#ifdef __cplusplus
}
#endif

#endif // MIDI_UTILS_H
