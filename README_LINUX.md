# HMP7 Video Renderer - Linux Support

This document provides comprehensive instructions for building and running HMP7 Video Renderer on Linux systems, including NixOS support.

## 🐧 Supported Linux Distributions

- **Ubuntu/Debian** (18.04+)
- **Fedora/RHEL/CentOS** (8+)
- **Arch Linux/Manjaro**
- **openSUSE/SLES**
- **NixOS** (with provided shell.nix)
- Other distributions (manual dependency installation required)

## 🚀 Quick Start

### Option 1: Automated Build Script

The easiest way to build on Linux is using the provided build script:

```bash
# Install dependencies and build in one command
./build-linux.sh --install-deps

# Or build without installing dependencies (if already installed)
./build-linux.sh

# Build in debug mode
./build-linux.sh --debug

# Build and run immediately
./build-linux.sh --run
```

### Option 2: NixOS Development Environment

If you're using NixOS or have Nix installed:

```bash
# Enter the development environment
nix-shell

# Build the project
xmake config --plat=linux --arch=x86_64
xmake build

# Run the project
xmake run
```

### Option 3: Manual Build

1. **Install Dependencies** (see distribution-specific instructions below)
2. **Install xmake**:
   ```bash
   curl -fsSL https://xmake.io/shget.text | bash
   export PATH="$HOME/.local/bin:$PATH"
   ```
3. **Configure and Build**:
   ```bash
   xmake config --plat=linux --arch=x86_64
   xmake build
   ```
4. **Run**:
   ```bash
   xmake run
   ```

## 📦 Dependencies

### Core Dependencies

- **Build Tools**: gcc/g++, cmake, ninja, pkg-config
- **Graphics Libraries**: 
  - Skia (2D graphics)
  - FFmpeg 7.0+ (video encoding with VA-API/VDPAU support)
  - OpenGL/EGL (GPU rendering)
  - Mesa (OpenGL implementation)
- **Image Libraries**: libjpeg, libpng, libwebp
- **Font Libraries**: freetype, fontconfig
- **X11 Libraries**: libX11, libXext, libXrender, etc.
- **Audio Libraries**: ALSA, PulseAudio
- **System Libraries**: pthread, dl, rt, m

### Development Tools (Optional)

- **Debugging**: gdb, valgrind
- **File Dialogs**: zenity (GNOME) or kdialog (KDE)

## 🔧 Distribution-Specific Installation

### Ubuntu/Debian

```bash
sudo apt update
sudo apt install -y \
    build-essential cmake ninja-build pkg-config \
    libskia-dev libffmpeg-dev libfreetype6-dev libfontconfig1-dev \
    libjpeg-dev libpng-dev libwebp-dev \
    libgl1-mesa-dev libegl1-mesa-dev \
    libx11-dev libxext-dev libxrender-dev libxrandr-dev \
    libxi-dev libxcursor-dev libxinerama-dev libxxf86vm-dev \
    libasound2-dev libpulse-dev zenity gdb valgrind
```

### Fedora/RHEL/CentOS

```bash
sudo dnf install -y \
    gcc gcc-c++ cmake ninja-build pkgconfig \
    skia-devel ffmpeg-devel freetype-devel fontconfig-devel \
    libjpeg-turbo-devel libpng-devel libwebp-devel \
    mesa-libGL-devel mesa-libEGL-devel \
    libX11-devel libXext-devel libXrender-devel libXrandr-devel \
    libXi-devel libXcursor-devel libXinerama-devel libXxf86vm-devel \
    alsa-lib-devel pulseaudio-libs-devel zenity gdb valgrind
```

### Arch Linux/Manjaro

```bash
sudo pacman -S --needed \
    base-devel cmake ninja pkgconf \
    skia ffmpeg freetype2 fontconfig \
    libjpeg-turbo libpng libwebp \
    mesa libx11 libxext libxrender libxrandr \
    libxi libxcursor libxinerama libxxf86vm \
    alsa-lib libpulse zenity gdb valgrind
```

### openSUSE

```bash
sudo zypper install -y \
    gcc gcc-c++ cmake ninja pkg-config \
    libskia-devel ffmpeg-devel freetype2-devel fontconfig-devel \
    libjpeg8-devel libpng16-devel libwebp-devel \
    Mesa-libGL-devel Mesa-libEGL-devel \
    libX11-devel libXext-devel libXrender-devel libXrandr-devel \
    libXi-devel libXcursor-devel libXinerama-devel libXxf86vm-devel \
    alsa-devel libpulse-devel zenity gdb valgrind
```

## 🎮 GPU Acceleration

### NVIDIA GPUs

The project supports NVIDIA GPU acceleration through:
- **NVENC** (hardware video encoding)
- **CUDA** (if available)

Ensure you have the NVIDIA drivers and development libraries installed:

```bash
# Ubuntu/Debian
sudo apt install nvidia-driver-dev nvidia-cuda-toolkit

# Fedora
sudo dnf install nvidia-driver-devel cuda-toolkit

# Arch
sudo pacman -S nvidia nvidia-utils cuda
```

### AMD GPUs

AMD GPU acceleration is supported through:
- **VA-API** (Video Acceleration API)
- **VDPAU** (Video Decode and Presentation API)

Install Mesa drivers and VA-API:

```bash
# Ubuntu/Debian
sudo apt install mesa-va-drivers libva-dev libvdpau-dev

# Fedora
sudo dnf install mesa-va-drivers libva-devel libvdpau-devel

# Arch
sudo pacman -S mesa-vdpau libva-mesa-driver
```

### Intel GPUs

Intel integrated graphics acceleration:

```bash
# Ubuntu/Debian
sudo apt install intel-media-va-driver libva-dev

# Fedora
sudo dnf install intel-media-driver libva-devel

# Arch
sudo pacman -S intel-media-driver
```

## 🔍 Troubleshooting

### Common Issues

1. **Skia not found**:
   - Install libskia-dev or skia-devel package
   - Or build Skia from source and set include/library paths

2. **FFmpeg version issues**:
   - Ensure FFmpeg 7.0+ is installed
   - Check with: `ffmpeg -version`

3. **OpenGL/EGL errors**:
   - Install Mesa development packages
   - Ensure graphics drivers are properly installed
   - For headless systems, ensure EGL is available

4. **X11 errors in headless mode**:
   - Install Xvfb for virtual display: `sudo apt install xvfb`
   - Run with: `xvfb-run -a ./your_program`

5. **Permission errors**:
   - Ensure user is in video/audio groups: `sudo usermod -a -G video,audio $USER`

### Debug Build

For debugging issues, build in debug mode:

```bash
xmake config --mode=debug
xmake build
gdb build/linux/x86_64/debug/counter_animation
```

### Memory Debugging

Use Valgrind for memory leak detection:

```bash
xmake config --mode=debug
xmake build
valgrind --leak-check=full --show-leak-kinds=all build/linux/x86_64/debug/counter_animation
```

## 🌟 Features

### Linux-Specific Features

- **Headless GPU rendering** using EGL
- **Hardware video encoding** (NVENC, VA-API, VDPAU)
- **Native file dialogs** using zenity/kdialog
- **Multi-threading** with OpenMP support
- **Memory debugging** with AddressSanitizer support

### Performance Optimizations

- **GPU batch rendering** for improved performance
- **Multi-core processing** for MIDI processing
- **Hardware-accelerated video encoding**
- **Optimized memory management**

## 📝 Configuration

The project uses the same configuration files as Windows:
- `config.toml` - Main configuration
- `config.schema.json` - Configuration schema

See `README_CONFIG.md` for detailed configuration options.

## 🤝 Contributing

When contributing Linux-specific code:

1. Test on multiple distributions
2. Use portable APIs when possible
3. Add appropriate preprocessor guards (`#ifdef LINUX_PLATFORM`)
4. Update this documentation

## 📄 License

Same license as the main project. See LICENSE file for details.

---

For more information, see the main project documentation or open an issue on the project repository.
