#include "../../include/utils/config_manager.h"
#include "../../include/utils/color.h"
#include <iostream>

// 設定ファイルを再読み込みする関数
void reloadConfiguration() {
    // ConfigManagerのインスタンスを取得
    ConfigManager* configManager = ConfigManager::getInstance();
    
    // 設定ファイルを再読み込み
    if (configManager->loadConfig()) {
        std::cout << "設定ファイルを再読み込みしました。" << std::endl;
        
        // 色設定を再適用
        const ColorConfig& colors = configManager->getColorConfig();
        
        // チャンネルカラーを更新
        for (int i = 0; i < 16; i++) {
            channel_colors[i] = colors.channelColors[i];
        }
        
        std::cout << "色設定を更新しました:" << std::endl;
        std::cout << "背景色: " << ConfigManager::colorToHexString(colors.backgroundColor) << std::endl;
        std::cout << "グリッド線の色: " << ConfigManager::colorToHexString(colors.gridLineColor) << std::endl;
        std::cout << "テキスト色: " << ConfigManager::colorToHexString(colors.textColor) << std::endl;
        std::cout << "ボーダー色: " << ConfigManager::colorToHexString(colors.borderColor) << std::endl;
        std::cout << "グラフ背景色: " << ConfigManager::colorToHexString(colors.graphBackgroundColor) << std::endl;
    } else {
        std::cout << "設定ファイルの読み込みに失敗しました。" << std::endl;
    }
}
