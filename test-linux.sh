#!/bin/bash

# HMP7 Video Renderer - Linux Test Script
# This script tests the Linux build and verifies functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test if required tools are available
test_tools() {
    print_status "Testing required tools..."
    
    local tools=("gcc" "g++" "pkg-config" "cmake")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_success "$tool is available"
        else
            print_error "$tool is missing"
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "Missing tools: ${missing_tools[*]}"
        return 1
    fi
    
    return 0
}

# Test if required libraries are available
test_libraries() {
    print_status "Testing required libraries..."
    
    local libs=("gl" "egl" "x11" "freetype2" "fontconfig")
    local missing_libs=()
    
    for lib in "${libs[@]}"; do
        if pkg-config --exists "$lib" 2>/dev/null; then
            print_success "lib$lib is available"
        else
            print_warning "lib$lib might be missing or not in pkg-config"
            # Don't fail for libraries that might have different names
        fi
    done
    
    return 0
}

# Test xmake installation
test_xmake() {
    print_status "Testing xmake..."
    
    if command -v xmake &> /dev/null; then
        local version=$(xmake --version | head -n1)
        print_success "xmake is available: $version"
        return 0
    else
        print_error "xmake is not available"
        return 1
    fi
}

# Test project configuration
test_configuration() {
    print_status "Testing project configuration..."
    
    if xmake config --plat=linux --arch=x86_64 &> /dev/null; then
        print_success "Project configuration successful"
        return 0
    else
        print_error "Project configuration failed"
        return 1
    fi
}

# Test project build
test_build() {
    print_status "Testing project build..."
    
    # Clean previous build
    xmake clean &> /dev/null || true
    
    # Try to build
    if xmake build 2>&1 | tee build.log; then
        print_success "Project build successful"
        return 0
    else
        print_error "Project build failed"
        echo "Build log:"
        cat build.log
        return 1
    fi
}

# Test GPU support
test_gpu() {
    print_status "Testing GPU support..."
    
    # Check for OpenGL
    if command -v glxinfo &> /dev/null; then
        local gl_version=$(glxinfo | grep "OpenGL version" | head -n1)
        print_success "OpenGL: $gl_version"
    else
        print_warning "glxinfo not available (install mesa-utils)"
    fi
    
    # Check for EGL
    if [ -f /usr/lib/x86_64-linux-gnu/libEGL.so ] || [ -f /usr/lib64/libEGL.so ] || [ -f /usr/lib/libEGL.so ]; then
        print_success "EGL library found"
    else
        print_warning "EGL library not found in standard locations"
    fi
    
    return 0
}

# Test runtime dependencies
test_runtime() {
    print_status "Testing runtime dependencies..."
    
    # Check if the binary was created
    local binary_path=$(find build -name "counter_animation" -type f 2>/dev/null | head -n1)
    
    if [ -n "$binary_path" ] && [ -f "$binary_path" ]; then
        print_success "Binary found: $binary_path"
        
        # Check binary dependencies
        if command -v ldd &> /dev/null; then
            print_status "Checking binary dependencies..."
            if ldd "$binary_path" | grep -q "not found"; then
                print_error "Missing runtime dependencies:"
                ldd "$binary_path" | grep "not found"
                return 1
            else
                print_success "All runtime dependencies satisfied"
            fi
        fi
        
        return 0
    else
        print_error "Binary not found"
        return 1
    fi
}

# Test file dialog support
test_file_dialog() {
    print_status "Testing file dialog support..."
    
    if command -v zenity &> /dev/null; then
        print_success "zenity (GNOME file dialogs) available"
    elif command -v kdialog &> /dev/null; then
        print_success "kdialog (KDE file dialogs) available"
    else
        print_warning "No file dialog tool found (install zenity or kdialog)"
    fi
    
    return 0
}

# Main test function
main() {
    echo "🧪 HMP7 Video Renderer - Linux Test Suite"
    echo "=========================================="
    echo ""
    
    local tests=(
        "test_tools"
        "test_libraries"
        "test_xmake"
        "test_configuration"
        "test_build"
        "test_gpu"
        "test_runtime"
        "test_file_dialog"
    )
    
    local passed=0
    local failed=0
    
    for test in "${tests[@]}"; do
        echo ""
        if $test; then
            ((passed++))
        else
            ((failed++))
        fi
    done
    
    echo ""
    echo "=========================================="
    echo "Test Results:"
    echo "  Passed: $passed"
    echo "  Failed: $failed"
    echo ""
    
    if [ $failed -eq 0 ]; then
        print_success "All tests passed! 🎉"
        echo ""
        echo "Your Linux environment is ready for HMP7 Video Renderer!"
        echo "You can now run: xmake run"
        return 0
    else
        print_error "Some tests failed. Please check the output above."
        echo ""
        echo "Common solutions:"
        echo "  - Install missing dependencies with: ./build-linux.sh --install-deps"
        echo "  - Check the README_LINUX.md for distribution-specific instructions"
        return 1
    fi
}

# Cleanup function
cleanup() {
    rm -f build.log
}

# Set up cleanup trap
trap cleanup EXIT

# Run main function
main "$@"
