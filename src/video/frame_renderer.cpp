#include <stdio.h>
#include <string.h>
#include <time.h>
#include <SkCanvas.h>
#include <SkSurface.h>
#include <SkImage.h>
#include <SkPaint.h>
#include <SkFont.h>
#include <SkTypeface.h>
#include <SkRect.h>
#include <SkRRect.h>
#include <SkColor.h>
#include "../../include/frame_renderer.h"
#include "../../include/font_manager.h"
#include "../../include/string_utils.h"
#include "../../include/graphs.h"
#include "../../include/ui/piano_keyboard.h"
#include "../../include/ui/piano_roll.h"
#include "../../include/performance.h"
#include "../../include/gpu_context.h"
#include "../../include/gpu_batch_renderer.h"
#include "../../include/utils/config_manager.h"

// 現在レンダリング中のノート数を追跡するグローバル変数
static uint32_t currentRenderingNotes = 0;

// レンダリング中のノート数を設定する関数
void setCurrentRenderingNotes(uint32_t count) {
    currentRenderingNotes = count;
}

// レンダリング中のノート数を取得する関数
uint32_t getCurrentRenderingNotes(void) {
    return currentRenderingNotes;
}

// 色変換ヘルパー関数：RGB整数値をSkColorに変換する
SkColor rgbToSkColor(uint32_t rgbColor, uint8_t alpha = 255) {
    uint32_t r = (rgbColor >> 16) & 0xFF;
    uint32_t g = (rgbColor >> 8) & 0xFF;
    uint32_t b = rgbColor & 0xFF;
    return SkColorSetARGB(alpha, r, g, b);
}

// レンダリングステータスを描画する関数
void drawRenderingStatus(SkCanvas* canvas, float currentTime) {
    // ConfigManagerからvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    // デバッグ情報の表示が無効な場合は何も描画しない
    if (!visualConfig.showDebug) return;

    // 画面サイズを取得
    int screenWidth = WIDTH;
    int screenHeight = HEIGHT;

    // ピアノキーボードの高さを考慮
    int keyboardHeight = PIANO_WHITE_KEY_HEIGHT;
    int keyboardY = screenHeight - keyboardHeight;

    // ピアノキーボードの上部に表示する位置を計算
    int statusY = keyboardY - 145; // ピアノキーボードの上部から十分に離す
    int baseX = 5; // 左側からの余白
    int lineHeight = 20; // 行間

    // フォントを設定（サイズ16px）
    SkFont font = setFont(FONT_DEFAULT, 16);
    SkPaint textPaint;

    // ConfigManagerから色を取得（既に取得済みのconfigManagerを使用）
    const ColorConfig& colorConfig = configManager->getColorConfig();

    // カウンター表示用テキスト色を使用
    textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor));
    textPaint.setAntiAlias(true);

    // リアル時間を表示 (Real Time: 0000/00/00 00:00:00)
    time_t now = time(NULL);
    struct tm* localTime = localtime(&now);
    char realTimeStr[64];
    strftime(realTimeStr, sizeof(realTimeStr), "Real Time: %Y/%m/%d %H:%M:%S", localTime);
    canvas->drawString(realTimeStr, baseX, statusY, font, textPaint);

    // 経過時間を計算（日、時間、分、秒に変換）
    double totalSeconds = perfMetrics.totalElapsedSeconds;
    int days = (int)(totalSeconds / 86400); // 1日 = 86400秒
    int hours = (int)((totalSeconds - days * 86400) / 3600);
    int minutes = (int)((totalSeconds - days * 86400 - hours * 3600) / 60);
    int seconds = (int)(totalSeconds - days * 86400 - hours * 3600 - minutes * 60);

    // 経過時間を表示（Elapsed:0d/12:34:56形式）
    char elapsedTimeStr[64];
    snprintf(elapsedTimeStr, sizeof(elapsedTimeStr), "Elapsed: %dd/%02d:%02d:%02d", days, hours, minutes, seconds);
    canvas->drawString(elapsedTimeStr, baseX, statusY + lineHeight, font, textPaint);

    // CPU使用率を表示
    char cpuStr[32];
    snprintf(cpuStr, sizeof(cpuStr), "CPU: %.1f%%", perfMetrics.cpuUsage);
    canvas->drawString(cpuStr, baseX, statusY + lineHeight * 2, font, textPaint);

    // フレーム時間を表示
    char frameTimeStr[32];
    snprintf(frameTimeStr, sizeof(frameTimeStr), "Frame Time: %.1f ms", perfMetrics.frameElapsedSeconds * 1000.0f);
    canvas->drawString(frameTimeStr, baseX, statusY + lineHeight * 3, font, textPaint);

    // FPSを表示
    char fpsStr[32];
    snprintf(fpsStr, sizeof(fpsStr), "FPS: %.1f", perfMetrics.currentFps);
    canvas->drawString(fpsStr, baseX, statusY + lineHeight * 4, font, textPaint);

    // 残り時間を計算
    float currentTimeRatio = currentTime / appState.videoDuration;
    double remainingTime = 0.0;
    if (currentTimeRatio > 0.0f) {
        double elapsedTime = perfMetrics.totalElapsedSeconds;
        double estimatedTotalTime = elapsedTime / currentTimeRatio;
        remainingTime = estimatedTotalTime - elapsedTime;
    }

    // 残り時間を表示
    char etaStr[32];
    snprintf(etaStr, sizeof(etaStr), "ETA: %.1f min", remainingTime / 60.0);
    canvas->drawString(etaStr, baseX, statusY + lineHeight * 5, font, textPaint);

    // レンダリング中のノート数を表示
    char renderingNotesStr[32];
    snprintf(renderingNotesStr, sizeof(renderingNotesStr), "Rendering Notes: %u", currentRenderingNotes);
    canvas->drawString(renderingNotesStr, baseX, statusY + lineHeight * 6, font, textPaint);

    // レンダリング方式の情報を表示
#ifdef GPU_RENDERING_ENABLED
    if (isGPUAvailable()) {
        // バッチレンダラーの状態もチェック
        extern GPUBatchRenderer batchRenderer;
        if (batchRenderer.initialized) {
            canvas->drawString("GPU Batch Rendering", baseX, statusY + lineHeight * 7, font, textPaint);
        } else {
            canvas->drawString("GPU Rendering (Skia)", baseX, statusY + lineHeight * 7, font, textPaint);
        }
    } else {
        canvas->drawString("CPU Rendering (Fallback)", baseX, statusY + lineHeight * 7, font, textPaint);
    }
#else
    canvas->drawString("CPU Rendering", baseX, statusY + lineHeight * 7, font, textPaint);
#endif
}

// フレームをレンダリングする関数
sk_sp<SkImage> renderFrame(float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                          uint32_t currentNps, uint16_t currentPolyphony) {
    // 再生位置を更新（MIDIイベントを処理）
    updatePlaybackPosition(currentTime);

    // GPUレンダリング用のサーフェスとキャンバスを作成
    sk_sp<SkSurface> surface = nullptr;
    bool usingGPU = false;

#ifdef GPU_RENDERING_ENABLED
    if (isGPUAvailable()) {
        surface = createGPUSurface(WIDTH, HEIGHT);
        if (surface) {
            usingGPU = true;
            // フレーム0でのみGPU使用を報告
            static bool firstGPUFrame = true;
            if (firstGPUFrame) {
                printf("✓ Using GPU surface for rendering\n");
                firstGPUFrame = false;
            }
        } else {
            fprintf(stderr, "Warning: Failed to create GPU surface, falling back to CPU\n");
        }
    }
#endif

    // GPUサーフェスの作成に失敗した場合はCPUレンダリングにフォールバック
    if (!surface) {
        surface = SkSurface::MakeRasterN32Premul(WIDTH, HEIGHT);
        // フレーム0でのみCPU使用を報告
        static bool firstCPUFrame = true;
        if (firstCPUFrame) {
            printf("Using CPU surface for rendering\n");
            firstCPUFrame = false;
        }
    }

    SkCanvas* canvas = surface->getCanvas();

    // 背景を透明に設定
    canvas->clear(SK_ColorTRANSPARENT);

    // レンダリング中のノート数をリセット
    setCurrentRenderingNotes(0);

    // ConfigManagerからvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    // マスタートグルチェック - すべてのビジュアル要素を非表示にする場合
    if (!visualConfig.showAll) {
        // 空のイメージを返す（透明な背景のみ）
        return surface->makeImageSnapshot();
    }

    // ピアノロールを最初に描画（背景）
    if (visualConfig.showPianoroll) {
        updatePianoRoll(currentTime);
        drawPianoRoll(canvas, currentTime);
    }

    // 統計情報を描画（カウンター設定に基づく）
    if (visualConfig.showCounters) {
        drawStatistics(canvas, currentTime, currentNotes, totalNotes, currentNps, appState.maxNps,
                      currentPolyphony, appState.maxPolyphony);
    }

    // テキストイベントを描画（テキストイベント設定に基づく）
    if (visualConfig.showTextEvents) {
        drawTextEvents(canvas, appState.midiFile, currentTime);
    }

    // グラフを更新して描画（グラフ設定に基づく）
    if (visualConfig.showGraphs) {
        updateGraphs(currentTime, currentNotes, totalNotes, currentNps, currentPolyphony);
#ifdef GPU_RENDERING_ENABLED
        extern GPUBatchRenderer batchRenderer;
        if (usingGPU && batchRenderer.initialized) {
            // GPU バッチレンダリングを使用（背景のみ、線とテキストは既存の関数）
            drawGraphsBatched(canvas);
        } else {
            drawGraphs(canvas);
        }
#else
        drawGraphs(canvas);
#endif
    }

    // ピアノキーボードを更新して描画
    if (visualConfig.showPiano) {
        updatePianoKeyboard(currentTime);
        drawPianoKeyboard(canvas);
    }

    // レンダリングステータスを描画
    drawRenderingStatus(canvas, currentTime);

    // サーフェスからイメージを作成
    sk_sp<SkImage> resultImage = surface->makeImageSnapshot();

    // GPUコンテキストをフラッシュ（GPU使用時のみ）
#ifdef GPU_RENDERING_ENABLED
    if (usingGPU && isGPUAvailable()) {
        flushGPUContext();

        // GPUイメージの場合、CPUアクセス可能なイメージに変換
        if (resultImage && resultImage->isTextureBacked()) {
            static bool firstConversion = true;
            if (firstConversion) {
                printf("Converting GPU image to CPU-accessible format\n");
                firstConversion = false;
            }

            SkImageInfo info = SkImageInfo::MakeN32Premul(WIDTH, HEIGHT);
            sk_sp<SkSurface> cpuSurface = SkSurface::MakeRaster(info);
            if (cpuSurface) {
                SkCanvas* cpuCanvas = cpuSurface->getCanvas();
                cpuCanvas->drawImage(resultImage, 0, 0);
                resultImage = cpuSurface->makeImageSnapshot();
            } else {
                fprintf(stderr, "Warning: Failed to create CPU surface for GPU image conversion\n");
            }
        }
    }
#endif

    // 最終的なイメージのチェック
    if (!resultImage) {
        fprintf(stderr, "Error: Failed to create image from surface\n");
    }

    return resultImage;
}

// 時間を分:秒形式に変換するヘルパー関数
static void formatTimeDisplay(float seconds, char* buffer, size_t bufferSize) {
    int minutes = (int)(seconds / 60);
    int secs = (int)(seconds - minutes * 60);

    snprintf(buffer, bufferSize, "%02d:%02d", minutes, secs);
}

// 統計情報を描画する関数
void drawStatistics(SkCanvas* canvas, float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                   uint32_t currentNps, uint32_t maxNps,
                   uint16_t currentPolyphony, uint16_t maxPolyphony) {
    SkFont font = setFont(FONT_DEFAULT, 36);
    SkPaint textPaint;

    // ConfigManagerから色とvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    // カウンター表示用テキスト色を使用
    textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor));
    textPaint.setAntiAlias(true);

    char formattedCurrentNotes[32], formattedTotalNotes[32];
    char formattedCurrentNps[32], formattedMaxNps[32];
    char formattedCurrentPoly[32], formattedMaxPoly[32];

    formatNumberWithCommas(formattedCurrentNotes, sizeof(formattedCurrentNotes), currentNotes);
    formatNumberWithCommas(formattedTotalNotes, sizeof(formattedTotalNotes), totalNotes);
    formatNumberWithCommas(formattedCurrentNps, sizeof(formattedCurrentNps), currentNps);
    formatNumberWithCommas(formattedMaxNps, sizeof(formattedMaxNps), maxNps);
    formatNumberWithCommas(formattedCurrentPoly, sizeof(formattedCurrentPoly), currentPolyphony);
    formatNumberWithCommas(formattedMaxPoly, sizeof(formattedMaxPoly), maxPolyphony);

    // 時間情報を計算
    float totalTime = appState.videoDuration;
    float remainingTime = totalTime - currentTime;
    if (remainingTime < 0) remainingTime = 0;

    char currentTimeStr[16], totalTimeStr[16], remainingTimeStr[16];
    formatTimeDisplay(currentTime, currentTimeStr, sizeof(currentTimeStr));
    formatTimeDisplay(totalTime, totalTimeStr, sizeof(totalTimeStr));
    formatTimeDisplay(remainingTime, remainingTimeStr, sizeof(remainingTimeStr));

    int yPosition = 50;
    const int lineHeight = 50;

    // 時間表示（個別設定に基づく）
    if (visualConfig.counters.showTime) {
        char timeStr[128];
        sprintf(timeStr, "Time: %s - %s - %s", currentTimeStr, totalTimeStr, remainingTimeStr);
        canvas->drawString(timeStr, 20, yPosition, font, textPaint);
        yPosition += lineHeight;
    }

    // ノート数表示（個別設定に基づく）
    if (visualConfig.counters.showNotes) {
        char notesStr[64];
        sprintf(notesStr, "Notes: %s/%s", formattedCurrentNotes, formattedTotalNotes);
        canvas->drawString(notesStr, 20, yPosition, font, textPaint);
        yPosition += lineHeight;
    }

    // NPS表示（個別設定に基づく）
    if (visualConfig.counters.showNps) {
        char npsStr[64];
        sprintf(npsStr, "NPS: %s/%s", formattedCurrentNps, formattedMaxNps);
        canvas->drawString(npsStr, 20, yPosition, font, textPaint);
        yPosition += lineHeight;
    }

    // ポリフォニー表示（個別設定に基づく）
    if (visualConfig.counters.showPolyphony) {
        char polyStr[64];
        sprintf(polyStr, "Polyphony: %s/%s", formattedCurrentPoly, formattedMaxPoly);
        canvas->drawString(polyStr, 20, yPosition, font, textPaint);
        yPosition += lineHeight;
    }
}

// テキストイベントを描画する関数
void drawTextEvents(SkCanvas* canvas, MidiFile* midiFile, float currentTime) {
    // ConfigManagerから色とvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    // 左上に表示するための位置設定（より目立つ位置に調整）
    int baseX = 20; // 少し右に移動
    int baseY = 250; // より下方に移動（統計情報の下に十分なスペースを確保）
    int lineHeight = 50; // 行間を広げる
    int currentY = baseY; // 現在のY位置を追跡

    // マーカーイベント（設定に基づく表示制御）
    if (visualConfig.textEvents.showMarkers) {
        TextEvent* currentMarker = getCurrentTextEvent(midiFile, currentTime, EVENT_MARKER);
        if (currentMarker && currentMarker->text && *currentMarker->text) {
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;

            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);

            // テキストが確実に見えるようにアウトラインを追加
            SkPaint outlinePaint;
            outlinePaint.setStyle(SkPaint::kStroke_Style);
            outlinePaint.setStrokeWidth(2.0f);
            outlinePaint.setColor(SK_ColorBLACK);
            outlinePaint.setAntiAlias(true);

            // ラベルとテキストを表示
            char markerStr[512];
            snprintf(markerStr, sizeof(markerStr), "Marker: %s", currentMarker->text);

            // アウトラインを描画
            canvas->drawString(markerStr, baseX, currentY, font, outlinePaint);
            // テキストを描画
            canvas->drawString(markerStr, baseX, currentY, font, textPaint);
        } else {
            // テキストがない場合はラベルのみ表示
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;
            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);
            canvas->drawString("Marker: ", baseX, currentY, font, textPaint);
        }
        currentY += lineHeight; // 表示した場合のみ次の行に移動
    }

    // テキストイベント（設定に基づく表示制御）
    if (visualConfig.textEvents.showText) {
        TextEvent* currentText = getCurrentTextEvent(midiFile, currentTime, EVENT_TEXT);
        if (currentText && currentText->text && *currentText->text) {
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;
            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);

            // テキストが確実に見えるようにアウトラインを追加
            SkPaint outlinePaint;
            outlinePaint.setStyle(SkPaint::kStroke_Style);
            outlinePaint.setStrokeWidth(2.0f);
            outlinePaint.setColor(SK_ColorBLACK);
            outlinePaint.setAntiAlias(true);

            // ラベルとテキストを表示
            char textStr[512];
            snprintf(textStr, sizeof(textStr), "Text: %s", currentText->text);

            // アウトラインを描画
            canvas->drawString(textStr, baseX, currentY, font, outlinePaint);
            // テキストを描画
            canvas->drawString(textStr, baseX, currentY, font, textPaint);
        } else {
            // テキストがない場合はラベルのみ表示
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;
            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);
            canvas->drawString("Text: ", baseX, currentY, font, textPaint);
        }
        currentY += lineHeight; // 表示した場合のみ次の行に移動
    }

    // 歌詞イベント（設定に基づく表示制御）
    if (visualConfig.textEvents.showLyrics) {
        TextEvent* currentLyric = getCurrentTextEvent(midiFile, currentTime, EVENT_LYRIC);
        if (currentLyric && currentLyric->text && *currentLyric->text) {
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;
            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);

            // テキストが確実に見えるようにアウトラインを追加
            SkPaint outlinePaint;
            outlinePaint.setStyle(SkPaint::kStroke_Style);
            outlinePaint.setStrokeWidth(2.0f);
            outlinePaint.setColor(SK_ColorBLACK);
            outlinePaint.setAntiAlias(true);

            // ラベルとテキストを表示
            char lyricStr[512];
            snprintf(lyricStr, sizeof(lyricStr), "Lyrics: %s", currentLyric->text);

            // アウトラインを描画
            canvas->drawString(lyricStr, baseX, currentY, font, outlinePaint);
            // テキストを描画
            canvas->drawString(lyricStr, baseX, currentY, font, textPaint);
        } else {
            // テキストがない場合はラベルのみ表示
            SkFont font = setFont(FONT_JAPANESE, 36);
            SkPaint textPaint;
            textPaint.setColor(rgbToSkColor(colorConfig.counter_textColor)); // カウンターテキストカラーを使用
            textPaint.setAntiAlias(true);
            canvas->drawString("Lyrics: ", baseX, currentY, font, textPaint);
        }
        currentY += lineHeight; // 表示した場合のみ次の行に移動
    }
}



