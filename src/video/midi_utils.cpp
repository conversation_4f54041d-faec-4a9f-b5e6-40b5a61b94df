#include "../../include/graphs.h"
#include "../../include/midi_utils.h"
#include "../../include/app_state.h"
#include "../../include/ui/piano_keyboard.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <math.h>

// 外部関数の宣言
extern float tickToSeconds(uint32_t tick, uint16_t division);

// MIDIチャンネルの状態を保持する構造体
typedef struct {
    int pan;            // パン値 (0-127, 64が中央)
    int pitchBend;      // ピッチベンド値 (-8192〜8191, 0が中央)
    int program;        // プログラム番号 (0-127)
    int volume;         // ボリューム (0-127) - CC#7
    int expression;     // エクスプレッション (0-127)
    int modulation;     // モジュレーション (0-127)
    int sustain;        // サステイン (0-127) - CC#64
    int reverb;         // リバーブ (0-127)
    int chorus;         // コーラス (0-127)
    int filterCutoff;   // フィルターカットオフ (0-127) - CC#74
    int filterResonance; // フィルターレゾナンス (0-127) - CC#71
    int releaseTime;    // リリースタイム (0-127) - CC#72
} MidiChannelState;

// 全チャンネルの状態
static MidiChannelState channelStates[16];

// 現在の再生位置（秒）
static float currentPlaybackTime = 0.0f;
// 初期化フラグ
static bool initialized = false;

// MIDIチャンネル状態を初期化する関数
void initMidiChannelStates() {
    for (int i = 0; i < 16; i++) {
        channelStates[i].pan = 64;        // 中央
        channelStates[i].pitchBend = 0;   // 中央
        channelStates[i].program = 0;     // プログラム0
        channelStates[i].volume = 100;    // ボリューム100
        channelStates[i].expression = 127; // エクスプレッション最大
        channelStates[i].modulation = 0;  // モジュレーション0
        channelStates[i].sustain = 0;     // サステイン0
        channelStates[i].reverb = 40;     // リバーブ40
        channelStates[i].chorus = 0;      // コーラス0
        channelStates[i].filterCutoff = 127;   // フィルターカットオフ最大
        channelStates[i].filterResonance = 0;  // フィルターレゾナンス0
        channelStates[i].releaseTime = 64;     // リリースタイム中央
    }
}

// 再生位置を更新する関数
void updatePlaybackPosition(float currentTime) {
    // 初期化されていない場合は初期化
    if (!initialized) {
        initMidiChannelStates();
        initPianoKeyboard(); // ピアノキーボードも初期化
        initialized = true;

        // 初期化時は前回の再生位置を0に設定
        currentPlaybackTime = 0.0f;
    }

    // 前回の位置より戻った場合は、チャンネル状態をリセット
    if (currentTime < currentPlaybackTime) {
        initMidiChannelStates();
        initPianoKeyboard(); // ピアノキーボードもリセット

        // リセット時は前回の再生位置を0に設定
        float prevTime = currentPlaybackTime;
        currentPlaybackTime = 0.0f;

        // デバッグ出力
        // printf("Playback position reset: %.2f -> %.2f\n", prevTime, currentTime);
    }

    // 前回の再生位置と同じ場合は何もしない（最適化）
    if (currentTime == currentPlaybackTime) {
        return;
    }

    // MIDIファイルが読み込まれていない場合は何もしない
    if (!appState.midiFile || !appState.midiFile->tracks) {
        currentPlaybackTime = currentTime; // 再生位置は更新する
        return;
    }

    // 前回の再生位置から現在の再生位置までのイベントのみを処理
    float prevTime = currentPlaybackTime;

    // 現在の再生位置を更新
    currentPlaybackTime = currentTime;

    // 各トラックを処理
    for (int i = 0; i < appState.midiFile->header.tracks; i++) {
        MidiTrack* track = &appState.midiFile->tracks[i];
        if (track->ended) continue;

        // トラックの先頭に戻る
        uint32_t position = 0;
        uint32_t tick = 0;
        uint8_t runningStatus = 0;
        float eventTime = 0.0f;

        // トラックをスキャンして現在の時間までのイベントを処理
        while (position < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (position >= track->length) break;
                byte = track->data[position++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            tick += deltaTime;

            // ティックを時間（秒）に変換
            eventTime = tickToSeconds(tick, appState.midiFile->header.division);

            // 現在の再生位置より後のイベントは処理しない
            if (eventTime > currentTime) {
                break;
            }

            // ステータスバイト処理
            uint8_t statusByte;
            if (position >= track->length) break;

            if (track->data[position] & 0x80) {
                statusByte = track->data[position++];
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // コントロールチェンジ (0xB0)
            if ((statusByte & 0xF0) == 0xB0) {
                if (position + 1 < track->length) {
                    uint8_t controller = track->data[position++];
                    uint8_t value = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    // パン (CC#10)
                    if (controller == 10) {
                        channelStates[channel].pan = value;
                    }
                    // ボリューム (CC#7)
                    else if (controller == 7) {
                        channelStates[channel].volume = value;
                    }
                    // エクスプレッション (CC#11)
                    else if (controller == 11) {
                        channelStates[channel].expression = value;
                    }
                    // モジュレーション (CC#1)
                    else if (controller == 1) {
                        channelStates[channel].modulation = value;
                    }
                    // サステイン (CC#64)
                    else if (controller == 64) {
                        channelStates[channel].sustain = value;
                    }
                    // リバーブ (CC#91)
                    else if (controller == 91) {
                        channelStates[channel].reverb = value;
                    }
                    // コーラス (CC#93)
                    else if (controller == 93) {
                        channelStates[channel].chorus = value;
                    }
                    // フィルターカットオフ (CC#74)
                    else if (controller == 74) {
                        channelStates[channel].filterCutoff = value;
                    }
                    // フィルターレゾナンス (CC#71)
                    else if (controller == 71) {
                        channelStates[channel].filterResonance = value;
                    }
                    // リリースタイム (CC#72)
                    else if (controller == 72) {
                        channelStates[channel].releaseTime = value;
                    }
                }
            }
            // ピッチベンド (0xE0)
            else if ((statusByte & 0xF0) == 0xE0) {
                if (position + 1 < track->length) {
                    uint8_t lsb = track->data[position++];
                    uint8_t msb = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    // ピッチベンド値を計算 (0-16383, 中央は8192)
                    int value = ((msb << 7) | lsb) - 8192;
                    channelStates[channel].pitchBend = value;
                }
            }
            // プログラムチェンジ (0xC0)
            else if ((statusByte & 0xF0) == 0xC0) {
                if (position < track->length) {
                    uint8_t program = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;
                    channelStates[channel].program = program;
                }
            }
            // ノートオンイベント (0x90)
            else if ((statusByte & 0xF0) == 0x90) {
                if (position + 1 < track->length) {
                    uint8_t note = track->data[position++];
                    uint8_t velocity = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    if (velocity > 0) {
                        // ノートオンイベントをピアノキーボードに通知
                        handleNoteOn(note, velocity, channel);
                    } else {
                        // ベロシティ0のノートオンはノートオフとして扱う
                        handleNoteOff(note, channel);
                    }
                }
            }
            // ノートオフイベント (0x80)
            else if ((statusByte & 0xF0) == 0x80) {
                if (position + 1 < track->length) {
                    uint8_t note = track->data[position++];
                    uint8_t velocity = track->data[position++]; // velocityは使わないが読み飛ばす
                    uint8_t channel = statusByte & 0x0F;

                    // ノートオフイベントをピアノキーボードに通知
                    handleNoteOff(note, channel);
                }
            }
            // その他のイベント
            else if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xD0) { // チャンネルプレッシャー (1バイト)
                    if (position < track->length) position++;
                } else if ((statusByte & 0xF0) != 0xC0) { // プログラムチェンジ以外の2バイトメッセージ
                    if (position + 1 < track->length) position += 2;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (position >= track->length) break;
                uint8_t metaType = track->data[position++];

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                // メタイベントのデータをスキップ
                if (position + length <= track->length) {
                    position += length;
                } else {
                    break;
                }
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                // SysExデータをスキップ
                if (position + length <= track->length) {
                    position += length;
                } else {
                    break;
                }
            }
        }
    }
}

// 現在のBPM値を取得する関数
float getCurrentBPM(void) {
    // MIDIファイルが読み込まれていない場合はデフォルト値を返す
    if (!appState.midiFile) {
        return 120.0f;
    }

    // テンポマップが空の場合はデフォルト値を返す
    if (tempoMap.count == 0) {
        return 120.0f; // デフォルトBPM
    }

    // 現在の再生位置に最も近いテンポイベントを探す
    uint32_t tempo = 500000; // デフォルトテンポ (500,000マイクロ秒/四分音符 = 120 BPM)

    // テンポマップをスキャンして現在の再生位置より前の最新のテンポイベントを見つける
    for (uint32_t i = 0; i < tempoMap.count; i++) {
        // テンポイベントの時間を計算
        float eventTime = tickToSeconds(tempoMap.events[i].tick, appState.midiFile->header.division);

        // 現在の再生位置より後のテンポイベントは処理しない
        if (eventTime > currentPlaybackTime) {
            break;
        }

        // 現在の再生位置より前の最新のテンポイベントを使用
        tempo = tempoMap.events[i].tempo;
    }

    // テンポをBPMに変換
    float bpm = 60000000.0f / (float)tempo;
    return bpm;
}

// 現在のパン値を取得する関数
// チャンネル番号を引数として受け取り、そのチャンネルのパン値を返す
int getCurrentPan(int channel) {
    // チャンネル番号の範囲チェック
    if (channel < 0 || channel >= 16) {
        return 64; // デフォルト値（中央）
    }

    // MIDIファイルが読み込まれていない場合はデフォルト値を返す
    if (!appState.midiFile) {
        return 64; // デフォルト値（中央）
    }

    // チャンネル状態からパン値を取得
    return channelStates[channel].pan;
}

// 現在のピッチベンド値を取得する関数
// チャンネル番号を引数として受け取り、そのチャンネルのピッチベンド値を返す
int getCurrentPitchBend(int channel) {
    // チャンネル番号の範囲チェック
    if (channel < 0 || channel >= 16) {
        return 0; // デフォルト値（中央）
    }

    // MIDIファイルが読み込まれていない場合はデフォルト値を返す
    if (!appState.midiFile) {
        return 0; // デフォルト値（中央）
    }

    // チャンネル状態からピッチベンド値を取得
    return channelStates[channel].pitchBend;
}

// 現在のボリューム値を取得する関数 (CC#7)
int getCurrentVolume(int channel) {
    if (channel < 0 || channel >= 16) {
        return 100; // デフォルト値
    }
    if (!appState.midiFile) {
        return 100; // デフォルト値
    }
    return channelStates[channel].volume;
}

// 現在のサステイン値を取得する関数 (CC#64)
int getCurrentSustain(int channel) {
    if (channel < 0 || channel >= 16) {
        return 0; // デフォルト値
    }
    if (!appState.midiFile) {
        return 0; // デフォルト値
    }
    return channelStates[channel].sustain;
}

// 現在のフィルターカットオフ値を取得する関数 (CC#74)
int getCurrentFilterCutoff(int channel) {
    if (channel < 0 || channel >= 16) {
        return 127; // デフォルト値（最大）
    }
    if (!appState.midiFile) {
        return 127; // デフォルト値（最大）
    }
    return channelStates[channel].filterCutoff;
}

// 現在のフィルターレゾナンス値を取得する関数 (CC#71)
int getCurrentFilterResonance(int channel) {
    if (channel < 0 || channel >= 16) {
        return 0; // デフォルト値
    }
    if (!appState.midiFile) {
        return 0; // デフォルト値
    }
    return channelStates[channel].filterResonance;
}

// 現在のリリースタイム値を取得する関数 (CC#72)
int getCurrentReleaseTime(int channel) {
    if (channel < 0 || channel >= 16) {
        return 64; // デフォルト値（中央）
    }
    if (!appState.midiFile) {
        return 64; // デフォルト値（中央）
    }
    return channelStates[channel].releaseTime;
}

// 指定された時間のMIDIイベントを処理する関数
void processMidiEventsAtTime(MidiFile* midiFile, float currentTime) {
    // MIDIファイルが無効な場合は何もしない
    if (!midiFile || !midiFile->tracks) {
        return;
    }

    // 初期化されていない場合は初期化
    if (!initialized) {
        initMidiChannelStates();
        initPianoKeyboard(); // ピアノキーボードも初期化
        initialized = true;
    }

    // 前回の位置より戻った場合は、チャンネル状態をリセット
    if (currentTime < currentPlaybackTime) {
        initMidiChannelStates();
        initPianoKeyboard(); // ピアノキーボードもリセット
    }

    // 現在の再生位置を更新
    currentPlaybackTime = currentTime;

    // 各トラックを処理
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        // トラックの先頭に戻る
        uint32_t position = 0;
        uint32_t tick = 0;
        uint8_t runningStatus = 0;
        float eventTime = 0.0f;

        // トラックをスキャンして現在の時間までのイベントを処理
        while (position < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (position >= track->length) break;
                byte = track->data[position++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            tick += deltaTime;

            // ティックを時間（秒）に変換
            eventTime = tickToSeconds(tick, midiFile->header.division);

            // 現在の再生位置より後のイベントは処理しない
            if (eventTime > currentTime) {
                break;
            }

            // ステータスバイト処理
            uint8_t statusByte;
            if (position >= track->length) break;

            if (track->data[position] & 0x80) {
                statusByte = track->data[position++];
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // コントロールチェンジ (0xB0)
            if ((statusByte & 0xF0) == 0xB0) {
                if (position + 1 < track->length) {
                    uint8_t controller = track->data[position++];
                    uint8_t value = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    // パン (CC#10)
                    if (controller == 10) {
                        channelStates[channel].pan = value;
                    }
                    // ボリューム (CC#7)
                    else if (controller == 7) {
                        channelStates[channel].volume = value;
                    }
                    // エクスプレッション (CC#11)
                    else if (controller == 11) {
                        channelStates[channel].expression = value;
                    }
                    // モジュレーション (CC#1)
                    else if (controller == 1) {
                        channelStates[channel].modulation = value;
                    }
                    // サステイン (CC#64)
                    else if (controller == 64) {
                        channelStates[channel].sustain = value;
                    }
                    // リバーブ (CC#91)
                    else if (controller == 91) {
                        channelStates[channel].reverb = value;
                    }
                    // コーラス (CC#93)
                    else if (controller == 93) {
                        channelStates[channel].chorus = value;
                    }
                    // フィルターカットオフ (CC#74)
                    else if (controller == 74) {
                        channelStates[channel].filterCutoff = value;
                    }
                    // フィルターレゾナンス (CC#71)
                    else if (controller == 71) {
                        channelStates[channel].filterResonance = value;
                    }
                    // リリースタイム (CC#72)
                    else if (controller == 72) {
                        channelStates[channel].releaseTime = value;
                    }
                }
            }
            // ピッチベンド (0xE0)
            else if ((statusByte & 0xF0) == 0xE0) {
                if (position + 1 < track->length) {
                    uint8_t lsb = track->data[position++];
                    uint8_t msb = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    // ピッチベンド値を計算 (0-16383, 中央は8192)
                    int value = ((msb << 7) | lsb) - 8192;
                    channelStates[channel].pitchBend = value;
                }
            }
            // プログラムチェンジ (0xC0)
            else if ((statusByte & 0xF0) == 0xC0) {
                if (position < track->length) {
                    uint8_t program = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;
                    channelStates[channel].program = program;
                }
            }
            // ノートオンイベント (0x90)
            else if ((statusByte & 0xF0) == 0x90) {
                if (position + 1 < track->length) {
                    uint8_t note = track->data[position++];
                    uint8_t velocity = track->data[position++];
                    uint8_t channel = statusByte & 0x0F;

                    if (velocity > 0) {
                        // ノートオンイベントをピアノキーボードに通知
                        handleNoteOn(note, velocity, channel);
                    } else {
                        // ベロシティ0のノートオンはノートオフとして扱う
                        handleNoteOff(note, channel);
                    }
                }
            }
            // ノートオフイベント (0x80)
            else if ((statusByte & 0xF0) == 0x80) {
                if (position + 1 < track->length) {
                    uint8_t note = track->data[position++];
                    uint8_t velocity = track->data[position++]; // velocityは使わないが読み飛ばす
                    uint8_t channel = statusByte & 0x0F;

                    // ノートオフイベントをピアノキーボードに通知
                    handleNoteOff(note, channel);
                }
            }
            // その他のイベント
            else if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xD0) { // チャンネルプレッシャー (1バイト)
                    if (position < track->length) position++;
                } else if ((statusByte & 0xF0) != 0xC0) { // プログラムチェンジ以外の2バイトメッセージ
                    if (position + 1 < track->length) position += 2;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (position >= track->length) break;
                uint8_t metaType = track->data[position++];

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                // メタイベントのデータをスキップ
                if (position + length <= track->length) {
                    position += length;
                } else {
                    break;
                }
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                // SysExデータをスキップ
                if (position + length <= track->length) {
                    position += length;
                } else {
                    break;
                }
            }
        }
    }
}
