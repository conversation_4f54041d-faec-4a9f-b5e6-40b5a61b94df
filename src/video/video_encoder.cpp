#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <inttypes.h> // PRIu64 マクロのために追加 (FFmpegヘッダでカバーされる場合もある)

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libavutil/channel_layout.h>
#include <libavutil/error.h>
#include <libavutil/samplefmt.h> // av_get_sample_fmt_name のために追加
#include <libswscale/swscale.h>
#include <libswresample/swresample.h> // オーディオリサンプリング用
}

#include "../../include/skia/include/core/SkImage.h"
#include "../../include/skia/include/core/SkData.h"

// プラットフォーム別のヘッダー
#ifdef _WIN32
#include <windows.h>
#elif defined(__linux__)
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>
#endif

#include "../../include/video_encoder.h"
#include "../../include/file_dialog.h"
#include "../../include/string_utils.h"

// グローバル変数
int64_t next_audio_pts = 0;

// グローバルビデオエンコーダー
VideoEncoder videoEncoder = {0};

// 内部関数のプロトタイプ宣言
static void displayFFmpegInfo();
static const AVCodec* findVideoCodec();
static const AVCodec* findGpuCodecs();
static const AVCodec* findSoftwareCodecs();
static bool configureVideoCodecContext(const AVCodec* codec);
static bool configureNvencEncoder(const char* codecName);
static bool configureQsvEncoder(const char* codecName);
static bool configureMfEncoder(const char* codecName);
static bool configureH264Encoder();
static void setBasicCodecContext(AVCodecContext* ctx);
static bool tryOpenCodec(const AVCodec* codec);
static bool setupAudioProcessing(const char* audioFilePath, bool* hasAudio, float videoDuration, char* tempAudioFilePathForEncoder, bool* audioFileIsTemporary);
static bool handleJapaneseAudioPath(const char* audioFilePath, char* currentAudioPath, char* tempAudioFilePathForEncoder, bool* audioFileIsTemporary);
static bool initializeAudioFile(const char* currentAudioPath, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary);
static bool setupAudioEncoder(const AVCodec* audioCodec, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary);
static bool setupAudioDecoder(AVStream* inputAudioStream, float videoDuration, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary);
static void cleanupAudioOnError(char* tempAudioFilePathForEncoder, bool audioFileIsTemporary);
static void cleanupOnError(char* tempAudioFilePathForEncoder, bool audioFileIsTemporary);

// ビデオエンコーダーを初期化する関数
bool initVideoEncoder(const char* outputFilePath, const char* audioFilePath, bool hasAudio, float videoDuration) {
    next_audio_pts = 0;

    int ret;
    char outputFileName[MAX_PATH_LENGTH];
    char tempAudioFilePathForEncoder[MAX_PATH_LENGTH] = {0};
    bool audio_file_is_temporary = false;

    if (containsJapaneseChars(outputFilePath)) {
        printf("Warning: The output file path contains Japanese characters.\n");
        strcpy(outputFileName, outputFilePath);
        printf("Using output file path: %s\n", outputFileName);
    } else {
        strcpy(outputFileName, outputFilePath);
    }

    avformat_alloc_output_context2(&videoEncoder.formatContext, NULL, NULL, outputFileName);
    if (!videoEncoder.formatContext) {
        fprintf(stderr, "Could not create output context from filename, trying specific formats\n");
        const char* formats[] = {"mp4", "matroska", "avi", "mov", "flv", "mpegts", "webm"};
        int num_formats = sizeof(formats) / sizeof(formats[0]);
        bool format_found = false;
        for (int k = 0; k < num_formats; k++) {
            avformat_alloc_output_context2(&videoEncoder.formatContext, NULL, formats[k], outputFileName);
            if (videoEncoder.formatContext) {
                printf("Using output format: %s\n", formats[k]);
                format_found = true;
                break;
            }
        }
        if (!format_found) {
            fprintf(stderr, "Could not create output context for any format\n");
            return false;
        }
    } else {
        printf("Using output format detected from filename: %s\n", videoEncoder.formatContext->oformat->name);
    }

    // ビデオコーデックを検索
    const AVCodec *videoCodec = findVideoCodec();
    if (!videoCodec) {
        fprintf(stderr, "No suitable video codec found\n");
        if (videoEncoder.formatContext) avformat_free_context(videoEncoder.formatContext);
        videoEncoder.formatContext = NULL;
        return false;
    }

    videoEncoder.videoCodecContext = avcodec_alloc_context3(videoCodec);
    if (!videoEncoder.videoCodecContext) {
        fprintf(stderr, "Could not allocate video codec context\n");
        if (videoEncoder.formatContext) avformat_free_context(videoEncoder.formatContext);
        videoEncoder.formatContext = NULL;
        return false;
    }

    // 基本的なコーデックコンテキストを設定
    setBasicCodecContext(videoEncoder.videoCodecContext);
    videoEncoder.videoCodecContext->max_b_frames = (videoCodec->id == AV_CODEC_ID_H264 || videoCodec->id == AV_CODEC_ID_MPEG4) ? 1 : 0;

    enum AVPixelFormat selected_pix_fmt = AV_PIX_FMT_YUV420P;
    const char* codec_name = videoCodec->name;
    if (codec_name) {
        if (strstr(codec_name, "mjpeg") || strstr(codec_name, "jpeg")) {
            selected_pix_fmt = AV_PIX_FMT_YUVJ420P;
        } else if (strstr(codec_name, "rawvideo")) {
            selected_pix_fmt = AV_PIX_FMT_BGR24;
        }
    }

    bool supported_pix_fmt = false;
    if (videoCodec->pix_fmts) {
        for (const enum AVPixelFormat *p = videoCodec->pix_fmts; *p != AV_PIX_FMT_NONE; p++) {
            if (*p == selected_pix_fmt) {
                supported_pix_fmt = true;
                break;
            }
        }
        if (!supported_pix_fmt && videoCodec->pix_fmts[0] != AV_PIX_FMT_NONE) {
            fprintf(stdout, "Warning: Codec %s may not support pix_fmt %d, using its first supported pix_fmt %d\n",
                    codec_name ? codec_name : "unknown", selected_pix_fmt, videoCodec->pix_fmts[0]);
            selected_pix_fmt = videoCodec->pix_fmts[0];
        } else if (!supported_pix_fmt) {
             fprintf(stdout, "Warning: Codec %s does not list supported pixel formats or preferred one (%d) is not available. Defaulting to YUV420P.\n",
                     codec_name ? codec_name : "unknown", selected_pix_fmt);
             selected_pix_fmt = AV_PIX_FMT_YUV420P;
        }
    } else {
        fprintf(stdout, "Warning: Codec %s does not provide pix_fmts list. Assuming %d is supported.\n",
                codec_name ? codec_name : "unknown", selected_pix_fmt);
    }
    videoEncoder.videoCodecContext->pix_fmt = selected_pix_fmt;
    printf("Using pixel format: %s (%d) for codec: %s\n",
           av_get_pix_fmt_name(selected_pix_fmt), selected_pix_fmt, codec_name ? codec_name : "unknown");

    // エンコーダー設定を適用
    if (!configureVideoCodecContext(videoCodec)) {
        fprintf(stderr, "Failed to configure video codec context\n");
        avcodec_free_context(&videoEncoder.videoCodecContext);
        avformat_free_context(videoEncoder.formatContext);
        return false;
    }

    videoEncoder.videoStream = avformat_new_stream(videoEncoder.formatContext, NULL);
    if (!videoEncoder.videoStream) {
        fprintf(stderr, "Could not allocate video stream\n");
        avcodec_free_context(&videoEncoder.videoCodecContext);
        avformat_free_context(videoEncoder.formatContext);
        return false;
    }
    videoEncoder.videoStream->id = videoEncoder.formatContext->nb_streams - 1;
    videoEncoder.videoStream->time_base = videoEncoder.videoCodecContext->time_base;

    ret = avcodec_parameters_from_context(videoEncoder.videoStream->codecpar, videoEncoder.videoCodecContext);
    if (ret < 0) {
        fprintf(stderr, "Could not copy the video stream parameters: %s\n", av_err2str(ret));
        avcodec_free_context(&videoEncoder.videoCodecContext);
        avformat_free_context(videoEncoder.formatContext);
        return false;
    }

    // コーデックを開く（フォールバック付き）
    if (!tryOpenCodec(videoCodec)) {
        fprintf(stderr, "Failed to open video codec\n");
        avcodec_free_context(&videoEncoder.videoCodecContext);
        avformat_free_context(videoEncoder.formatContext);
        return false;
    }

    // オーディオ処理を設定
    if (!setupAudioProcessing(audioFilePath, &hasAudio, videoDuration, tempAudioFilePathForEncoder, &audio_file_is_temporary)) {
        fprintf(stderr, "Failed to setup audio processing\n");
        // エラーでも続行（オーディオなしで）
    }



    // 変数宣言をgoto文の前に移動
    AVDictionary* muxer_opts = NULL;

    if (!(videoEncoder.formatContext->oformat->flags & AVFMT_NOFILE)) {
        ret = avio_open(&videoEncoder.formatContext->pb, outputFileName, AVIO_FLAG_WRITE);
        if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            fprintf(stderr, "Could not open output file %s: %s (err: %d)\n", outputFileName, errbuf, ret);
            goto error_cleanup;
        }
    }
    if (videoEncoder.formatContext->oformat->flags & AVFMT_GLOBALHEADER) {
        if (videoEncoder.videoCodecContext) videoEncoder.videoCodecContext->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
    }

    ret = avformat_write_header(videoEncoder.formatContext, &muxer_opts);
    av_dict_free(&muxer_opts);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Error writing header: %s (err: %d)\n", errbuf, ret);
        goto error_cleanup;
    }

    videoEncoder.frame = av_frame_alloc();
    if (!videoEncoder.frame) { goto error_cleanup; }
    videoEncoder.frame->format = videoEncoder.videoCodecContext->pix_fmt;
    videoEncoder.frame->width = videoEncoder.videoCodecContext->width;
    videoEncoder.frame->height = videoEncoder.videoCodecContext->height;
    if (av_frame_get_buffer(videoEncoder.frame, 0) < 0) { goto error_cleanup; }

    videoEncoder.packet = av_packet_alloc();
    if (!videoEncoder.packet) { goto error_cleanup; }

    if (hasAudio && videoEncoder.audioStream) {
        videoEncoder.audioPacket = av_packet_alloc();
        if (!videoEncoder.audioPacket) { goto error_cleanup; }
    }

    videoEncoder.rgbaBuffer = (uint8_t *)malloc(WIDTH * HEIGHT * 4);
    if (!videoEncoder.rgbaBuffer) { goto error_cleanup; }

    videoEncoder.swsContext = sws_getContext(WIDTH, HEIGHT, AV_PIX_FMT_RGBA,
                                           WIDTH, HEIGHT, videoEncoder.videoCodecContext->pix_fmt,
                                           SWS_BICUBIC, NULL, NULL, NULL);
    if (!videoEncoder.swsContext) { goto error_cleanup; }

    videoEncoder.initialized = true;
    videoEncoder.frameCounter = 0;
    videoEncoder.errorCount = 0;
    videoEncoder.audioEOF = false;

    return true;

error_cleanup:
    fprintf(stderr, "Error during init, cleaning up.\n");
    if (videoEncoder.rgbaBuffer) { free(videoEncoder.rgbaBuffer); videoEncoder.rgbaBuffer = NULL; }
    if (videoEncoder.audioPacket) { av_packet_free(&videoEncoder.audioPacket); videoEncoder.audioPacket = NULL; }
    if (videoEncoder.packet) { av_packet_free(&videoEncoder.packet); videoEncoder.packet = NULL; }
    if (videoEncoder.frame) { av_frame_free(&videoEncoder.frame); videoEncoder.frame = NULL; }
    if (videoEncoder.swsContext) { sws_freeContext(videoEncoder.swsContext); videoEncoder.swsContext = NULL; }
    if (videoEncoder.videoCodecContext) { avcodec_free_context(&videoEncoder.videoCodecContext); videoEncoder.videoCodecContext = NULL; }
    if (videoEncoder.audioFormatContext) { avformat_close_input(&videoEncoder.audioFormatContext); videoEncoder.audioFormatContext = NULL; }
    if (audio_file_is_temporary && tempAudioFilePathForEncoder[0] != '\0') {
        DeleteFileA(tempAudioFilePathForEncoder);
    }
    if (videoEncoder.formatContext) {
        if (!(videoEncoder.formatContext->oformat->flags & AVFMT_NOFILE) && videoEncoder.formatContext->pb) {
            avio_closep(&videoEncoder.formatContext->pb);
        }
        avformat_free_context(videoEncoder.formatContext);
        videoEncoder.formatContext = NULL;
    }
    memset(&videoEncoder, 0, sizeof(VideoEncoder));
    videoEncoder.initialized = false;
    return false;
}

bool encodeFrame(sk_sp<SkImage> image, float currentTime) {
    int ret;

    if (!videoEncoder.initialized || !videoEncoder.videoCodecContext || !videoEncoder.frame || !videoEncoder.packet || !videoEncoder.formatContext) {
        fprintf(stderr, "Encoder not properly initialized for encodeFrame\n");
        return false;
    }

    // SkImageからピクセルデータを取得
    SkImageInfo info = SkImageInfo::Make(WIDTH, HEIGHT, kRGBA_8888_SkColorType, kPremul_SkAlphaType);

    // フレーム0でのみ詳細情報を出力
    static bool firstFrame = true;
    if (firstFrame) {
        printf("Encoding first frame: image size %dx%d, texture-backed: %s\n",
               image->width(), image->height(),
               image->isTextureBacked() ? "yes" : "no");
        firstFrame = false;
    }

    if (!image->readPixels(info, videoEncoder.rgbaBuffer, WIDTH * 4, 0, 0)) {
        fprintf(stderr, "Failed to read pixels from SkImage (frame %d)\n", videoEncoder.frameCounter);
        return false;
    }

    if (av_frame_make_writable(videoEncoder.frame) < 0) {
        fprintf(stderr, "Could not make frame writable\n");
        return false;
    }

    const uint8_t *const srcData[4] = {videoEncoder.rgbaBuffer, NULL, NULL, NULL};
    int srcLinesize[4] = {WIDTH * 4, 0, 0, 0};
    sws_scale(videoEncoder.swsContext, srcData, srcLinesize, 0, HEIGHT, videoEncoder.frame->data, videoEncoder.frame->linesize);

    // PTSを時間ベースで正しく設定（単調増加を保証）
    // time_baseが{1, FPS}なので、フレーム番号がそのままPTSになる
    videoEncoder.frame->pts = videoEncoder.frameCounter;

    // フレームカウンターを増加
    videoEncoder.frameCounter++;

    ret = avcodec_send_frame(videoEncoder.videoCodecContext, videoEncoder.frame);
    if (ret < 0) {
        // AVERROR_EOF は正常な終了を示すので、エラーとして扱わない
        if (ret == AVERROR_EOF) {
            printf("Video encoder reached end of stream (normal completion)\n");
            return true; // 正常終了として扱う
        }

        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Error sending video frame: %s (err: %d)\n", errbuf, ret);

        // エンコーダーの詳細情報を出力
        fprintf(stderr, "Encoder details: codec=%s, pix_fmt=%d, width=%d, height=%d, bit_rate=%ld\n",
                videoEncoder.videoCodecContext->codec ? videoEncoder.videoCodecContext->codec->name : "unknown",
                videoEncoder.videoCodecContext->pix_fmt,
                videoEncoder.videoCodecContext->width,
                videoEncoder.videoCodecContext->height,
                videoEncoder.videoCodecContext->bit_rate);

        // フレームの詳細情報を出力
        if (videoEncoder.frame) {
            fprintf(stderr, "Frame details: format=%d, width=%d, height=%d, pts=%lld\n",
                    videoEncoder.frame->format,
                    videoEncoder.frame->width,
                    videoEncoder.frame->height,
                    videoEncoder.frame->pts);
        }

        return false;
    }

    while (true) {
        ret = avcodec_receive_packet(videoEncoder.videoCodecContext, videoEncoder.packet);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
        if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            fprintf(stderr, "Error during video encoding: %s (err: %d)\n", errbuf, ret);

            // エンコーダーの状態をリセットしてみる
            avcodec_flush_buffers(videoEncoder.videoCodecContext);

            // エラーが続く場合は、より安全なエンコーダー設定に切り替える
            if (videoEncoder.errorCount++ > 5) {
                fprintf(stderr, "Too many encoding errors, trying to adjust encoder settings...\n");

                // B-フレームを無効化
                videoEncoder.videoCodecContext->max_b_frames = 0;

                // GOPサイズを小さくする
                videoEncoder.videoCodecContext->gop_size = FPS / 4;

                // エラーカウンターをリセット
                videoEncoder.errorCount = 0;
            }

            return false;
        }

        videoEncoder.packet->stream_index = videoEncoder.videoStream->index;
        if (videoEncoder.videoStream->time_base.den != 0) {
             av_packet_rescale_ts(videoEncoder.packet, videoEncoder.videoCodecContext->time_base, videoEncoder.videoStream->time_base);
        } else {
            fprintf(stderr, "Warning: Video stream time_base invalid in encodeFrame, TS not rescaled.\n");
        }

        int write_ret = av_interleaved_write_frame(videoEncoder.formatContext, videoEncoder.packet);
        av_packet_unref(videoEncoder.packet);

        if (write_ret < 0) {
            fprintf(stderr, "Error writing video packet: %s\n", av_err2str(write_ret));
            return false;
        }
    }

    // オーディオ処理（デコード・エンコード）- 常に処理する
    if (videoEncoder.audioStream && videoEncoder.audioCodecContext) {

        // 必要なオーディオサンプル数を計算（現在の時間に基づく）
        double target_audio_time = currentTime + (1.0 / FPS); // 次のフレームまでの時間
        int64_t target_audio_samples = (int64_t)(target_audio_time * videoEncoder.audioCodecContext->sample_rate);

        // 現在のオーディオサンプル数が目標に達するまで処理
        while (next_audio_pts < target_audio_samples) {
            // オーディオEOFに達している場合は無音を生成
            if (videoEncoder.audioEOF) {
                // 無音フレームを生成
                if (videoEncoder.audioFrameResampled) {
                    av_frame_unref(videoEncoder.audioFrameResampled);

                    // 無音フレームを設定
                    videoEncoder.audioFrameResampled->format = videoEncoder.audioCodecContext->sample_fmt;
                    videoEncoder.audioFrameResampled->ch_layout = videoEncoder.audioCodecContext->ch_layout;
                    videoEncoder.audioFrameResampled->sample_rate = videoEncoder.audioCodecContext->sample_rate;
                    videoEncoder.audioFrameResampled->nb_samples = videoEncoder.audioCodecContext->frame_size;
                    videoEncoder.audioFrameResampled->pts = next_audio_pts;

                    if (av_frame_get_buffer(videoEncoder.audioFrameResampled, 0) >= 0) {
                        // バッファを0で埋める（無音）
                        av_samples_set_silence(videoEncoder.audioFrameResampled->data, 0,
                                             videoEncoder.audioFrameResampled->nb_samples,
                                             videoEncoder.audioFrameResampled->ch_layout.nb_channels,
                                             (enum AVSampleFormat)videoEncoder.audioFrameResampled->format);

                        printf("Generating silence frame: pts=%lld, samples=%d\n", next_audio_pts, videoEncoder.audioFrameResampled->nb_samples);

                        // 無音フレームをエンコード
                        int silence_ret = avcodec_send_frame(videoEncoder.audioCodecContext, videoEncoder.audioFrameResampled);
                        if (silence_ret >= 0) {
                            AVPacket *silencePacket = av_packet_alloc();
                            int packet_count = 0;
                            while (avcodec_receive_packet(videoEncoder.audioCodecContext, silencePacket) >= 0) {
                                silencePacket->stream_index = videoEncoder.audioStream->index;
                                av_packet_rescale_ts(silencePacket, videoEncoder.audioCodecContext->time_base, videoEncoder.audioStream->time_base);
                                int write_ret = av_interleaved_write_frame(videoEncoder.formatContext, silencePacket);
                                if (write_ret >= 0) {
                                    packet_count++;
                                } else {
                                    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                                    av_strerror(write_ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                                    fprintf(stderr, "Error writing silence packet: %s\n", errbuf);
                                }
                                av_packet_unref(silencePacket);
                            }
                            av_packet_free(&silencePacket);
                            next_audio_pts += videoEncoder.audioFrameResampled->nb_samples;
                            printf("Silence frame encoded: %d packets written\n", packet_count);
                        } else {
                            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                            av_strerror(silence_ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                            fprintf(stderr, "Error sending silence frame to encoder: %s\n", errbuf);
                        }
                        av_frame_unref(videoEncoder.audioFrameResampled);
                    } else {
                        fprintf(stderr, "Failed to allocate buffer for silence frame\n");
                    }
                } else {
                    fprintf(stderr, "audioFrameResampled is null, cannot generate silence\n");
                }
                // breakを削除して、継続的に無音を生成
                continue; // whileループを継続
            }
            // 通常のオーディオ処理
            else if (videoEncoder.audioFormatContext && videoEncoder.audioPacket &&
                     videoEncoder.audioDecoderContext) {

                int input_audio_stream_idx = -1;
                for (unsigned int k = 0; k < videoEncoder.audioFormatContext->nb_streams; ++k) {
                    if (videoEncoder.audioFormatContext->streams[k]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO) {
                        input_audio_stream_idx = k;
                        break;
                    }
                }

                if (input_audio_stream_idx != -1) {
                    // デバッグ情報を追加
                    static int debug_counter = 0;
                    if (debug_counter % 60 == 0) { // 1秒ごとにデバッグ情報を出力
                        double current_audio_pts_sec = (double)next_audio_pts / videoEncoder.audioCodecContext->sample_rate;
                        printf("Audio debug: currentTime=%.2f, audio_pts_sec=%.2f, target_samples=%lld, next_pts=%lld, audioEOF=%d\n",
                               currentTime, current_audio_pts_sec, target_audio_samples, next_audio_pts, videoEncoder.audioEOF);
                    }
                    debug_counter++;

                    int read_result = av_read_frame(videoEncoder.audioFormatContext, videoEncoder.audioPacket);
                    if (read_result < 0) {
                        if (read_result == AVERROR_EOF) {
                            // オーディオファイルの終端に達した場合、ループ再生を試行
                            printf("Audio EOF reached, attempting to loop audio file\n");

                            // 安全性チェック
                            if (!videoEncoder.audioFormatContext || !videoEncoder.audioDecoderContext) {
                                printf("Audio context is null, switching to silence padding\n");
                                videoEncoder.audioEOF = true;
                                av_packet_unref(videoEncoder.audioPacket);
                                continue;
                            }

                            // ファイルの先頭にシーク
                            int seek_result = av_seek_frame(videoEncoder.audioFormatContext, -1, 0, AVSEEK_FLAG_BACKWARD);
                            if (seek_result >= 0) {
                                // デコーダーのバッファをフラッシュ
                                avcodec_flush_buffers(videoEncoder.audioDecoderContext);
                                printf("Audio file looped successfully\n");
                                av_packet_unref(videoEncoder.audioPacket);
                                continue; // 再度読み取りを試行
                            } else {
                                // シークに失敗した場合は無音生成に切り替え
                                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                                av_strerror(seek_result, errbuf, AV_ERROR_MAX_STRING_SIZE);
                                printf("Audio seek failed (%s), switching to silence padding\n", errbuf);
                                videoEncoder.audioEOF = true; // EOFフラグを設定
                                av_packet_unref(videoEncoder.audioPacket);
                                continue; // whileループを継続して無音処理に移行
                            }
                        } else if (read_result == AVERROR(EAGAIN)) {
                            break;
                        } else {
                            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                            av_strerror(read_result, errbuf, AV_ERROR_MAX_STRING_SIZE);
                            fprintf(stderr, "Error reading audio packet: %s\n", errbuf);
                            break;
                        }
                    }

                    if (videoEncoder.audioPacket->stream_index == input_audio_stream_idx) {
                        // パケットをデコード
                        ret = avcodec_send_packet(videoEncoder.audioDecoderContext, videoEncoder.audioPacket);
                        if (ret < 0) {
                            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                            fprintf(stderr, "Error sending audio packet to decoder: %s\n", errbuf);
                            av_packet_unref(videoEncoder.audioPacket);
                            continue;
                        }

                        while (ret >= 0) {
                            ret = avcodec_receive_frame(videoEncoder.audioDecoderContext, videoEncoder.audioFrame);
                            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                                break;
                            } else if (ret < 0) {
                                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                                fprintf(stderr, "Error receiving audio frame from decoder: %s\n", errbuf);
                                break;
                            }

                        // リサンプラーを初期化（必要に応じて）
                        if (!videoEncoder.swrContext) {
                            ret = swr_alloc_set_opts2(&videoEncoder.swrContext,
                                                    &videoEncoder.audioCodecContext->ch_layout,
                                                    videoEncoder.audioCodecContext->sample_fmt,
                                                    videoEncoder.audioCodecContext->sample_rate,
                                                    &videoEncoder.audioDecoderContext->ch_layout,
                                                    videoEncoder.audioDecoderContext->sample_fmt,
                                                    videoEncoder.audioDecoderContext->sample_rate,
                                                    0, NULL);
                            if (ret < 0) {
                                fprintf(stderr, "Could not allocate resampler context\n");
                                break;
                            }
                            ret = swr_init(videoEncoder.swrContext);
                            if (ret < 0) {
                                fprintf(stderr, "Could not initialize resampler context\n");
                                break;
                            }
                        }

                        // リサンプル後のフレームを設定
                        videoEncoder.audioFrameResampled->format = videoEncoder.audioCodecContext->sample_fmt;
                        videoEncoder.audioFrameResampled->ch_layout = videoEncoder.audioCodecContext->ch_layout;
                        videoEncoder.audioFrameResampled->sample_rate = videoEncoder.audioCodecContext->sample_rate;
                        videoEncoder.audioFrameResampled->nb_samples = videoEncoder.audioCodecContext->frame_size;

                        ret = av_frame_get_buffer(videoEncoder.audioFrameResampled, 0);
                        if (ret < 0) {
                            fprintf(stderr, "Could not allocate audio frame buffer\n");
                            break;
                        }

                        // リサンプリング実行
                        int converted_samples = swr_convert(videoEncoder.swrContext,
                                                          videoEncoder.audioFrameResampled->data,
                                                          videoEncoder.audioFrameResampled->nb_samples,
                                                          (const uint8_t**)videoEncoder.audioFrame->data,
                                                          videoEncoder.audioFrame->nb_samples);
                        if (converted_samples < 0) {
                            fprintf(stderr, "Error during audio resampling\n");
                            av_frame_unref(videoEncoder.audioFrameResampled);
                            break;
                        }

                        videoEncoder.audioFrameResampled->nb_samples = converted_samples;
                        videoEncoder.audioFrameResampled->pts = next_audio_pts;

                        // エンコード
                        ret = avcodec_send_frame(videoEncoder.audioCodecContext, videoEncoder.audioFrameResampled);
                        if (ret < 0) {
                            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                            fprintf(stderr, "Error sending audio frame to encoder: %s\n", errbuf);
                            av_frame_unref(videoEncoder.audioFrameResampled);
                            break;
                        }

                        AVPacket *encodedPacket = av_packet_alloc();
                        while (ret >= 0) {
                            ret = avcodec_receive_packet(videoEncoder.audioCodecContext, encodedPacket);
                            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                                break;
                            } else if (ret < 0) {
                                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                                fprintf(stderr, "Error receiving audio packet from encoder: %s\n", errbuf);
                                break;
                            }

                            encodedPacket->stream_index = videoEncoder.audioStream->index;
                            av_packet_rescale_ts(encodedPacket, videoEncoder.audioCodecContext->time_base, videoEncoder.audioStream->time_base);

                            int write_ret = av_interleaved_write_frame(videoEncoder.formatContext, encodedPacket);
                            if (write_ret < 0) {
                                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                                av_strerror(write_ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                                fprintf(stderr, "Error writing audio packet: %s\n", errbuf);
                            }
                            av_packet_unref(encodedPacket);
                        }
                        av_packet_free(&encodedPacket);

                        next_audio_pts += videoEncoder.audioFrameResampled->nb_samples;

                        av_frame_unref(videoEncoder.audioFrameResampled);
                    }
                }
                av_packet_unref(videoEncoder.audioPacket);
            }
        }
    }
    }
    return true;
}

bool finalizeVideoEncoder() {
    int ret;

    if (!videoEncoder.initialized) {
        return true;
    }
    if (!videoEncoder.formatContext) {
        fprintf(stderr, "Finalize: Format context is null.\n");
        return true;
    }

    // ビデオエンコーダーのフラッシュ
    if (videoEncoder.videoCodecContext && videoEncoder.videoStream) {
        printf("Flushing video encoder...\n");
        ret = avcodec_send_frame(videoEncoder.videoCodecContext, NULL);
        if (ret < 0 && ret != AVERROR_EOF && ret != AVERROR(EAGAIN)) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            fprintf(stderr, "Error sending flush frame to video: %s (code: %d)\n", errbuf, ret);

            // Media Foundation エンコーダーの場合、フラッシュエラーを無視して続行
            if (videoEncoder.videoCodecContext->codec &&
                videoEncoder.videoCodecContext->codec->name &&
                strstr(videoEncoder.videoCodecContext->codec->name, "mf")) {
                printf("Media Foundation encoder flush error ignored, continuing with finalization\n");
                // MFエラーの場合は強制的に成功として扱う
                ret = 0;
            } else {
                printf("Non-MF encoder flush error, attempting to continue\n");
            }
        }

        // フラッシュが成功した場合、またはMFエラーを無視した場合のみパケット受信を試行
        if (ret >= 0) {
            int flush_attempts = 0;
            const int max_flush_attempts = 100; // 無限ループ防止

            while (flush_attempts < max_flush_attempts) {
                if (!videoEncoder.packet) {
                    fprintf(stderr, "Finalize: video packet is null.\n");
                    break;
                }
                ret = avcodec_receive_packet(videoEncoder.videoCodecContext, videoEncoder.packet);
                if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
                if (ret < 0) {
                    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                    av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                    fprintf(stderr, "Error flushing video: %s (code: %d)\n", errbuf, ret);

                    // Media Foundation の場合はエラーを無視
                    if (videoEncoder.videoCodecContext->codec &&
                        videoEncoder.videoCodecContext->codec->name &&
                        strstr(videoEncoder.videoCodecContext->codec->name, "mf")) {
                        printf("Media Foundation receive packet error ignored\n");
                        break;
                    } else {
                        break;
                    }
                }

                videoEncoder.packet->stream_index = videoEncoder.videoStream->index;
                if(videoEncoder.videoStream->time_base.den != 0) {
                    av_packet_rescale_ts(videoEncoder.packet, videoEncoder.videoCodecContext->time_base, videoEncoder.videoStream->time_base);
                } else {
                    fprintf(stderr, "Finalize: videoStream time_base invalid, not rescaling TS.\n");
                }

                int write_ret = av_interleaved_write_frame(videoEncoder.formatContext, videoEncoder.packet);
                av_packet_unref(videoEncoder.packet);

                if (write_ret < 0) {
                    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                    av_strerror(write_ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                    fprintf(stderr, "Error writing flushed video packet: %s (code: %d)\n", errbuf, write_ret);
                    break;
                }

                flush_attempts++;
            }

            if (flush_attempts >= max_flush_attempts) {
                printf("Warning: Video flush reached maximum attempts, continuing\n");
            }
        }
        printf("Video encoder flush completed\n");
    }

    // オーディオエンコーダーのフラッシュ
    if (videoEncoder.audioCodecContext && videoEncoder.audioStream) {
        printf("Flushing audio encoder...\n");
        ret = avcodec_send_frame(videoEncoder.audioCodecContext, NULL);
        if (ret < 0 && ret != AVERROR_EOF && ret != AVERROR(EAGAIN)) {
            fprintf(stderr, "Error sending flush frame to audio: %s\n", av_err2str(ret));
        } else {
            AVPacket *flushPacket = av_packet_alloc();
            if (!flushPacket) {
                fprintf(stderr, "Failed to allocate flush packet for audio\n");
            } else {
                while (true) {
                    ret = avcodec_receive_packet(videoEncoder.audioCodecContext, flushPacket);
                    if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
                    if (ret < 0) {
                        fprintf(stderr, "Error flushing audio: %s\n", av_err2str(ret));
                        break;
                    }

                    flushPacket->stream_index = videoEncoder.audioStream->index;
                    if (videoEncoder.audioStream && videoEncoder.audioStream->time_base.den != 0) {
                        av_packet_rescale_ts(flushPacket, videoEncoder.audioCodecContext->time_base, videoEncoder.audioStream->time_base);
                    } else {
                        fprintf(stderr, "Finalize: audioStream time_base invalid, not rescaling TS.\n");
                    }

                    int write_ret = av_interleaved_write_frame(videoEncoder.formatContext, flushPacket);
                    av_packet_unref(flushPacket);

                    if (write_ret < 0) {
                        fprintf(stderr, "Error writing flushed audio packet: %s\n", av_err2str(write_ret));
                        break;
                    }
                }
                av_packet_free(&flushPacket);
            }
        }
        printf("Audio encoder flushed\n");
    }

    // トレーラーを書き込む
    printf("Writing trailer...\n");
    ret = av_write_trailer(videoEncoder.formatContext);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Error writing trailer: %s (code: %d)\n", errbuf, ret);

        // Media Foundation エンコーダーの場合、トレーラーエラーを警告として扱う
        if (videoEncoder.videoCodecContext &&
            videoEncoder.videoCodecContext->codec &&
            videoEncoder.videoCodecContext->codec->name &&
            strstr(videoEncoder.videoCodecContext->codec->name, "mf")) {
            printf("Media Foundation encoder trailer error treated as warning, file may still be usable\n");
        } else {
            return false;
        }
    } else {
        printf("Trailer written successfully\n");
    }

    printf("Video finalized successfully\n");
    return true;
}

void cleanupVideoEncoder() {
    printf("Starting video encoder cleanup...\n");

    // フレームとパケットを最初に解放
    if (videoEncoder.frame) {
        av_frame_free(&videoEncoder.frame);
        videoEncoder.frame = NULL;
    }
    if (videoEncoder.audioFrame) {
        av_frame_free(&videoEncoder.audioFrame);
        videoEncoder.audioFrame = NULL;
    }
    if (videoEncoder.audioFrameResampled) {
        av_frame_free(&videoEncoder.audioFrameResampled);
        videoEncoder.audioFrameResampled = NULL;
    }
    if (videoEncoder.packet) {
        av_packet_free(&videoEncoder.packet);
        videoEncoder.packet = NULL;
    }
    if (videoEncoder.audioPacket) {
        av_packet_free(&videoEncoder.audioPacket);
        videoEncoder.audioPacket = NULL;
    }

    // コーデックコンテキストを解放
    if (videoEncoder.videoCodecContext) {
        avcodec_free_context(&videoEncoder.videoCodecContext);
        videoEncoder.videoCodecContext = NULL;
    }
    if (videoEncoder.audioCodecContext) {
        avcodec_free_context(&videoEncoder.audioCodecContext);
        videoEncoder.audioCodecContext = NULL;
    }
    if (videoEncoder.audioDecoderContext) {
        avcodec_free_context(&videoEncoder.audioDecoderContext);
        videoEncoder.audioDecoderContext = NULL;
    }

    // リサンプラーとスケーラーを解放
    if (videoEncoder.swrContext) {
        swr_free(&videoEncoder.swrContext);
        videoEncoder.swrContext = NULL;
    }
    if (videoEncoder.swsContext) {
        sws_freeContext(videoEncoder.swsContext);
        videoEncoder.swsContext = NULL;
    }

    // オーディオフォーマットコンテキストを解放
    if (videoEncoder.audioFormatContext) {
        avformat_close_input(&videoEncoder.audioFormatContext);
        videoEncoder.audioFormatContext = NULL;
    }

    // フォーマットコンテキストを最後に解放
    if (videoEncoder.formatContext) {
        // 安全性チェック
        if (videoEncoder.formatContext->oformat &&
            !(videoEncoder.formatContext->oformat->flags & AVFMT_NOFILE) &&
            videoEncoder.formatContext->pb) {
            avio_closep(&videoEncoder.formatContext->pb);
        }
        avformat_free_context(videoEncoder.formatContext);
        videoEncoder.formatContext = NULL;
    }

    // バッファを最後に解放
    if (videoEncoder.rgbaBuffer) {
        free(videoEncoder.rgbaBuffer);
        videoEncoder.rgbaBuffer = NULL;
    }

    // ストリームポインタをクリア（これらは所有していない）
    videoEncoder.videoStream = NULL;
    videoEncoder.audioStream = NULL;

    // フラグとカウンターをリセット
    videoEncoder.initialized = false;
    videoEncoder.frameCounter = 0;
    videoEncoder.errorCount = 0;
    videoEncoder.videoDuration = 0.0f;
    videoEncoder.audioEOF = false;

    printf("Video encoder cleanup completed\n");
}

// ビデオコーデックを検索する関数
static const AVCodec* findVideoCodec() {
    const AVCodec* codec = NULL;

    // まずGPUエンコーダーを優先的に探す
    printf("Prioritizing GPU encoders for better performance...\n");
    codec = findGpuCodecs();
    if (codec) {
        return codec;
    }

    // GPUエンコーダーが見つからない場合、通常のH.264エンコーダーを使用
    codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (codec) {
        printf("Using software H.264 video codec: %s\n", codec->name);
        return codec;
    }

    // フォーマットのデフォルトコーデックを試す
    if (videoEncoder.formatContext->oformat->video_codec != AV_CODEC_ID_NONE) {
        codec = avcodec_find_encoder(videoEncoder.formatContext->oformat->video_codec);
        if (codec) {
            printf("Using format's default video codec: %s\n", codec->name);
            return codec;
        }
    }

    // 最後の手段としてソフトウェアエンコーダーを試す
    codec = findSoftwareCodecs();
    return codec;
}

// GPUエンコーダーを検索する関数
static const AVCodec* findGpuCodecs() {
    const AVCodec* codec = NULL;

    // 優先GPUコーデックリスト
    const char* preferred_gpu_codecs[] = {
        "h264_nvenc",      // NVIDIA NVENC
        "h264_qsv",        // Intel Quick Sync Video
        "h264_amf",        // AMD AMF
        "h264_mf",         // Microsoft Media Foundation
        "h264_videotoolbox", // Apple VideoToolbox
        "h264_vaapi"       // VA-API (Linux)
    };

    for (int i = 0; i < sizeof(preferred_gpu_codecs) / sizeof(preferred_gpu_codecs[0]); i++) {
        codec = avcodec_find_encoder_by_name(preferred_gpu_codecs[i]);
        if (codec) {
            printf("✓ Using GPU H.264 encoder: %s\n", codec->name);
            return codec;
        }
    }

    // より多くのGPUエンコーダーを試す
    printf("No default codec found, trying GPU encoders first, then common encoders\n");

    // 利用可能なハードウェアエンコーダーを確認
    enum AVHWDeviceType hw_type = AV_HWDEVICE_TYPE_NONE;
    printf("Checking available hardware encoders:\n");
    bool has_hardware = false;
    while ((hw_type = av_hwdevice_iterate_types(hw_type)) != AV_HWDEVICE_TYPE_NONE) {
        printf("  Hardware type: %s\n", av_hwdevice_get_type_name(hw_type));
        has_hardware = true;
    }
    if (!has_hardware) {
        printf("  No hardware acceleration types detected\n");
    }

    // GPU情報を表示
    printf("Attempting to use GPU acceleration for video encoding...\n");

    // GPUエンコーダーを最優先で試す（より多くのGPUエンコーダーを追加）
    const char* gpu_codecs[] = {
        "h264_nvenc",      // NVIDIA NVENC
        "hevc_nvenc",      // NVIDIA NVENC HEVC
        "h264_qsv",        // Intel Quick Sync Video
        "hevc_qsv",        // Intel Quick Sync Video HEVC
        "h264_amf",        // AMD AMF
        "hevc_amf",        // AMD AMF HEVC
        "h264_mf",         // Microsoft Media Foundation
        "hevc_mf",         // Microsoft Media Foundation HEVC
        "h264_videotoolbox", // Apple VideoToolbox
        "hevc_videotoolbox", // Apple VideoToolbox HEVC
        "h264_vaapi",      // VA-API (Linux)
        "hevc_vaapi"       // VA-API HEVC (Linux)
    };

    for (int k = 0; k < sizeof(gpu_codecs) / sizeof(gpu_codecs[0]); k++) {
        codec = avcodec_find_encoder_by_name(gpu_codecs[k]);
        if (codec) {
            printf("✓ Found GPU codec: %s (%s)\n", codec->name, gpu_codecs[k]);
            printf("  GPU acceleration will be used for video encoding\n");
            return codec;
        } else {
            printf("✗ GPU codec %s not available\n", gpu_codecs[k]);
        }
    }

    return NULL;
}

// ソフトウェアエンコーダーを検索する関数
static const AVCodec* findSoftwareCodecs() {
    const AVCodec* codec = NULL;

    printf("No GPU encoder found, trying software encoders\n");
    const char* common_codecs[] = {
        "libx264", "h264", "mpeg4", "mjpeg", "mpeg2video", "flv", "msmpeg4", "wmv2", "libvpx", "theora"
    };
    for (int k = 0; k < sizeof(common_codecs) / sizeof(common_codecs[0]); k++) {
        codec = avcodec_find_encoder_by_name(common_codecs[k]);
        if (codec) {
            printf("Found software codec: %s\n", codec->name);
            return codec;
        } else {
            printf("Software codec %s not available\n", common_codecs[k]);
        }
    }

    // 最後の手段として任意のエンコーダーを試す
    fprintf(stderr, "No common codec found, trying to find ANY available encoder\n");
    enum AVCodecID codec_ids[] = {
        AV_CODEC_ID_H264, AV_CODEC_ID_MPEG4, AV_CODEC_ID_MPEG2VIDEO,
        AV_CODEC_ID_MJPEG, AV_CODEC_ID_FLV1, AV_CODEC_ID_RAWVIDEO
    };
    for (int k = 0; k < sizeof(codec_ids) / sizeof(codec_ids[0]); k++) {
        codec = avcodec_find_encoder(codec_ids[k]);
        if (codec) {
            printf("Found codec by ID: %s\n", codec->name);
            return codec;
        }
    }

    return NULL;
}

// ビデオコーデックコンテキストを設定する関数
static bool configureVideoCodecContext(const AVCodec* codec) {
    const char* codec_name = codec->name;

    // NVENCエンコーダーの場合
    if (strstr(codec_name, "nvenc")) {
        return configureNvencEncoder(codec_name);
    }
    // QSVエンコーダーの場合
    else if (strstr(codec_name, "qsv")) {
        return configureQsvEncoder(codec_name);
    }
    // Media Foundation (MF) encoder configuration
    else if (strstr(codec_name, "mf")) {
        return configureMfEncoder(codec_name);
    }
    // 通常のH.264エンコーダーの場合
    else if (codec->id == AV_CODEC_ID_H264) {
        return configureH264Encoder();
    }
    // MJPEGエンコーダーの場合
    else if (codec->id == AV_CODEC_ID_MJPEG) {
        videoEncoder.videoCodecContext->flags |= AV_CODEC_FLAG_QSCALE;
        videoEncoder.videoCodecContext->global_quality = FF_QP2LAMBDA * 10;
        return true;
    }

    // デフォルト設定
    return true;
}

// NVENCエンコーダーを設定する関数
static bool configureNvencEncoder(const char* codecName) {
    printf("Configuring NVENC encoder settings with CRF 6\n");

    // H.264 NVENC設定
    if (strstr(codecName, "h264")) {
        // NVENCプリセット設定（最高品質）
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "p7", 0); // p7: 最高品質（最も遅い）
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "tune", "hq", 0);   // hq: 高品質

        // CRF 6相当の設定（最高品質）
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "rc", "constqp", 0); // Constant QP mode
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "qp", 6, 0);     // QP=6 (CRF 6相当)
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "qmin", 6, 0);   // 最小QP=6
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "qmax", 6, 0);   // 最大QP=6（固定品質）

        // 品質向上のための設定
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "spatial-aq", 1, 0);    // 空間的適応量子化
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "temporal-aq", 1, 0);   // 時間的適応量子化
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "rc-lookahead", 32, 0); // 先読みフレーム数（最大）
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "aq-strength", 15, 0);  // AQ強度最大

        // B-frame設定（高品質）
        videoEncoder.videoCodecContext->max_b_frames = 4; // より多くのB-frame
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "b_ref_mode", "middle", 0); // B-frameを参照フレームとして使用

        printf("NVENC H.264 settings: preset=p7 (highest quality), rc=constqp, qp=6 (CRF 6 equivalent)\n");
    }
    // HEVC NVENC設定
    else if (strstr(codecName, "hevc")) {
        printf("Using HEVC NVENC encoder for better compression\n");
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "p4", 0);
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "tune", "hq", 0);
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "rc", "constqp", 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "cq", 6, 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "spatial-aq", 1, 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "temporal-aq", 1, 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "rc-lookahead", 20, 0);
        videoEncoder.videoCodecContext->max_b_frames = 3;
    }

    printf("NVENC GPU acceleration enabled\n");
    return true;
}

// QSVエンコーダーを設定する関数
static bool configureQsvEncoder(const char* codecName) {
    printf("Configuring QSV encoder settings with CRF 6\n");

    // H.264 QSV設定
    if (strstr(codecName, "h264")) {
        // QSVプリセット設定（最高品質）
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "veryslow", 0); // 最高品質プリセット

        // CRF 6相当の設定（最高品質）
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "rate_control", "icq", 0); // ICQ（CRF相当）
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "global_quality", 6, 0); // CRF 6（非常に高品質）
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "qmin", 6, 0); // 最小品質=6
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "qmax", 6, 0); // 最大品質=6（固定品質）

        av_opt_set(videoEncoder.videoCodecContext->priv_data, "profile", "high", 0); // high profile
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "look_ahead", "1", 0); // 先読み有効化
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "look_ahead_depth", 40, 0); // 先読み深度最大

        // 品質設定（最高品質）
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "async_depth", 1, 0); // 品質優先（低遅延）

        // B-frame設定（高品質）
        videoEncoder.videoCodecContext->max_b_frames = 4; // より多くのB-frame

        // ハードウェアアクセラレーション設定
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "low_power", 0, 0); // 0: 通常モード（高品質）

        printf("QSV H.264 encoder settings: preset=veryslow (highest quality), rate_control=icq, global_quality=6 (CRF 6 equivalent)\n");
    }
    // HEVC QSV設定
    else if (strstr(codecName, "hevc")) {
        printf("Using HEVC QSV encoder for better compression\n");
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "faster", 0);
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "rate_control", "icq", 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "global_quality", 6, 0);
        av_opt_set(videoEncoder.videoCodecContext->priv_data, "look_ahead", "1", 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "async_depth", 4, 0);
        av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "low_power", 0, 0);
        videoEncoder.videoCodecContext->max_b_frames = 3;
        printf("QSV HEVC encoder settings configured\n");
    }

    printf("QSV GPU acceleration enabled\n");
    return true;
}

// Media Foundationエンコーダーを設定する関数
static bool configureMfEncoder(const char* codecName) {
    printf("Configuring Media Foundation encoder settings with improved stability\n");

    // Change pixel format to NV12 (best supported by MF)
    videoEncoder.videoCodecContext->pix_fmt = AV_PIX_FMT_NV12;
    printf("Using NV12 pixel format for MF: %s\n", av_get_pix_fmt_name(videoEncoder.videoCodecContext->pix_fmt));

    // より安定した設定を使用（フラッシュエラーを防ぐため）
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "rate_control", 1, 0); // 1: Quality-based VBR (より安定)

    // 高品質だが安定したビットレート設定
    videoEncoder.videoCodecContext->bit_rate = 20000000; // 20Mbps (高品質だが安定)
    videoEncoder.videoCodecContext->rc_max_rate = 30000000; // 最大30Mbps
    videoEncoder.videoCodecContext->rc_min_rate = 10000000; // 最小10Mbps

    // 品質設定（安定性重視）
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "quality", 90, 0); // 高品質だが安定

    // B-frame settings - disable for better stability
    videoEncoder.videoCodecContext->max_b_frames = 0; // B-frameを無効化して安定性向上

    // Profile settings - Main profile for better compatibility
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "profile", 77, 0); // 77: Main (より安定)

    // Hardware acceleration settings - use hardware encoding
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "hw_encoding", 1, 0);

    // 安定性向上のための追加設定
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "scenario", 0, 0); // 0: Default scenario
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "allow_frame_drops", 0, 0); // フレームドロップを無効

    printf("MF encoder settings: rate_control=1 (Quality VBR), bitrate=20Mbps, quality=90, profile=77 (Main), stability optimized\n");
    return true;
}

// H.264エンコーダーを設定する関数
static bool configureH264Encoder() {
    printf("Configuring libx264 encoder settings with CRF 6 (highest quality)\n");

    // 最高品質プリセット
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "veryslow", 0); // 最高品質プリセット

    // CRF 6（最高品質）
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "crf", "6", 0); // CRF 6（非常に高品質）

    // 品質向上設定
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "tune", "film", 0); // 実写映像用最適化

    // YouTube対応設定（最高品質）
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "profile", "high", 0);
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "level", "4.0", 0);

    // 高品質エンコーディング設定
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "me_method", "umh", 0); // 高品質動き推定
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "me_range", 24, 0); // 動き推定範囲拡大
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "subq", 10, 0); // サブピクセル動き推定最大
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "trellis", 2, 0); // トレリス量子化最大

    printf("libx264 settings: preset=veryslow, crf=6, tune=film (highest quality)\n");
    return true;
}

// 基本的なコーデックコンテキストを設定する関数
static void setBasicCodecContext(AVCodecContext* ctx) {
    ctx->width = WIDTH;
    ctx->height = HEIGHT;
    ctx->time_base = (AVRational){1, FPS};
    ctx->framerate = (AVRational){FPS, 1};
    ctx->gop_size = FPS / 2;
    ctx->pix_fmt = AV_PIX_FMT_YUV420P;
}

// コーデックを開く（フォールバック付き）
static bool tryOpenCodec(const AVCodec* codec) {
    AVDictionary* codec_opts = NULL;
    int ret = avcodec_open2(videoEncoder.videoCodecContext, codec, &codec_opts);
    av_dict_free(&codec_opts);

    if (ret >= 0) {
        return true; // 成功
    }

    // エラーログ
    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
    av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
    fprintf(stderr, "Could not open video codec: %s (err: %d)\n", errbuf, ret);

    // フォールバック: libx264を試す
    printf("Trying fallback to libx264 software encoder\n");
    avcodec_free_context(&videoEncoder.videoCodecContext);

    const AVCodec *fallbackCodec = avcodec_find_encoder_by_name("libx264");
    if (!fallbackCodec) {
        fprintf(stderr, "libx264 fallback encoder not found\n");
        return false;
    }

    videoEncoder.videoCodecContext = avcodec_alloc_context3(fallbackCodec);
    if (!videoEncoder.videoCodecContext) {
        fprintf(stderr, "Could not allocate fallback codec context\n");
        return false;
    }

    // 基本設定を適用
    setBasicCodecContext(videoEncoder.videoCodecContext);
    videoEncoder.videoCodecContext->max_b_frames = 1;

    // libx264設定（フォールバック用最高品質）
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "preset", "veryslow", 0); // 最高品質プリセット
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "crf", "6", 0); // CRF 6（最高品質）
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "tune", "film", 0); // 実写映像用最適化
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "profile", "high", 0);
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "level", "4.0", 0);

    // 高品質エンコーディング設定
    av_opt_set(videoEncoder.videoCodecContext->priv_data, "me_method", "umh", 0); // 高品質動き推定
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "subq", 10, 0); // サブピクセル動き推定最大
    av_opt_set_int(videoEncoder.videoCodecContext->priv_data, "trellis", 2, 0); // トレリス量子化最大

    ret = avcodec_open2(videoEncoder.videoCodecContext, fallbackCodec, NULL);
    if (ret < 0) {
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Could not open fallback codec: %s\n", errbuf);
        return false;
    }

    printf("Successfully opened libx264 fallback encoder\n");
    return true;
}

// オーディオ処理を設定する関数
static bool setupAudioProcessing(const char* audioFilePath, bool* hasAudio, float videoDuration, char* tempAudioFilePathForEncoder, bool* audioFileIsTemporary) {
    if (!*hasAudio || !audioFilePath || strlen(audioFilePath) == 0) {
        return true; // オーディオなしは正常
    }

    char currentAudioPath[MAX_PATH_LENGTH];

    // 日本語パス処理
    if (!handleJapaneseAudioPath(audioFilePath, currentAudioPath, tempAudioFilePathForEncoder, audioFileIsTemporary)) {
        *hasAudio = false;
        return true; // エラーでも続行
    }

    // オーディオファイル初期化
    if (!initializeAudioFile(currentAudioPath, tempAudioFilePathForEncoder, *audioFileIsTemporary)) {
        *hasAudio = false;
        return true; // エラーでも続行
    }

    // オーディオストリーム検索
    int audioStreamIndex = -1;
    AVStream *inputAudioStream = NULL;
    for (unsigned int k = 0; k < videoEncoder.audioFormatContext->nb_streams; k++) {
        if (videoEncoder.audioFormatContext->streams[k]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO) {
            audioStreamIndex = k;
            inputAudioStream = videoEncoder.audioFormatContext->streams[k];
            break;
        }
    }

    if (audioStreamIndex == -1 || !inputAudioStream) {
        fprintf(stderr, "Could not find audio stream\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, *audioFileIsTemporary);
        *hasAudio = false;
        return true; // エラーでも続行
    }

    // AACエンコーダー設定
    const AVCodec *audioCodec = avcodec_find_encoder(AV_CODEC_ID_AAC);
    if (!audioCodec) {
        fprintf(stderr, "AAC encoder not found\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, *audioFileIsTemporary);
        *hasAudio = false;
        return true; // エラーでも続行
    }

    if (!setupAudioEncoder(audioCodec, tempAudioFilePathForEncoder, *audioFileIsTemporary)) {
        *hasAudio = false;
        return true; // エラーでも続行
    }

    if (!setupAudioDecoder(inputAudioStream, videoDuration, tempAudioFilePathForEncoder, *audioFileIsTemporary)) {
        *hasAudio = false;
        return true; // エラーでも続行
    }

    printf("Audio stream configured: AAC, 192kbps, 48kHz, Stereo\n");
    printf("Video duration: %.2f seconds\n", videoDuration);
    return true;
}

// 日本語パス処理関数
static bool handleJapaneseAudioPath(const char* audioFilePath, char* currentAudioPath, char* tempAudioFilePathForEncoder, bool* audioFileIsTemporary) {
    if (!containsJapaneseChars(audioFilePath)) {
        strcpy(currentAudioPath, audioFilePath);
        return true;
    }

    printf("Warning: The audio file path contains Japanese characters, attempting to copy to a temporary path.\n");

    // 一時ディレクトリ取得
    wchar_t tempDirW[MAX_PATH_LENGTH];
    if (GetTempPathW(MAX_PATH_LENGTH, tempDirW) == 0) {
        wcscpy(tempDirW, L"C:\\temp\\");
        CreateDirectoryW(L"C:\\temp", NULL);
    }

    // パス変換
    wchar_t audioFilePathW[MAX_PATH_LENGTH];
    MultiByteToWideChar(CP_UTF8, 0, audioFilePath, -1, audioFilePathW, MAX_PATH_LENGTH);

    // 拡張子取得
    const char* extension = getFileExtension(audioFilePath);
    wchar_t extensionW[32] = L".tmp";
    if (extension && strlen(extension) > 0) {
        MultiByteToWideChar(CP_UTF8, 0, extension, -1, extensionW, 32);
    }

    // 一時ファイル名生成
    wchar_t tempAudioFileW[MAX_PATH_LENGTH];
    if (GetTempFileNameW(tempDirW, L"AUD", 0, tempAudioFileW) == 0) {
        _snwprintf(tempAudioFileW, MAX_PATH_LENGTH, L"%saudio_temp_%lu%s", tempDirW, GetTickCount(), extensionW);
    } else {
        DeleteFileW(tempAudioFileW);
        wchar_t tempFileNameOnly[MAX_PATH_LENGTH];
        _wsplitpath_s(tempAudioFileW, NULL, 0, NULL, 0, tempFileNameOnly, MAX_PATH_LENGTH, NULL, 0);
        _snwprintf(tempAudioFileW, MAX_PATH_LENGTH, L"%s%s%s", tempDirW, tempFileNameOnly, extensionW);
    }

    // パス変換（戻し）
    char tempAudioFileA[MAX_PATH_LENGTH];
    WideCharToMultiByte(CP_UTF8, 0, tempAudioFileW, -1, tempAudioFileA, MAX_PATH_LENGTH, NULL, NULL);
    printf("Copying audio file to temporary location: %s\n", tempAudioFileA);

    // ファイルコピー
    if (!CopyFileW(audioFilePathW, tempAudioFileW, FALSE)) {
        fprintf(stderr, "Failed to copy audio file to temporary path. Error code: %lu\n", GetLastError());
        return false;
    }

    printf("Audio file copied successfully.\n");
    strcpy(currentAudioPath, tempAudioFileA);
    strcpy(tempAudioFilePathForEncoder, tempAudioFileA);
    *audioFileIsTemporary = true;
    return true;
}

// オーディオファイル初期化関数
static bool initializeAudioFile(const char* currentAudioPath, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary) {
    printf("Opening audio file: %s\n", currentAudioPath);

    int ret = avformat_open_input(&videoEncoder.audioFormatContext, currentAudioPath, NULL, NULL);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Could not open audio file %s: %s (error code: %d)\n", currentAudioPath, errbuf, ret);
        if (audioFileIsTemporary) DeleteFileA(tempAudioFilePathForEncoder);
        return false;
    }

    printf("Successfully opened audio file: %s\n", currentAudioPath);

    if (avformat_find_stream_info(videoEncoder.audioFormatContext, NULL) < 0) {
        fprintf(stderr, "Could not find audio stream info in %s\n", currentAudioPath);
        avformat_close_input(&videoEncoder.audioFormatContext);
        videoEncoder.audioFormatContext = NULL;
        if (audioFileIsTemporary) DeleteFileA(tempAudioFilePathForEncoder);
        return false;
    }

    return true;
}

// オーディオエンコーダー設定関数
static bool setupAudioEncoder(const AVCodec* audioCodec, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary) {
    // オーディオエンコーダーコンテキストを作成
    videoEncoder.audioCodecContext = avcodec_alloc_context3(audioCodec);
    if (!videoEncoder.audioCodecContext) {
        fprintf(stderr, "Could not allocate audio codec context\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // AAC エンコーダー設定（192kbps）
    videoEncoder.audioCodecContext->codec_id = AV_CODEC_ID_AAC;
    videoEncoder.audioCodecContext->bit_rate = 192000; // 192kbps
    videoEncoder.audioCodecContext->sample_rate = 48000; // YouTube推奨
    videoEncoder.audioCodecContext->sample_fmt = AV_SAMPLE_FMT_FLTP; // AAC推奨フォーマット

    // ステレオ設定
    av_channel_layout_default(&videoEncoder.audioCodecContext->ch_layout, 2);

    // プロファイル設定（YouTube対応）
    videoEncoder.audioCodecContext->profile = FF_PROFILE_AAC_LOW;

    // 時間ベース設定
    videoEncoder.audioCodecContext->time_base = (AVRational){1, videoEncoder.audioCodecContext->sample_rate};

    printf("Configuring AAC encoder: 192kbps, 48kHz, Stereo, AAC-LC profile\n");

    // エンコーダーを開く
    int ret = avcodec_open2(videoEncoder.audioCodecContext, audioCodec, NULL);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Could not open AAC encoder: %s\n", errbuf);
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // 出力ストリームを作成
    videoEncoder.audioStream = avformat_new_stream(videoEncoder.formatContext, NULL);
    if (!videoEncoder.audioStream) {
        fprintf(stderr, "Could not allocate output audio stream\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // エンコーダーパラメータを出力ストリームにコピー
    ret = avcodec_parameters_from_context(videoEncoder.audioStream->codecpar, videoEncoder.audioCodecContext);
    if (ret < 0) {
        fprintf(stderr, "Could not copy audio encoder parameters\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    videoEncoder.audioStream->time_base = videoEncoder.audioCodecContext->time_base;
    videoEncoder.audioStream->codecpar->codec_tag = 0;

    return true;
}

// オーディオデコーダー設定関数
static bool setupAudioDecoder(AVStream* inputAudioStream, float videoDuration, char* tempAudioFilePathForEncoder, bool audioFileIsTemporary) {
    // 映像の長さを設定
    videoEncoder.videoDuration = videoDuration;

    // オーディオファイルの長さを取得
    double audioDuration = 0.0;
    if (videoEncoder.audioFormatContext->duration != AV_NOPTS_VALUE) {
        audioDuration = (double)videoEncoder.audioFormatContext->duration / AV_TIME_BASE;
    } else if (inputAudioStream->duration != AV_NOPTS_VALUE) {
        audioDuration = (double)inputAudioStream->duration * av_q2d(inputAudioStream->time_base);
    }

    if (audioDuration > 0) {
        printf("Audio file duration: %.2f seconds, Video duration: %.2f seconds\n", audioDuration, videoDuration);
        if (audioDuration < videoDuration) {
            printf("Audio is shorter than video, will loop audio to match video length\n");
        }
    } else {
        printf("Could not determine audio duration, will loop as needed\n");
    }

    // オーディオデコーダーを設定
    const AVCodec *audioDecoder = avcodec_find_decoder(inputAudioStream->codecpar->codec_id);
    if (!audioDecoder) {
        fprintf(stderr, "Audio decoder not found\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    videoEncoder.audioDecoderContext = avcodec_alloc_context3(audioDecoder);
    if (!videoEncoder.audioDecoderContext) {
        fprintf(stderr, "Could not allocate audio decoder context\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // デコーダーパラメータを設定
    int ret = avcodec_parameters_to_context(videoEncoder.audioDecoderContext, inputAudioStream->codecpar);
    if (ret < 0) {
        fprintf(stderr, "Could not copy audio decoder parameters\n");
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // デコーダーを開く
    ret = avcodec_open2(videoEncoder.audioDecoderContext, audioDecoder, NULL);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        fprintf(stderr, "Could not open audio decoder: %s\n", errbuf);
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    // オーディオフレームを割り当て
    videoEncoder.audioFrame = av_frame_alloc();
    videoEncoder.audioFrameResampled = av_frame_alloc();
    if (!videoEncoder.audioFrame || !videoEncoder.audioFrameResampled) {
        fprintf(stderr, "Could not allocate audio frames\n");
        if (videoEncoder.audioFrame) av_frame_free(&videoEncoder.audioFrame);
        if (videoEncoder.audioFrameResampled) av_frame_free(&videoEncoder.audioFrameResampled);
        cleanupAudioOnError(tempAudioFilePathForEncoder, audioFileIsTemporary);
        return false;
    }

    return true;
}

// オーディオエラー時のクリーンアップ関数
static void cleanupAudioOnError(char* tempAudioFilePathForEncoder, bool audioFileIsTemporary) {
    if (videoEncoder.audioCodecContext) {
        avcodec_free_context(&videoEncoder.audioCodecContext);
        videoEncoder.audioCodecContext = NULL;
    }
    if (videoEncoder.audioDecoderContext) {
        avcodec_free_context(&videoEncoder.audioDecoderContext);
        videoEncoder.audioDecoderContext = NULL;
    }
    if (videoEncoder.audioFormatContext) {
        avformat_close_input(&videoEncoder.audioFormatContext);
        videoEncoder.audioFormatContext = NULL;
    }
    if (videoEncoder.audioFrame) {
        av_frame_free(&videoEncoder.audioFrame);
        videoEncoder.audioFrame = NULL;
    }
    if (videoEncoder.audioFrameResampled) {
        av_frame_free(&videoEncoder.audioFrameResampled);
        videoEncoder.audioFrameResampled = NULL;
    }
    if (audioFileIsTemporary && tempAudioFilePathForEncoder && tempAudioFilePathForEncoder[0] != '\0') {
        DeleteFileA(tempAudioFilePathForEncoder);
        tempAudioFilePathForEncoder[0] = '\0';
    }
}