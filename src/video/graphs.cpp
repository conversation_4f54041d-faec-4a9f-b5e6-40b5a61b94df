#include "../../include/graphs.h"
#include "../../include/utils/color.h" // チャンネルカラー用
#include "../../include/utils/config_manager.h" // 色設定の管理用
#include "../../include/font_manager.h" // フォント関連
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <vector>
#include <iostream> // std::cout のために追加
#include "../../include/skia/include/core/SkFont.h"

// グラフの位置情報
GraphPositions graphPos;

// グロー効果のパラメータ（最適化版）
#define GLOW_LAYERS 3        // グロー効果の層の数（最適化のため減らす）
#define GLOW_BASE_SIZE 3.0f  // 基本サイズ
#define GLOW_MAX_SIZE 5.0f  // 最大サイズ（少し小さくする）
#define GLOW_BASE_ALPHA 0.3f // 基本透明度

// --- Notes Graph Variables ---
static float graph_history_notes[GRAPH_HISTORY_LENGTH] = {0};
static int graph_history_index_notes = 0;
static float graph_current_notes = 0.0f;
static float graph_total_notes = 0.0f;
static float graph_update_timer_notes = 0.0f;

// --- NPS Graph Variables ---
static float graph_history_nps[GRAPH_HISTORY_LENGTH] = {0};
static int graph_history_index_nps = 0;
static float graph_current_nps = 0.0f;
static float graph_min_nps = 0.0f;
static float graph_max_nps = 10.0f;
static float target_graph_min_nps = 0.0f;
static float target_graph_max_nps = 10.0f;
static float graph_update_timer_nps = 0.0f;

// --- Polyphony Graph Variables ---
static float graph_history_poly[GRAPH_HISTORY_LENGTH] = {0};
static int graph_history_index_poly = 0;
static float graph_current_poly = 0.0f;
static float graph_min_poly = 0.0f;
static float graph_max_poly = 50.0f;
static float target_graph_min_poly = 0.0f;
static float target_graph_max_poly = 50.0f;
static float graph_update_timer_poly = 0.0f;

// --- BPM Graph Variables ---
static float graph_history_bpm[GRAPH_HISTORY_LENGTH] = {0};
static int graph_history_index_bpm = 0;
static float graph_current_bpm = 0.0f;
static float graph_min_bpm = 60.0f;
static float graph_max_bpm = 180.0f;
static float target_graph_min_bpm = 60.0f;
static float target_graph_max_bpm = 180.0f;
static float graph_update_timer_bpm = 0.0f;

// --- Pan Graph Variables ---
static float graph_history_pan[16][GRAPH_HISTORY_LENGTH] = {{0}}; // 16チャンネル分
static int graph_history_index_pan = 0;
static float graph_update_timer_pan = 0.0f;

// --- Pitch Bend Graph Variables ---
static float graph_history_pb[16][GRAPH_HISTORY_LENGTH] = {{0}}; // 16チャンネル分
static int graph_history_index_pb = 0;
static float graph_update_timer_pb = 0.0f;

// --- Volume Graph Variables (CC#7) ---
static float graph_history_volume[16][GRAPH_HISTORY_LENGTH] = {{0}};
static int graph_history_index_volume = 0;
static float graph_update_timer_volume = 0.0f;

// --- Sustain Graph Variables (CC#64) ---
static float graph_history_sustain[16][GRAPH_HISTORY_LENGTH] = {{0}};
static int graph_history_index_sustain = 0;
static float graph_update_timer_sustain = 0.0f;

// --- Filter Cutoff Graph Variables (CC#74) ---
static float graph_history_filter_cutoff[16][GRAPH_HISTORY_LENGTH] = {{0}};
static int graph_history_index_filter_cutoff = 0;
static float graph_update_timer_filter_cutoff = 0.0f;

// --- Filter Resonance Graph Variables (CC#71) ---
static float graph_history_filter_resonance[16][GRAPH_HISTORY_LENGTH] = {{0}};
static int graph_history_index_filter_resonance = 0;
static float graph_update_timer_filter_resonance = 0.0f;

// --- Release Time Graph Variables (CC#72) ---
static float graph_history_release_time[16][GRAPH_HISTORY_LENGTH] = {{0}};
static int graph_history_index_release_time = 0;
static float graph_update_timer_release_time = 0.0f;

// グラフの位置とサイズを更新する関数（動的レイアウト対応）
void updateGraphPositions(int width, int height) {
    // ConfigManagerから設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();
    // 基本レイアウト設定
    int graphWidth = width / 4;  // 画面幅の1/4
    int graphHeight = height / 12; // 画面高さの1/12
    int graphSpacing = 8; // グラフ間の間隔
    int columnSpacing = 10; // 列間の間隔

    // 上段グラフ（右列）の表示状態をチェック
    bool hasRightColumnGraphs = visualConfig.graphs.showNotes ||
                               visualConfig.graphs.showNps ||
                               visualConfig.graphs.showPolyphony ||
                               visualConfig.graphs.showBpm ||
                               visualConfig.graphs.showPan ||
                               visualConfig.graphs.showPitchBend;

    // 列の位置を決定
    int graphX1, graphX2;
    if (hasRightColumnGraphs) {
        // 通常の2列レイアウト
        graphX1 = width - (graphWidth * 2) - columnSpacing - 20; // 左列
        graphX2 = width - graphWidth - 20; // 右列
    } else {
        // 右列のグラフがすべて非表示の場合、左列のグラフを右側に移動
        graphX1 = width - graphWidth - 20; // 左列を右列の位置に移動
        graphX2 = width - graphWidth - 20; // 右列（使用されない）
    }

    // 第1列のグラフ（CC系グラフ）- 動的レイアウト
    int currentY1 = 20; // 開始Y位置

    // Volumeグラフ (CC#7)
    if (visualConfig.graphs.showVolume) {
        graphPos.x_volume = graphX1;
        graphPos.y_volume = currentY1;
        graphPos.width_volume = graphWidth;
        graphPos.height_volume = graphHeight;
        currentY1 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_volume = -1000;
        graphPos.y_volume = -1000;
        graphPos.width_volume = 0;
        graphPos.height_volume = 0;
    }

    // Sustainグラフ (CC#64)
    if (visualConfig.graphs.showSustain) {
        graphPos.x_sustain = graphX1;
        graphPos.y_sustain = currentY1;
        graphPos.width_sustain = graphWidth;
        graphPos.height_sustain = graphHeight;
        currentY1 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_sustain = -1000;
        graphPos.y_sustain = -1000;
        graphPos.width_sustain = 0;
        graphPos.height_sustain = 0;
    }

    // Filter Cutoffグラフ (CC#74)
    if (visualConfig.graphs.showFilterCutoff) {
        graphPos.x_filter_cutoff = graphX1;
        graphPos.y_filter_cutoff = currentY1;
        graphPos.width_filter_cutoff = graphWidth;
        graphPos.height_filter_cutoff = graphHeight;
        currentY1 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_filter_cutoff = -1000;
        graphPos.y_filter_cutoff = -1000;
        graphPos.width_filter_cutoff = 0;
        graphPos.height_filter_cutoff = 0;
    }

    // Filter Resonanceグラフ (CC#71)
    if (visualConfig.graphs.showFilterResonance) {
        graphPos.x_filter_resonance = graphX1;
        graphPos.y_filter_resonance = currentY1;
        graphPos.width_filter_resonance = graphWidth;
        graphPos.height_filter_resonance = graphHeight;
        currentY1 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_filter_resonance = -1000;
        graphPos.y_filter_resonance = -1000;
        graphPos.width_filter_resonance = 0;
        graphPos.height_filter_resonance = 0;
    }

    // Release Timeグラフ (CC#72)
    if (visualConfig.graphs.showReleaseTime) {
        graphPos.x_release_time = graphX1;
        graphPos.y_release_time = currentY1;
        graphPos.width_release_time = graphWidth;
        graphPos.height_release_time = graphHeight;
        currentY1 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_release_time = -1000;
        graphPos.y_release_time = -1000;
        graphPos.width_release_time = 0;
        graphPos.height_release_time = 0;
    }

    // 第2列のグラフ（メイングラフ）- 動的レイアウト
    int currentY2 = 20; // 開始Y位置

    // ノートグラフ
    if (visualConfig.graphs.showNotes) {
        graphPos.x_notes = graphX2;
        graphPos.y_notes = currentY2;
        graphPos.width_notes = graphWidth;
        graphPos.height_notes = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_notes = -1000;
        graphPos.y_notes = -1000;
        graphPos.width_notes = 0;
        graphPos.height_notes = 0;
    }

    // NPSグラフ
    if (visualConfig.graphs.showNps) {
        graphPos.x_nps = graphX2;
        graphPos.y_nps = currentY2;
        graphPos.width_nps = graphWidth;
        graphPos.height_nps = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_nps = -1000;
        graphPos.y_nps = -1000;
        graphPos.width_nps = 0;
        graphPos.height_nps = 0;
    }

    // Polyphonyグラフ
    if (visualConfig.graphs.showPolyphony) {
        graphPos.x_poly = graphX2;
        graphPos.y_poly = currentY2;
        graphPos.width_poly = graphWidth;
        graphPos.height_poly = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_poly = -1000;
        graphPos.y_poly = -1000;
        graphPos.width_poly = 0;
        graphPos.height_poly = 0;
    }



    // BPMグラフ
    if (visualConfig.graphs.showBpm) {
        graphPos.x_bpm = graphX2;
        graphPos.y_bpm = currentY2;
        graphPos.width_bpm = graphWidth;
        graphPos.height_bpm = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_bpm = -1000;
        graphPos.y_bpm = -1000;
        graphPos.width_bpm = 0;
        graphPos.height_bpm = 0;
    }

    // Panグラフ
    if (visualConfig.graphs.showPan) {
        graphPos.x_pan = graphX2;
        graphPos.y_pan = currentY2;
        graphPos.width_pan = graphWidth;
        graphPos.height_pan = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_pan = -1000;
        graphPos.y_pan = -1000;
        graphPos.width_pan = 0;
        graphPos.height_pan = 0;
    }

    // Pitch Bendグラフ
    if (visualConfig.graphs.showPitchBend) {
        graphPos.x_pb = graphX2;
        graphPos.y_pb = currentY2;
        graphPos.width_pb = graphWidth;
        graphPos.height_pb = graphHeight;
        currentY2 += graphHeight + graphSpacing;
    } else {
        // 非表示の場合は画面外に移動
        graphPos.x_pb = -1000;
        graphPos.y_pb = -1000;
        graphPos.width_pb = 0;
        graphPos.height_pb = 0;
    }
}

// グラフ初期化関数
void initGraphs(int width, int height) {
    // グラフの位置とサイズを初期化
    updateGraphPositions(width, height);

    // グラフデータの初期化
    memset(graph_history_notes, 0, sizeof(graph_history_notes));
    memset(graph_history_nps, 0, sizeof(graph_history_nps));
    memset(graph_history_poly, 0, sizeof(graph_history_poly));
    memset(graph_history_bpm, 0, sizeof(graph_history_bpm));
    memset(graph_history_pan, 0, sizeof(graph_history_pan));
    memset(graph_history_pb, 0, sizeof(graph_history_pb));
    memset(graph_history_volume, 0, sizeof(graph_history_volume));
    memset(graph_history_sustain, 0, sizeof(graph_history_sustain));
    memset(graph_history_filter_cutoff, 0, sizeof(graph_history_filter_cutoff));
    memset(graph_history_filter_resonance, 0, sizeof(graph_history_filter_resonance));
    memset(graph_history_release_time, 0, sizeof(graph_history_release_time));

    graph_history_index_notes = 0;
    graph_history_index_nps = 0;
    graph_history_index_poly = 0;
    graph_history_index_bpm = 0;
    graph_history_index_pan = 0;
    graph_history_index_pb = 0;
    graph_history_index_volume = 0;
    graph_history_index_sustain = 0;
    graph_history_index_filter_cutoff = 0;
    graph_history_index_filter_resonance = 0;
    graph_history_index_release_time = 0;

    graph_current_notes = 0.0f;
    graph_total_notes = 0.0f;
    graph_current_nps = 0.0f;
    graph_current_poly = 0.0f;
    graph_current_bpm = 0.0f;

    graph_min_nps = 0.0f;
    graph_max_nps = 10.0f;
    target_graph_min_nps = 0.0f;
    target_graph_max_nps = 10.0f;

    graph_min_poly = 0.0f;
    graph_max_poly = 50.0f;
    target_graph_min_poly = 0.0f;
    target_graph_max_poly = 50.0f;

    graph_min_bpm = 60.0f;
    graph_max_bpm = 180.0f;
    target_graph_min_bpm = 60.0f;
    target_graph_max_bpm = 180.0f;

    graph_update_timer_notes = 0.0f;
    graph_update_timer_nps = 0.0f;
    graph_update_timer_poly = 0.0f;
    graph_update_timer_bpm = 0.0f;
    graph_update_timer_pan = 0.0f;
    graph_update_timer_pb = 0.0f;
    graph_update_timer_volume = 0.0f;
    graph_update_timer_sustain = 0.0f;
    graph_update_timer_filter_cutoff = 0.0f;
    graph_update_timer_filter_resonance = 0.0f;
    graph_update_timer_release_time = 0.0f;
}

// グラフ更新関数
void updateGraphs(float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                 uint32_t currentNps, uint16_t currentPolyphony) {
    // フレーム時間（1/60秒と仮定）
    float dt = 1.0f / 60.0f;

    // 各タイマーを更新
    graph_update_timer_notes += dt;
    graph_update_timer_nps += dt;
    graph_update_timer_poly += dt;
    graph_update_timer_bpm += dt;
    graph_update_timer_pan += dt;
    graph_update_timer_pb += dt;
    graph_update_timer_volume += dt;
    graph_update_timer_sustain += dt;
    graph_update_timer_filter_cutoff += dt;
    graph_update_timer_filter_resonance += dt;
    graph_update_timer_release_time += dt;

    // 現在の値を更新
    graph_current_notes = (float)currentNotes;
    graph_total_notes = (float)totalNotes;
    graph_current_nps = (float)currentNps;
    graph_current_poly = (float)currentPolyphony;
    graph_current_bpm = getCurrentBPM();

    // NPSグラフの最小値と最大値を動的に計算
    float min_nps = graph_history_nps[0];
    float max_nps = graph_history_nps[0];

    // 履歴データから最小値と最大値を計算
    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int idx = (graph_history_index_nps + i) % GRAPH_HISTORY_LENGTH;
        if (graph_history_nps[idx] < min_nps) min_nps = graph_history_nps[idx];
        if (graph_history_nps[idx] > max_nps) max_nps = graph_history_nps[idx];
    }

    // 現在の値も考慮
    if (graph_current_nps < min_nps) min_nps = graph_current_nps;
    if (graph_current_nps > max_nps) max_nps = graph_current_nps;

    // 目標範囲に余裕を持たせる
    target_graph_min_nps = fmaxf(0.0f, min_nps - (max_nps - min_nps) * 0.1f);
    target_graph_max_nps = max_nps + (max_nps - min_nps) * 0.1f;

    // 最小範囲を確保（少なくとも5.0の範囲）
    if (target_graph_max_nps - target_graph_min_nps < 5.0f) {
        float mid = (target_graph_max_nps + target_graph_min_nps) / 2.0f;
        target_graph_min_nps = fmaxf(0.0f, mid - 2.5f);
        target_graph_max_nps = target_graph_min_nps + 5.0f;
    }

    // Polyphonyグラフの最小値と最大値を動的に計算
    float min_poly = graph_history_poly[0];
    float max_poly = graph_history_poly[0];

    // 履歴データから最小値と最大値を計算
    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int idx = (graph_history_index_poly + i) % GRAPH_HISTORY_LENGTH;
        if (graph_history_poly[idx] < min_poly) min_poly = graph_history_poly[idx];
        if (graph_history_poly[idx] > max_poly) max_poly = graph_history_poly[idx];
    }

    // 現在の値も考慮
    if (graph_current_poly < min_poly) min_poly = graph_current_poly;
    if (graph_current_poly > max_poly) max_poly = graph_current_poly;

    // 目標範囲に余裕を持たせる
    target_graph_min_poly = 0.0f; // Polyphonyは0から始まる
    target_graph_max_poly = max_poly + fmaxf(5.0f, max_poly * 0.1f); // 余裕を持たせる、少なくとも5

    // 最小範囲を確保（少なくとも10の範囲）
    if (target_graph_max_poly - target_graph_min_poly < 10.0f) {
        target_graph_max_poly = target_graph_min_poly + 10.0f;
    }

    // BPMグラフの最小値と最大値を動的に計算
    float min_bpm = graph_history_bpm[0];
    float max_bpm = graph_history_bpm[0];

    // 履歴データから最小値と最大値を計算
    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int idx = (graph_history_index_bpm + i) % GRAPH_HISTORY_LENGTH;
        if (graph_history_bpm[idx] < min_bpm) min_bpm = graph_history_bpm[idx];
        if (graph_history_bpm[idx] > max_bpm) max_bpm = graph_history_bpm[idx];
    }

    // 現在の値も考慮
    if (graph_current_bpm < min_bpm) min_bpm = graph_current_bpm;
    if (graph_current_bpm > max_bpm) max_bpm = graph_current_bpm;

    // 目標範囲に余裕を持たせる
    target_graph_min_bpm = fmaxf(0.0f, min_bpm - (max_bpm - min_bpm) * 0.1f);
    target_graph_max_bpm = max_bpm + (max_bpm - min_bpm) * 0.1f;

    // 最小範囲を確保（少なくとも30の範囲）
    if (target_graph_max_bpm - target_graph_min_bpm < 30.0f) {
        float mid = (target_graph_max_bpm + target_graph_min_bpm) / 2.0f;
        target_graph_min_bpm = fmaxf(0.0f, mid - 15.0f);
        target_graph_max_bpm = target_graph_min_bpm + 30.0f;
    }

    // グラフの最小値と最大値をスムーズに更新
    graph_min_nps += (target_graph_min_nps - graph_min_nps) * GRAPH_SCALE_SMOOTH_FACTOR * dt;
    graph_max_nps += (target_graph_max_nps - graph_max_nps) * GRAPH_SCALE_SMOOTH_FACTOR * dt;

    graph_min_poly += (target_graph_min_poly - graph_min_poly) * GRAPH_SCALE_SMOOTH_FACTOR * dt;
    graph_max_poly += (target_graph_max_poly - graph_max_poly) * GRAPH_SCALE_SMOOTH_FACTOR * dt;

    graph_min_bpm += (target_graph_min_bpm - graph_min_bpm) * GRAPH_SCALE_SMOOTH_FACTOR * dt;
    graph_max_bpm += (target_graph_max_bpm - graph_max_bpm) * GRAPH_SCALE_SMOOTH_FACTOR * dt;

    // 履歴を更新（一定間隔で）
    while (graph_update_timer_notes >= GRAPH_UPDATE_INTERVAL) {
        graph_history_notes[graph_history_index_notes] = graph_current_notes;
        graph_history_index_notes = (graph_history_index_notes + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_notes -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_nps >= GRAPH_UPDATE_INTERVAL) {
        graph_history_nps[graph_history_index_nps] = graph_current_nps;
        graph_history_index_nps = (graph_history_index_nps + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_nps -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_poly >= GRAPH_UPDATE_INTERVAL) {
        graph_history_poly[graph_history_index_poly] = graph_current_poly;
        graph_history_index_poly = (graph_history_index_poly + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_poly -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_bpm >= GRAPH_UPDATE_INTERVAL) {
        graph_history_bpm[graph_history_index_bpm] = graph_current_bpm;
        graph_history_index_bpm = (graph_history_index_bpm + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_bpm -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_pan >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのパン値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_pan[ch][graph_history_index_pan] = (float)getCurrentPan(ch);
        }
        graph_history_index_pan = (graph_history_index_pan + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_pan -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_pb >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのピッチベンド値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_pb[ch][graph_history_index_pb] = (float)getCurrentPitchBend(ch);
        }
        graph_history_index_pb = (graph_history_index_pb + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_pb -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_volume >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのボリューム値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_volume[ch][graph_history_index_volume] = (float)getCurrentVolume(ch);
        }
        graph_history_index_volume = (graph_history_index_volume + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_volume -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_sustain >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのサステイン値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_sustain[ch][graph_history_index_sustain] = (float)getCurrentSustain(ch);
        }
        graph_history_index_sustain = (graph_history_index_sustain + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_sustain -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_filter_cutoff >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのフィルターカットオフ値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_filter_cutoff[ch][graph_history_index_filter_cutoff] = (float)getCurrentFilterCutoff(ch);
        }
        graph_history_index_filter_cutoff = (graph_history_index_filter_cutoff + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_filter_cutoff -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_filter_resonance >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのフィルターレゾナンス値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_filter_resonance[ch][graph_history_index_filter_resonance] = (float)getCurrentFilterResonance(ch);
        }
        graph_history_index_filter_resonance = (graph_history_index_filter_resonance + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_filter_resonance -= GRAPH_UPDATE_INTERVAL;
    }

    while (graph_update_timer_release_time >= GRAPH_UPDATE_INTERVAL) {
        // 各チャンネルのリリースタイム値を更新
        for (int ch = 0; ch < 16; ch++) {
            graph_history_release_time[ch][graph_history_index_release_time] = (float)getCurrentReleaseTime(ch);
        }
        graph_history_index_release_time = (graph_history_index_release_time + 1) % GRAPH_HISTORY_LENGTH;
        graph_update_timer_release_time -= GRAPH_UPDATE_INTERVAL;
    }
}

// 最適化されたグラフ線描画関数
void drawGraphLine(SkCanvas* canvas, float x1, float y1, float x2, float y2,
                   float r, float g, float b, float a) {
    // 静的な初期化を削除し、毎回新しいペイントオブジェクトを作成
    SkPaint linePaint;
    linePaint.setStyle(SkPaint::kStroke_Style);
    linePaint.setStrokeWidth(GRAPH_LINE_THICKNESS);
    linePaint.setAntiAlias(true);
    linePaint.setStrokeCap(SkPaint::kRound_Cap);
    linePaint.setStrokeJoin(SkPaint::kRound_Join);
    
    // 色を設定
    linePaint.setColor(SkColorSetARGB((int)(a * 255), (int)(r * 255), (int)(g * 255), (int)(b * 255)));
    canvas->drawLine(x1, y1, x2, y2, linePaint);
}

// 共通のグラフ背景描画関数
static void drawGraphBackground(SkCanvas* canvas, float x, float y, float width, float height, const char* title) {
    // ConfigManagerから色を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    SkPaint bgPaint;
    SkPaint borderPaint;
    SkPaint textPaint;
    
    // 背景ペイント - グラフ背景色を使用
    uint32_t bgColor = colorConfig.graphBackgroundColor;
    uint8_t bgr = (bgColor >> 16) & 0xFF;
    uint8_t bgg = (bgColor >> 8) & 0xFF;
    uint8_t bgb = bgColor & 0xFF;
    uint8_t bga = 204; // 80%不透明度
    bgPaint.setColor(SkColorSetARGB(bga, bgr, bgg, bgb)); 
    
    // ボーダーペイント - ボーダー色を使用
    uint32_t borderColor = colorConfig.borderColor;
    uint8_t br = (borderColor >> 16) & 0xFF;
    uint8_t bg = (borderColor >> 8) & 0xFF;
    uint8_t bb = borderColor & 0xFF;
    uint8_t ba = 255; // 完全不透明
    borderPaint.setColor(SkColorSetARGB(ba, br, bg, bb));
    borderPaint.setStyle(SkPaint::kStroke_Style);
    borderPaint.setStrokeWidth(1.0f);
    
    // テキストペイント - グラフテキスト色を使用
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    uint8_t ta = 255; // 完全不透明
    textPaint.setColor(SkColorSetARGB(ta, tr, tg, tb));
    textPaint.setAntiAlias(true);
    
    SkFont font = setFont(FONT_DEFAULT, 16);

    // 背景を描画
    canvas->drawRect(SkRect::MakeXYWH(x, y, width, height), bgPaint);

    // ボーダーを描画
    canvas->drawRect(SkRect::MakeXYWH(x, y, width, height), borderPaint);

    // タイトルを描画
    if (title) {
        canvas->drawString(title, x + 10, y + 20, font, textPaint);
    }
}

// 最適化されたグラフ描画関数
void drawGraphs(SkCanvas* canvas) {
    if (!canvas) return;

    // 動的レイアウトを更新（画面サイズは固定値を使用、実際の実装では適切な値を取得）
    updateGraphPositions(1920, 1080);

    // ConfigManagerから色とvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    const VisualConfig& visualConfig = configManager->getVisualConfig();
    
    // 共通ペイントオブジェクト
    SkPaint bgPaint;
    SkPaint borderPaint;
    SkPaint textPaint;
    
    // 背景ペイント - グラフ背景色を使用
    uint32_t bgColor = colorConfig.graphBackgroundColor;
    uint8_t bgr = (bgColor >> 16) & 0xFF;
    uint8_t bgg = (bgColor >> 8) & 0xFF;
    uint8_t bgb = bgColor & 0xFF;
    uint8_t bga = 204; // 80%不透明度
    bgPaint.setColor(SkColorSetARGB(bga, bgr, bgg, bgb));
    
    // ボーダーペイント - ボーダー色を使用
    uint32_t borderColor = colorConfig.borderColor;
    uint8_t br = (borderColor >> 16) & 0xFF;
    uint8_t bg = (borderColor >> 8) & 0xFF;
    uint8_t bb = borderColor & 0xFF;
    uint8_t ba = 255; // 完全不透明
    borderPaint.setColor(SkColorSetARGB(ba, br, bg, bb));
    borderPaint.setStyle(SkPaint::kStroke_Style);
    borderPaint.setStrokeWidth(1.0f);
    
    // テキストペイント - グラフテキスト色を使用
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    uint8_t ta = 255; // 完全不透明
    textPaint.setColor(SkColorSetARGB(ta, tr, tg, tb));
    textPaint.setAntiAlias(true);

    // 各グラフを描画（個別設定に基づく）
    if (visualConfig.graphs.showNotes) {
        drawGraphNotes(canvas);
    }
    if (visualConfig.graphs.showNps) {
        drawGraphNPS(canvas);
    }
    if (visualConfig.graphs.showPolyphony) {
        drawGraphPolyphony(canvas);
    }
    // その他のグラフも設定に基づいて表示
    if (visualConfig.graphs.showBpm) {
        drawGraphBPM(canvas);
    }
    if (visualConfig.graphs.showPan) {
        drawGraphPan(canvas);
    }
    if (visualConfig.graphs.showPitchBend) {
        drawGraphPitchBend(canvas);
    }
    if (visualConfig.graphs.showVolume) {
        drawGraphVolume(canvas);
    }
    if (visualConfig.graphs.showSustain) {
        drawGraphSustain(canvas);
    }
    if (visualConfig.graphs.showFilterCutoff) {
        drawGraphFilterCutoff(canvas);
    }
    if (visualConfig.graphs.showFilterResonance) {
        drawGraphFilterResonance(canvas);
    }
    if (visualConfig.graphs.showReleaseTime) {
        drawGraphReleaseTime(canvas);
    }
}

// ノートグラフ描画関数
void drawGraphNotes(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_notes, graphPos.y_notes,
                        graphPos.width_notes, graphPos.height_notes, "Notes");

    // ConfigManagerから色を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();

    // テキストペイント - グラフテキスト色を使用
    SkPaint textPaint;
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    textPaint.setColor(SkColorSetARGB(255, tr, tg, tb));
    textPaint.setAntiAlias(true);

    // フォントを設定
    SkFont font = setFont(FONT_DEFAULT, 16);

    // 現在のノート数と総ノート数を表示（右端に配置）
    char noteText[64];

    // 整数値に変換
    int currentInt = (int)graph_current_notes;
    int totalInt = (int)graph_total_notes;

    char currentStr[32], totalStr[32];

    // 現在のノート数をカンマ区切りに変換
    if (currentInt >= 1000) {
        char tempBuffer[32];
        snprintf(tempBuffer, sizeof(tempBuffer), "%d", currentInt);

        int length = strlen(tempBuffer);
        int commaCount = (length - 1) / 3;
        int resultLength = length + commaCount;

        currentStr[resultLength] = '\0';
        int tempIndex = length - 1;
        int resultIndex = resultLength - 1;
        int count = 0;

        while (tempIndex >= 0) {
            currentStr[resultIndex--] = tempBuffer[tempIndex--];
            count++;
            if (count % 3 == 0 && tempIndex >= 0) {
                currentStr[resultIndex--] = ',';
            }
        }
    } else {
        snprintf(currentStr, sizeof(currentStr), "%d", currentInt);
    }

    // 総ノート数をカンマ区切りに変換
    if (totalInt >= 1000) {
        char tempBuffer[32];
        snprintf(tempBuffer, sizeof(tempBuffer), "%d", totalInt);

        int length = strlen(tempBuffer);
        int commaCount = (length - 1) / 3;
        int resultLength = length + commaCount;

        totalStr[resultLength] = '\0';
        int tempIndex = length - 1;
        int resultIndex = resultLength - 1;
        int count = 0;

        while (tempIndex >= 0) {
            totalStr[resultIndex--] = tempBuffer[tempIndex--];
            count++;
            if (count % 3 == 0 && tempIndex >= 0) {
                totalStr[resultIndex--] = ',';
            }
        }
    } else {
        snprintf(totalStr, sizeof(totalStr), "%d", totalInt);
    }

    // 最終的な表示文字列を作成
    snprintf(noteText, sizeof(noteText), "%s / %s", currentStr, totalStr);

    // テキストの幅を計測
    SkRect bounds;
    font.measureText(noteText, strlen(noteText), SkTextEncoding::kUTF8, &bounds);

    // 右端から適切な距離に配置
    float valueX = graphPos.x_notes + graphPos.width_notes - 10 - bounds.width();
    canvas->drawString(noteText, valueX, graphPos.y_notes + 20, font, textPaint);

    // グラフの線を描画
    if (graph_total_notes > 0) {
        float ratio = graph_current_notes / graph_total_notes;
        float barWidth = graphPos.width_notes - 20;
        float barHeight = 20;
        float barX = graphPos.x_notes + 10;
        float barY = graphPos.y_notes + graphPos.height_notes - 25;

        // 背景バー
        SkPaint barBgPaint;
        // グリッド色の暗い版を使用
        uint32_t gridColor = colorConfig.gridLineColor;
        uint8_t gr = ((gridColor >> 16) & 0xFF) / 3; // 1/3の輝度
        uint8_t gg = ((gridColor >> 8) & 0xFF) / 3;
        uint8_t gb = (gridColor & 0xFF) / 3;
        barBgPaint.setColor(SkColorSetARGB(255, gr, gg, gb));
        canvas->drawRect(SkRect::MakeXYWH(barX, barY, barWidth, barHeight), barBgPaint);

        // 進捗バー
        SkPaint barPaint;
        // 進捗バーは緑色（設定から取得可能にするか、デフォルト値を使用）
        barPaint.setColor(SkColorSetARGB(255, 0, 204, 0)); // 0.0, 0.8, 0.0, 1.0
        canvas->drawRect(SkRect::MakeXYWH(barX, barY, barWidth * ratio, barHeight), barPaint);
    }
}

// NPSグラフ描画関数（Skia版）
void drawGraphNPS(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_nps, graphPos.y_nps,
                        graphPos.width_nps, graphPos.height_nps, "NPS");

    // グラフの線のデータを準備
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();

    // NPS用の色（緑色）
    float r = 0.0f, g = 1.0f, b = 0.0f, a = 1.0f;
    
    float range = graph_max_nps - graph_min_nps;
    if (range <= 0) range = 1.0f;

    // タイトルの高さを考慮（タイトルの下にグラフを描画）
    float titleHeight = 30.0f; // タイトルの高さ（余裕を持たせる）
    float graphAreaHeight = graphPos.height_nps - titleHeight - 25; // グラフ描画領域の高さ

    // グラフ領域をクリップ（描画を枠内に制限）
    canvas->save();
    canvas->clipRect(SkRect::MakeXYWH(graphPos.x_nps, graphPos.y_nps, graphPos.width_nps, graphPos.height_nps));

    // 座標データを収集（vectorを使用して最適化）
    static std::vector<float> points_x(GRAPH_HISTORY_LENGTH);
    static std::vector<float> points_y(GRAPH_HISTORY_LENGTH);

    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int history_idx = (graph_history_index_nps + i) % GRAPH_HISTORY_LENGTH;
        float nps_val = graph_history_nps[history_idx];

        // 座標を計算
        points_x[i] = graphPos.x_nps + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_nps - 20);
        float normalized_y = (nps_val - graph_min_nps) / range;
        normalized_y = fmaxf(0.0f, fminf(1.0f, normalized_y));
        points_y[i] = graphPos.y_nps + titleHeight + graphAreaHeight - (normalized_y * graphAreaHeight);
    }

    // 線を描画
    for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
        drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, a);
    }

    // クリッピングを解除
    canvas->restore();

    // 現在のNPS値を表示（右端に配置）
    char npsText[32];

    // 整数値に変換してカンマ区切りで表示
    int npsInt = (int)graph_current_nps;

    if (npsInt >= 1000) {
        char tempBuffer[32];
        snprintf(tempBuffer, sizeof(tempBuffer), "%d", npsInt);

        int length = strlen(tempBuffer);
        int commaCount = (length - 1) / 3;
        int resultLength = length + commaCount;

        npsText[resultLength] = '\0';
        int tempIndex = length - 1;
        int resultIndex = resultLength - 1;
        int count = 0;

        while (tempIndex >= 0) {
            npsText[resultIndex--] = tempBuffer[tempIndex--];
            count++;
            if (count % 3 == 0 && tempIndex >= 0) {
                npsText[resultIndex--] = ',';
            }
        }
    } else {
        // 小さな数値の場合はそのまま表示
        snprintf(npsText, sizeof(npsText), "%d", npsInt);
    }

    // テキストペイント - グラフテキスト色を使用
    SkPaint textPaint;
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    textPaint.setColor(SkColorSetARGB(255, tr, tg, tb));
    textPaint.setAntiAlias(true);

    // フォントを設定
    SkFont font = setFont(FONT_DEFAULT, 16);

    // 範囲表示をタイトルの横に追加
    char titleText[64];
    snprintf(titleText, sizeof(titleText), "(%d-%d)",
             (int)graph_min_nps, (int)graph_max_nps);

    // テキストの幅を計測
    SkRect bounds;
    font.measureText(npsText, strlen(npsText), SkTextEncoding::kUTF8, &bounds);

    // 右端から適切な距離に配置
    float valueX = graphPos.x_nps + graphPos.width_nps - 10 - bounds.width();
    canvas->drawString(npsText, valueX, graphPos.y_nps + 20, font, textPaint);
    
    // 範囲を表示
    valueX = graphPos.x_nps + 50; // NPSの後ろに表示
    canvas->drawString(titleText, valueX, graphPos.y_nps + 20, font, textPaint);
}

// Polyphonyグラフ描画関数（Skia版）
void drawGraphPolyphony(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_poly, graphPos.y_poly,
                        graphPos.width_poly, graphPos.height_poly, "Polyphony");

    // グラフの線のデータを準備
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    // Polyphony用の色（黄色）
    float r = 1.0f, g = 1.0f, b = 0.0f, a = 1.0f;

    float range = graph_max_poly - graph_min_poly;
    if (range <= 0) range = 1.0f;

    // タイトルの高さを考慮（タイトルの下にグラフを描画）
    float titleHeight = 30.0f; // タイトルの高さ（余裕を持たせる）
    float graphAreaHeight = graphPos.height_poly - titleHeight - 25; // グラフ描画領域の高さ

    // グラフ領域をクリップ（描画を枠内に制限）
    canvas->save();
    canvas->clipRect(SkRect::MakeXYWH(graphPos.x_poly, graphPos.y_poly, graphPos.width_poly, graphPos.height_poly));

    // 座標データを収集（vectorを使用して最適化）
    static std::vector<float> points_x_poly(GRAPH_HISTORY_LENGTH);
    static std::vector<float> points_y_poly(GRAPH_HISTORY_LENGTH);

    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int history_idx = (graph_history_index_poly + i) % GRAPH_HISTORY_LENGTH;
        float poly_val = graph_history_poly[history_idx];

        // 座標を計算
        points_x_poly[i] = graphPos.x_poly + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_poly - 20);
        float normalized_y = (poly_val - graph_min_poly) / range;
        normalized_y = fmaxf(0.0f, fminf(1.0f, normalized_y));
        points_y_poly[i] = graphPos.y_poly + titleHeight + graphAreaHeight - (normalized_y * graphAreaHeight);
    }

    // 線を描画
    for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
        drawGraphLine(canvas, points_x_poly[i-1], points_y_poly[i-1], points_x_poly[i], points_y_poly[i], r, g, b, a);
    }

    // クリッピングを解除
    canvas->restore();

    // 現在のPolyphony値を表示（右端に配置）
    char polyText[32];

    // 整数値に変換してカンマ区切りで表示
    int polyInt = (int)graph_current_poly;

    if (polyInt >= 1000) {
        char tempBuffer[32];
        snprintf(tempBuffer, sizeof(tempBuffer), "%d", polyInt);

        int length = strlen(tempBuffer);
        int commaCount = (length - 1) / 3;
        int resultLength = length + commaCount;

        polyText[resultLength] = '\0';
        int tempIndex = length - 1;
        int resultIndex = resultLength - 1;
        int count = 0;

        while (tempIndex >= 0) {
            polyText[resultIndex--] = tempBuffer[tempIndex--];
            count++;
            if (count % 3 == 0 && tempIndex >= 0) {
                polyText[resultIndex--] = ',';
            }
        }
    } else {
        // 小さな数値の場合はそのまま表示
        snprintf(polyText, sizeof(polyText), "%d", polyInt);
    }

    // テキストペイント - グラフテキスト色を使用
    SkPaint textPaint;
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    textPaint.setColor(SkColorSetARGB(255, tr, tg, tb));
    textPaint.setAntiAlias(true);

    // フォントを設定
    SkFont font = setFont(FONT_DEFAULT, 16);

    // 範囲表示をタイトルの横に追加
    char titleText[64];
    snprintf(titleText, sizeof(titleText), "(%d-%d)",
             (int)graph_min_poly, (int)graph_max_poly);

    // テキストの幅を計測
    SkRect bounds;
    font.measureText(polyText, strlen(polyText), SkTextEncoding::kUTF8, &bounds);

    // 右端から適切な距離に配置
    float valueX = graphPos.x_poly + graphPos.width_poly - 10 - bounds.width();
    canvas->drawString(polyText, valueX, graphPos.y_poly + 20, font, textPaint);
    
    // 範囲を表示
    valueX = graphPos.x_poly + 80; // Polyphonyの後ろに表示
    canvas->drawString(titleText, valueX, graphPos.y_poly + 20, font, textPaint);
}

// BPMグラフ描画関数（Skia版）
void drawGraphBPM(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_bpm, graphPos.y_bpm,
                       graphPos.width_bpm, graphPos.height_bpm, "BPM");

    // グラフの線のデータを準備
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    // BPM用の色（青色）
    float r = 0.0f, g = 0.5f, b = 1.0f, a = 1.0f;
    float range = graph_max_bpm - graph_min_bpm;
    if (range <= 0) range = 1.0f;

    // タイトルの高さを考慮（タイトルの下にグラフを描画）
    float titleHeight = 30.0f; // タイトルの高さ（余裕を持たせる）
    float graphAreaHeight = graphPos.height_bpm - titleHeight - 25; // グラフ描画領域の高さ

    // グラフ領域をクリップ（描画を枠内に制限）
    canvas->save();
    canvas->clipRect(SkRect::MakeXYWH(graphPos.x_bpm, graphPos.y_bpm, graphPos.width_bpm, graphPos.height_bpm));

    // 座標データを収集
    float points_x[GRAPH_HISTORY_LENGTH];
    float points_y[GRAPH_HISTORY_LENGTH];

    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int history_idx = (graph_history_index_bpm + i) % GRAPH_HISTORY_LENGTH;
        float bpm_val = graph_history_bpm[history_idx];

        // 座標を計算
        points_x[i] = graphPos.x_bpm + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_bpm - 20);
        float normalized_y = (bpm_val - graph_min_bpm) / range;
        normalized_y = fmaxf(0.0f, fminf(1.0f, normalized_y));
        points_y[i] = graphPos.y_bpm + titleHeight + graphAreaHeight - (normalized_y * graphAreaHeight);
    }

    // 線を描画
    for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
        drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, a);
    }

    // クリッピングを解除
    canvas->restore();

    // テキストペイント - グラフテキスト色を使用
    SkPaint textPaint;
    uint32_t textColor = colorConfig.graph_textColor;
    uint8_t tr = (textColor >> 16) & 0xFF;
    uint8_t tg = (textColor >> 8) & 0xFF;
    uint8_t tb = textColor & 0xFF;
    textPaint.setColor(SkColorSetARGB(255, tr, tg, tb));
    textPaint.setAntiAlias(true);

    // フォントを設定
    SkFont font = setFont(FONT_DEFAULT, 16);

    // 範囲表示をタイトルの横に追加
    char titleText[64];
    snprintf(titleText, sizeof(titleText), "(%d-%d)",
             (int)graph_min_bpm, (int)graph_max_bpm);

    // 現在のBPM値を表示（右端に配置）
    char bpmText[32];
    snprintf(bpmText, sizeof(bpmText), "%d", (int)graph_current_bpm);

    // テキストの幅を計測
    SkRect bounds;
    font.measureText(bpmText, strlen(bpmText), SkTextEncoding::kUTF8, &bounds);

    // 右端から適切な距離に配置
    float valueX = graphPos.x_bpm + graphPos.width_bpm - 10 - bounds.width();
    canvas->drawString(bpmText, valueX, graphPos.y_bpm + 20, font, textPaint);
    
    // 範囲を表示
    valueX = graphPos.x_bpm + 50; // BPMの後ろに表示
    canvas->drawString(titleText, valueX, graphPos.y_bpm + 20, font, textPaint);
}

// パングラフ描画関数（Skia版）
void drawGraphPan(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_pan, graphPos.y_pan,
                       graphPos.width_pan, graphPos.height_pan, "Pan (L-R)");

    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    // タイトルの高さを考慮
    float titleHeight = 30.0f; // タイトルの高さ（余裕を持たせる）
    float graphAreaHeight = graphPos.height_pan - titleHeight - 25; // グラフ描画領域の高さ

    // グラフ領域をクリップ（描画を枠内に制限）
    canvas->save();
    canvas->clipRect(SkRect::MakeXYWH(graphPos.x_pan, graphPos.y_pan, graphPos.width_pan, graphPos.height_pan));

    // 各チャンネルのパン値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_pan + i) % GRAPH_HISTORY_LENGTH;
            float pan_val = graph_history_pan[ch][history_idx];

            // パン値を正規化（0-127を0.0-1.0に変換）
            float normalized_pan = pan_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_pan + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_pan - 20);
            points_y[i] = graphPos.y_pan + titleHeight + graphAreaHeight - (normalized_pan * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }

    // クリッピングを解除
    canvas->restore();

    // 中央線を描画（パン = 64）
    float centerY = graphPos.y_pan + titleHeight + graphAreaHeight / 2;

    // グリッド線の色を使用
    uint32_t gridColor = colorConfig.gridLineColor;
    uint8_t gr = (gridColor >> 16) & 0xFF;
    uint8_t gg = (gridColor >> 8) & 0xFF;
    uint8_t gb = gridColor & 0xFF;

    SkPaint centerLinePaint;
    centerLinePaint.setColor(SkColorSetARGB(128, gr, gg, gb)); // 半透明（50%）のグリッド線色
    centerLinePaint.setStyle(SkPaint::kStroke_Style);
    centerLinePaint.setStrokeWidth(1.0f);
    canvas->drawLine(
        graphPos.x_pan + 10, centerY,
        graphPos.x_pan + graphPos.width_pan - 10, centerY,
        centerLinePaint
    );
}

// ピッチベンドグラフ描画関数（Skia版）
void drawGraphPitchBend(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_pb, graphPos.y_pb,
                       graphPos.width_pb, graphPos.height_pb, "Pitch Bend");

    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
                       
    // タイトルの高さを考慮
    float titleHeight = 30.0f; // タイトルの高さ（余裕を持たせる）
    float graphAreaHeight = graphPos.height_pb - titleHeight - 25; // グラフ描画領域の高さ

    // グラフ領域をクリップ（描画を枠内に制限）
    canvas->save();
    canvas->clipRect(SkRect::MakeXYWH(graphPos.x_pb, graphPos.y_pb, graphPos.width_pb, graphPos.height_pb));

    // 各チャンネルのピッチベンド値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_pb + i) % GRAPH_HISTORY_LENGTH;
            float pb_val = graph_history_pb[ch][history_idx];

            // ピッチベンド値を正規化（-8192〜8191を0.0-1.0に変換）
            float normalized_pb = (pb_val + 8192.0f) / 16384.0f;

            // 座標を計算
            points_x[i] = graphPos.x_pb + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_pb - 20);
            points_y[i] = graphPos.y_pb + titleHeight + graphAreaHeight - (normalized_pb * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }

    // クリッピングを解除
    canvas->restore();

    // 中央線を描画（ピッチベンド = 0）
    float centerY = graphPos.y_pb + titleHeight + graphAreaHeight / 2;

    // グリッド線の色を使用
    uint32_t gridColor = colorConfig.gridLineColor;
    uint8_t gr = (gridColor >> 16) & 0xFF;
    uint8_t gg = (gridColor >> 8) & 0xFF;
    uint8_t gb = gridColor & 0xFF;

    SkPaint centerLinePaint;
    centerLinePaint.setColor(SkColorSetARGB(128, gr, gg, gb)); // 半透明（50%）のグリッド線色
    centerLinePaint.setStyle(SkPaint::kStroke_Style);
    centerLinePaint.setStrokeWidth(1.0f);
    canvas->drawLine(
        graphPos.x_pb + 10, centerY,
        graphPos.x_pb + graphPos.width_pb - 10, centerY,
        centerLinePaint
    );
}

// ボリュームグラフ描画関数 (CC#7) - 最適化版
void drawGraphVolume(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_volume, graphPos.y_volume,
                       graphPos.width_volume, graphPos.height_volume, "Volume");

    // グラフエリアの計算
    const float titleHeight = 30;
    const float graphAreaHeight = graphPos.height_volume - titleHeight - 25;

    // 各チャンネルのボリューム値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_volume + i) % GRAPH_HISTORY_LENGTH;
            float volume_val = graph_history_volume[ch][history_idx];

            // ボリューム値を正規化（0-127を0.0-1.0に変換）
            float normalized_volume = volume_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_volume + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_volume - 20);
            points_y[i] = graphPos.y_volume + titleHeight + graphAreaHeight - (normalized_volume * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }
}

// サステイングラフ描画関数 (CC#64) - 最適化版
void drawGraphSustain(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_sustain, graphPos.y_sustain,
                       graphPos.width_sustain, graphPos.height_sustain, "Sustain");

    // グラフエリアの計算
    const float titleHeight = 30;
    const float graphAreaHeight = graphPos.height_sustain - titleHeight - 25;

    // 各チャンネルのサステイン値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_sustain + i) % GRAPH_HISTORY_LENGTH;
            float sustain_val = graph_history_sustain[ch][history_idx];

            // サステイン値を正規化（0-127を0.0-1.0に変換）
            float normalized_sustain = sustain_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_sustain + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_sustain - 20);
            points_y[i] = graphPos.y_sustain + titleHeight + graphAreaHeight - (normalized_sustain * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }

    // 中央線を描画（サステイン = 64）
    float centerY = graphPos.y_sustain + titleHeight + graphAreaHeight / 2;
    SkPaint centerLinePaint;
    centerLinePaint.setColor(SkColorSetARGB(128, 128, 128, 128)); // 0.5, 0.5, 0.5, 0.5
    centerLinePaint.setStyle(SkPaint::kStroke_Style);
    centerLinePaint.setStrokeWidth(1.0f);
    canvas->drawLine(
        graphPos.x_sustain + 10, centerY,
        graphPos.x_sustain + graphPos.width_sustain - 10, centerY,
        centerLinePaint
    );
}

// フィルターカットオフグラフ描画関数 (CC#74) - 最適化版
void drawGraphFilterCutoff(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_filter_cutoff, graphPos.y_filter_cutoff,
                       graphPos.width_filter_cutoff, graphPos.height_filter_cutoff, "Filter Cut");

    // グラフエリアの計算
    const float titleHeight = 30;
    const float graphAreaHeight = graphPos.height_filter_cutoff - titleHeight - 25;

    // 各チャンネルのフィルターカットオフ値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_filter_cutoff + i) % GRAPH_HISTORY_LENGTH;
            float cutoff_val = graph_history_filter_cutoff[ch][history_idx];

            // フィルターカットオフ値を正規化（0-127を0.0-1.0に変換）
            float normalized_cutoff = cutoff_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_filter_cutoff + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_filter_cutoff - 20);
            points_y[i] = graphPos.y_filter_cutoff + titleHeight + graphAreaHeight - (normalized_cutoff * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }
}

// フィルターレゾナンスグラフ描画関数 (CC#71) - 最適化版
void drawGraphFilterResonance(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_filter_resonance, graphPos.y_filter_resonance,
                       graphPos.width_filter_resonance, graphPos.height_filter_resonance, "Filter Res");

    // グラフエリアの計算
    const float titleHeight = 30;
    const float graphAreaHeight = graphPos.height_filter_resonance - titleHeight - 25;

    // 各チャンネルのフィルターレゾナンス値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_filter_resonance + i) % GRAPH_HISTORY_LENGTH;
            float resonance_val = graph_history_filter_resonance[ch][history_idx];

            // フィルターレゾナンス値を正規化（0-127を0.0-1.0に変換）
            float normalized_resonance = resonance_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_filter_resonance + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_filter_resonance - 20);
            points_y[i] = graphPos.y_filter_resonance + titleHeight + graphAreaHeight - (normalized_resonance * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }
}

// リリースタイムグラフ描画関数 (CC#72) - 最適化版
void drawGraphReleaseTime(SkCanvas* canvas) {
    // 共通背景描画を使用
    drawGraphBackground(canvas, graphPos.x_release_time, graphPos.y_release_time,
                       graphPos.width_release_time, graphPos.height_release_time, "Release");

    // グラフエリアの計算
    const float titleHeight = 30;
    const float graphAreaHeight = graphPos.height_release_time - titleHeight - 25;

    // 各チャンネルのリリースタイム値を描画
    for (int ch = 0; ch < 16; ch++) {
        // 外部のチャンネルカラーを使用
        unsigned int color = channel_colors[ch];

        // RGBに変換
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;

        // チャンネル9（ドラム）は白色
        if (ch == 9) {
            r = g = b = 1.0f;
        }

        // 座標データを収集
        float points_x[GRAPH_HISTORY_LENGTH];
        float points_y[GRAPH_HISTORY_LENGTH];

        for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
            int history_idx = (graph_history_index_release_time + i) % GRAPH_HISTORY_LENGTH;
            float release_val = graph_history_release_time[ch][history_idx];

            // リリースタイム値を正規化（0-127を0.0-1.0に変換）
            float normalized_release = release_val / 127.0f;

            // 座標を計算
            points_x[i] = graphPos.x_release_time + 10 + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * (graphPos.width_release_time - 20);
            points_y[i] = graphPos.y_release_time + titleHeight + graphAreaHeight - (normalized_release * graphAreaHeight);
        }

        // 線を描画
        for (int i = 1; i < GRAPH_HISTORY_LENGTH; i++) {
            drawGraphLine(canvas, points_x[i-1], points_y[i-1], points_x[i], points_y[i], r, g, b, 0.8f);
        }
    }

    // 中央線を描画（リリースタイム = 64）
    float centerY = graphPos.y_release_time + titleHeight + graphAreaHeight / 2;
    SkPaint centerLinePaint;
    centerLinePaint.setColor(SkColorSetARGB(128, 128, 128, 128)); // 0.5, 0.5, 0.5, 0.5
    centerLinePaint.setStyle(SkPaint::kStroke_Style);
    centerLinePaint.setStrokeWidth(1.0f);
    canvas->drawLine(
        graphPos.x_release_time + 10, centerY,
        graphPos.x_release_time + graphPos.width_release_time - 10, centerY,
        centerLinePaint
    );
}