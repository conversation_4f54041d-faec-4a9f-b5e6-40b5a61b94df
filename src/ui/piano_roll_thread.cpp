#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <pthread.h>
#include <chrono>
#include <thread>
// プラットフォーム別のヘッダー
#ifdef _WIN32
#include <windows.h>
#elif defined(__linux__)
#include <unistd.h>
#include <sys/sysinfo.h>
#endif

#include <SkCanvas.h>
#include <SkSurface.h>

#include "../../include/ui/piano_roll_thread.h"
#include "../../include/ui/piano_roll.h"
#include "../../include/app_state.h"
#include "../../include/utils/color.h"
#include "../../include/ui/piano_keyboard.h"
#include "../../include/utils/texture_manager.h"
#include "../../include/gpu_context.h"

// スレッドプールの最大サイズ
#define MAX_THREAD_COUNT 16

// スレッド待機の最適化用定数
#define MAX_THREAD_WAIT_TIME_MS 10 // 最大待機時間（ミリ秒）

// スレッドプールの状態
static ThreadPoolState poolState = THREAD_POOL_IDLE;

// スレッド数（デフォルトは自動設定）
static int threadCount = 0;

// マルチスレッド処理が有効かどうか
static bool multithreadingEnabled = false; // GPU処理との競合を避けるため一時的に無効化

// スレッドプール
static pthread_t* threads = NULL;

// スレッド同期用
static pthread_mutex_t poolMutex;
static pthread_cond_t poolCondition;

// タスク構造体
typedef struct {
    SkCanvas* canvas;       // 描画先のSkiaキャンバス
    float currentTime;      // 現在の時間
    int startIndex;         // 処理開始インデックス
    int endIndex;           // 処理終了インデックス
    bool completed;         // タスクが完了したかどうか
} PianoRollTask;

// タスクキュー
static PianoRollTask* taskQueue = NULL;
static int taskCount = 0;
static int taskCapacity = 0;
static int completedTaskCount = 0;

// 各スレッドの描画用サーフェス
typedef struct {
    sk_sp<SkSurface> surface;
    SkCanvas* canvas;
    bool inUse;
} ThreadSurface;

static ThreadSurface* threadSurfaces = NULL;

// ピアノキーボードと共通の関数（piano_roll.cからコピー）
// 黒鍵の位置を判定する関数（0-11の中で黒鍵は1, 3, 6, 8, 10）
static bool isBlackKey(int noteNumber) {
    int position = noteNumber % 12;
    return (position == 1 || position == 3 || position == 6 || position == 8 || position == 10);
}

// 白鍵の位置を計算する関数
static int getWhiteKeyIndex(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;

    // 各オクターブの白鍵の数は7つ
    int whiteKeyIndex = octave * 7;

    // 白鍵のインデックスを計算
    if (position == 0) whiteKeyIndex += 0; // C
    else if (position == 2) whiteKeyIndex += 1; // D
    else if (position == 4) whiteKeyIndex += 2; // E
    else if (position == 5) whiteKeyIndex += 3; // F
    else if (position == 7) whiteKeyIndex += 4; // G
    else if (position == 9) whiteKeyIndex += 5; // A
    else if (position == 11) whiteKeyIndex += 6; // B

    return whiteKeyIndex;
}

// 黒鍵の位置を計算する関数（黒鍵の左側の白鍵のインデックスを返す）
static int getBlackKeyPosition(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;
    int whiteKeyIndex = octave * 7;

    // 黒鍵の位置を計算（左側の白鍵のインデックスを返す）
    if (position == 1) return whiteKeyIndex + 0; // C# (Cの右)
    else if (position == 3) return whiteKeyIndex + 1; // D# (Dの右)
    else if (position == 6) return whiteKeyIndex + 3; // F# (Fの右)
    else if (position == 8) return whiteKeyIndex + 4; // G# (Gの右)
    else if (position == 10) return whiteKeyIndex + 5; // A# (Aの右)

    // 黒鍵でない場合（エラー）
    return -1;
}

// ノートが無効化されているかチェックする関数（piano_roll.cから参照）
// piano_roll.hで宣言されているため、staticではない
extern bool isNoteInvalidated(uint32_t noteIndex);

// CPUコア数を取得する関数
static int getCPUCoreCount(void) {
#ifdef _WIN32
    SYSTEM_INFO sysinfo;
    GetSystemInfo(&sysinfo);
    return sysinfo.dwNumberOfProcessors;
#else
    // Linuxなどの場合はsysconfを使用
    return sysconf(_SC_NPROCESSORS_ONLN);
#endif
}

// スレッドプールのタスク処理関数
static void* threadPoolWorker(void* arg) {
    int threadId = *((int*)arg);
    free(arg); // 引数のメモリを解放

    printf("Piano roll thread %d started\n", threadId);

    while (true) {
        // ミューテックスをロック
        pthread_mutex_lock(&poolMutex);

        // タスクが利用可能か停止要求があるかチェック
        if (taskCount == 0 && poolState != THREAD_POOL_STOPPING) {
            // 短時間だけ条件変数で待機
            struct timespec ts;
            clock_gettime(CLOCK_REALTIME, &ts);
            ts.tv_nsec += MAX_THREAD_WAIT_TIME_MS * 1000000; // ナノ秒に変換
            if (ts.tv_nsec >= 1000000000) {
                ts.tv_sec += 1;
                ts.tv_nsec -= 1000000000;
            }

            // 条件変数でタイムアウト付き待機
            pthread_cond_timedwait(&poolCondition, &poolMutex, &ts);
        }

        // スレッドプールが停止要求を受けた場合
        if (poolState == THREAD_POOL_STOPPING) {
            pthread_mutex_unlock(&poolMutex);
            break;
        }

        // タスクを取得
        int taskIndex = -1;
        for (int i = 0; i < taskCapacity; i++) {
            if (!taskQueue[i].completed && taskQueue[i].canvas != NULL) {
                taskIndex = i;
                break;
            }
        }

        // タスクがない場合は待機
        if (taskIndex == -1) {
            pthread_mutex_unlock(&poolMutex);
            continue;
        }

        // タスクデータをローカルにコピー
        PianoRollTask task = taskQueue[taskIndex];

        // このスレッド用のサーフェスを取得
        threadSurfaces[threadId].inUse = true;

        // ミューテックスをアンロック
        pthread_mutex_unlock(&poolMutex);

        // タスクを実行（ノートの描画処理）
        // ここでpiano_roll.cの描画ロジックを呼び出す
        drawPianoRollRange(threadSurfaces[threadId].canvas, task.currentTime, task.startIndex, task.endIndex);

        // ミューテックスをロック
        pthread_mutex_lock(&poolMutex);

        // タスク完了をマーク
        taskQueue[taskIndex].completed = true;
        completedTaskCount++;

        // すべてのタスクが完了したら通知
        if (completedTaskCount == taskCount) {
            pthread_cond_broadcast(&poolCondition);
        }

        // ミューテックスをアンロック
        pthread_mutex_unlock(&poolMutex);
    }

    printf("Piano roll thread %d stopped\n", threadId);
    return NULL;
}

// スレッドプールの初期化
bool initPianoRollThreadPool(int requestedThreadCount) {
    // すでに初期化されている場合は何もしない
    if (poolState != THREAD_POOL_IDLE) {
        return true;
    }

    // スレッド数を設定
    if (requestedThreadCount <= 0) {
        // 自動設定（CPUコア数 - 1、最低1）
        threadCount = getCPUCoreCount() - 1;
        if (threadCount < 1) threadCount = 1;
    } else {
        // 指定されたスレッド数（最大MAX_THREAD_COUNT）
        threadCount = requestedThreadCount;
        if (threadCount > MAX_THREAD_COUNT) threadCount = MAX_THREAD_COUNT;
    }

    printf("Initializing piano roll thread pool with %d threads\n", threadCount);

    // ミューテックスと条件変数の初期化
    if (pthread_mutex_init(&poolMutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize pool mutex\n");
        return false;
    }

    if (pthread_cond_init(&poolCondition, NULL) != 0) {
        fprintf(stderr, "Failed to initialize pool condition\n");
        pthread_mutex_destroy(&poolMutex);
        return false;
    }

    // スレッド配列の確保
    threads = (pthread_t*)malloc(threadCount * sizeof(pthread_t));
    if (!threads) {
        fprintf(stderr, "Failed to allocate memory for threads\n");
        pthread_mutex_destroy(&poolMutex);
        pthread_cond_destroy(&poolCondition);
        return false;
    }

    // 各スレッド用のサーフェスを確保
    threadSurfaces = (ThreadSurface*)calloc(threadCount, sizeof(ThreadSurface));
    if (!threadSurfaces) {
        fprintf(stderr, "Failed to allocate memory for thread surfaces\n");
        free(threads);
        threads = NULL;
        pthread_mutex_destroy(&poolMutex);
        pthread_cond_destroy(&poolCondition);
        return false;
    }

    // 各スレッド用のサーフェスを初期化
    for (int i = 0; i < threadCount; i++) {
        // GPUサーフェスを優先的に作成
        sk_sp<SkSurface> surface = nullptr;

#ifdef GPU_RENDERING_ENABLED
        if (isGPUAvailable()) {
            surface = createGPUSurface(WIDTH, HEIGHT);
        }
#endif

        // GPUサーフェスの作成に失敗した場合はCPUサーフェスにフォールバック
        if (!surface) {
            SkImageInfo info = SkImageInfo::MakeN32Premul(WIDTH, HEIGHT);
            surface = SkSurface::MakeRaster(info);
        }

        threadSurfaces[i].surface = surface;
        if (!threadSurfaces[i].surface) {
            fprintf(stderr, "Failed to create surface for thread %d\n", i);
            // 作成済みのサーフェスを解放
            for (int j = 0; j < i; j++) {
                threadSurfaces[j].surface = nullptr;
            }
            free(threadSurfaces);
            threadSurfaces = NULL;
            free(threads);
            threads = NULL;
            pthread_mutex_destroy(&poolMutex);
            pthread_cond_destroy(&poolCondition);
            return false;
        }

        // サーフェス用のキャンバスを取得
        threadSurfaces[i].canvas = threadSurfaces[i].surface->getCanvas();
        if (!threadSurfaces[i].canvas) {
            fprintf(stderr, "Failed to get canvas for thread %d\n", i);
            // 作成済みのサーフェスを解放
            for (int j = 0; j < i; j++) {
                threadSurfaces[j].surface = nullptr;
            }
            threadSurfaces[i].surface = nullptr;
            free(threadSurfaces);
            threadSurfaces = NULL;
            free(threads);
            threads = NULL;
            pthread_mutex_destroy(&poolMutex);
            pthread_cond_destroy(&poolCondition);
            return false;
        }

        threadSurfaces[i].inUse = false;
    }

    // タスクキューの初期化
    // 各スレッドに複数のタスクを割り当て可能に（オーバーフロー防止のため上限を設定）
    taskCapacity = (threadCount > 0 && threadCount <= 100) ? threadCount * 2 : 16;
    taskQueue = (PianoRollTask*)calloc(taskCapacity, sizeof(PianoRollTask));
    if (!taskQueue) {
        fprintf(stderr, "Failed to allocate memory for task queue\n");
        for (int i = 0; i < threadCount; i++) {
            threadSurfaces[i].surface = nullptr;
        }
        free(threadSurfaces);
        threadSurfaces = NULL;
        free(threads);
        threads = NULL;
        pthread_mutex_destroy(&poolMutex);
        pthread_cond_destroy(&poolCondition);
        return false;
    }

    // スレッドプールの状態を設定
    poolState = THREAD_POOL_RUNNING;

    // スレッドを作成
    for (int i = 0; i < threadCount; i++) {
        int* threadId = (int*)malloc(sizeof(int));
        if (!threadId) {
            fprintf(stderr, "Failed to allocate memory for thread ID\n");
            continue;
        }
        *threadId = i;

        if (pthread_create(&threads[i], NULL, threadPoolWorker, threadId) != 0) {
            fprintf(stderr, "Failed to create thread %d\n", i);
            free(threadId);
            // エラー処理（すでに作成されたスレッドは停止させる）
            poolState = THREAD_POOL_STOPPING;
            pthread_cond_broadcast(&poolCondition);
            for (int j = 0; j < i; j++) {
                pthread_join(threads[j], NULL);
            }
            for (int j = 0; j < threadCount; j++) {
                threadSurfaces[j].surface = nullptr;
            }
            free(threadSurfaces);
            threadSurfaces = NULL;
            free(taskQueue);
            taskQueue = NULL;
            free(threads);
            threads = NULL;
            pthread_mutex_destroy(&poolMutex);
            pthread_cond_destroy(&poolCondition);
            return false;
        }
    }

    printf("Piano roll thread pool initialized successfully\n");
    return true;
}

// スレッドプールのクリーンアップ
void cleanupPianoRollThreadPool(void) {
    // スレッドプールが初期化されていない場合は何もしない
    if (poolState == THREAD_POOL_IDLE) {
        return;
    }

    printf("Cleaning up piano roll thread pool\n");

    // ミューテックスをロック
    pthread_mutex_lock(&poolMutex);

    // スレッドプールの状態を停止中に設定
    poolState = THREAD_POOL_STOPPING;

    // すべてのスレッドに通知
    pthread_cond_broadcast(&poolCondition);

    // ミューテックスをアンロック
    pthread_mutex_unlock(&poolMutex);

    // すべてのスレッドの終了を待機
    for (int i = 0; i < threadCount; i++) {
        pthread_join(threads[i], NULL);
    }

    // スレッド用のサーフェスを解放
    for (int i = 0; i < threadCount; i++) {
        threadSurfaces[i].surface = nullptr;
    }

    // メモリを解放
    free(threadSurfaces);
    threadSurfaces = NULL;
    free(taskQueue);
    taskQueue = NULL;
    free(threads);
    threads = NULL;

    // ミューテックスと条件変数を破棄
    pthread_mutex_destroy(&poolMutex);
    pthread_cond_destroy(&poolCondition);

    // スレッドプールの状態をリセット
    poolState = THREAD_POOL_IDLE;
    taskCount = 0;
    taskCapacity = 0;
    completedTaskCount = 0;

    printf("Piano roll thread pool cleaned up\n");
}

// スレッド数を設定
void setPianoRollThreadCount(int requestedThreadCount) {
    // スレッドプールが実行中の場合は一度クリーンアップして再初期化
    if (poolState == THREAD_POOL_RUNNING) {
        cleanupPianoRollThreadPool();
    }

    // スレッドプールを初期化
    initPianoRollThreadPool(requestedThreadCount);
}

// 現在のスレッド数を取得
int getPianoRollThreadCount(void) {
    return threadCount;
}

// マルチスレッド処理が有効かどうかを確認
bool isPianoRollMultithreaded(void) {
    return multithreadingEnabled && poolState == THREAD_POOL_RUNNING;
}

// マルチスレッド処理を有効/無効にする
void enablePianoRollMultithreading(bool enable) {
    multithreadingEnabled = enable;

    // 無効化する場合はスレッドプールをクリーンアップ
    if (!enable && poolState == THREAD_POOL_RUNNING) {
        cleanupPianoRollThreadPool();
    }
    // 有効化する場合はスレッドプールを初期化
    else if (enable && poolState == THREAD_POOL_IDLE) {
        initPianoRollThreadPool(threadCount);
    }
}

// マルチスレッドでピアノロールを描画
bool drawPianoRollMultithreaded(SkCanvas* canvas, float currentTime, int startIndex, int endIndex) {
    // マルチスレッド処理が無効または範囲が小さい場合はシングルスレッドで処理
    if (!isPianoRollMultithreaded() || endIndex - startIndex < threadCount * 2) {
        // シングルスレッドで描画
        drawPianoRollRange(canvas, currentTime, startIndex, endIndex);
        return true;
    }

    // ミューテックスをロック
    pthread_mutex_lock(&poolMutex);

    // タスクキューをリセット
    taskCount = 0;
    completedTaskCount = 0;
    for (int i = 0; i < taskCapacity; i++) {
        taskQueue[i].canvas = NULL;
        taskQueue[i].completed = false;
    }

    // 各スレッドのサーフェスをクリア
    for (int i = 0; i < threadCount; i++) {
        threadSurfaces[i].inUse = false;
        // サーフェスをクリア
        threadSurfaces[i].canvas->clear(SK_ColorTRANSPARENT);
    }

    // タスクを分割
    int notesPerThread = (endIndex - startIndex + 1) / threadCount;
    if (notesPerThread < 1) notesPerThread = 1;

    // タスクをキューに追加
    for (int i = 0; i < threadCount && taskCount < taskCapacity; i++) {
        int taskStartIndex = startIndex + i * notesPerThread;
        int taskEndIndex = taskStartIndex + notesPerThread - 1;

        // 最後のスレッドは残りすべてを処理
        if (i == threadCount - 1) {
            taskEndIndex = endIndex;
        }

        // 範囲が有効な場合のみタスクを追加
        if (taskStartIndex <= taskEndIndex) {
            taskQueue[taskCount].canvas = canvas;
            taskQueue[taskCount].currentTime = currentTime;
            taskQueue[taskCount].startIndex = taskStartIndex;
            taskQueue[taskCount].endIndex = taskEndIndex;
            taskQueue[taskCount].completed = false;
            taskCount++;
        }
    }

    // タスクがない場合は終了
    if (taskCount == 0) {
        pthread_mutex_unlock(&poolMutex);
        return true;
    }

    // スレッドに通知
    pthread_cond_broadcast(&poolCondition);

    // すべてのタスクが完了するまで短時間待機を繰り返す
    while (completedTaskCount < taskCount) {
        // 短時間だけ条件変数で待機
        struct timespec ts;
        clock_gettime(CLOCK_REALTIME, &ts);
        ts.tv_nsec += MAX_THREAD_WAIT_TIME_MS * 1000000; // ナノ秒に変換
        if (ts.tv_nsec >= 1000000000) {
            ts.tv_sec += 1;
            ts.tv_nsec -= 1000000000;
        }

        // 条件変数でタイムアウト付き待機
        pthread_cond_timedwait(&poolCondition, &poolMutex, &ts);
    }

    // 各スレッドの描画結果を統合
    for (int i = 0; i < threadCount; i++) {
        if (threadSurfaces[i].inUse) {
            // スレッドのサーフェスをメインのキャンバスに合成
            sk_sp<SkImage> image = threadSurfaces[i].surface->makeImageSnapshot();
            if (image) {
                canvas->drawImage(image, 0, 0);
            }
        }
    }

    // ミューテックスをアンロック
    pthread_mutex_unlock(&poolMutex);

    return true;
}

// 注意: isNoteInvalidated関数はpiano_roll.cで実装されており、
// piano_roll.hで宣言されているため、ここでは実装しない
