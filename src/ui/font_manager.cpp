#include <stdio.h>
#include <ft2build.h>
#include FT_FREETYPE_H
#include "../../include/skia/include/core/SkTypeface.h"
#include "../../include/skia/include/core/SkFont.h"
#include "../../include/skia/include/core/SkFontMgr.h"
#include "../../include/skia/include/core/SkData.h"
#include "../../include/font_manager.h"
#include "../../include/app_state.h"

// 埋め込みフォントデータ
static unsigned char g_noto_sans_jp_semibold_data[] = {
    #include "NotoSansJP-SemiBold.ttf.h"
};

// グローバルフォントコンテキスト
FontContext fontContext = {nullptr, nullptr, false};

// フォントコンテキストを初期化する関数
bool initFontContext() {
    if (fontContext.initialized) {
        return true;
    }

    // デフォルトフォント（Arial）
    fontContext.default_font = SkTypeface::MakeFromName("Arial", SkFontStyle::Bold());
    if (!fontContext.default_font) {
        fprintf(stderr, "Warning: Could not load Arial font, using default font\n");
        fontContext.default_font = SkTypeface::MakeDefault();
    }

    // 日本語フォント（Noto Sans JP）- 埋め込みデータから読み込み
    sk_sp<SkData> fontData = SkData::MakeWithoutCopy(g_noto_sans_jp_semibold_data, sizeof(g_noto_sans_jp_semibold_data));
    fontContext.japanese_font = SkTypeface::MakeFromData(fontData);

    if (!fontContext.japanese_font) {
        // 埋め込みデータからの読み込みに失敗した場合、ファイルから読み込みを試みる
        fprintf(stderr, "Warning: Could not load Japanese font from embedded data, trying file: %s\n", JAPANESE_FONT_PATH);
        fontContext.japanese_font = SkTypeface::MakeFromFile(JAPANESE_FONT_PATH);

        if (!fontContext.japanese_font) {
            fprintf(stderr, "Error: Could not load Japanese font from %s\n", JAPANESE_FONT_PATH);
            // 日本語フォントが読み込めない場合はデフォルトフォントを使用
            fontContext.japanese_font = fontContext.default_font;
        }
    }

    fontContext.initialized = true;
    printf("Font context initialized successfully\n");
    return true;
}

// フォントを設定する関数
SkFont setFont(FontType type, float size) {
    if (!fontContext.initialized) {
        if (!initFontContext()) {
            // 初期化に失敗した場合はデフォルトフォントを使用
            return SkFont(SkTypeface::MakeDefault(), size);
        }
    }

    sk_sp<SkTypeface> typeface;
    switch (type) {
        case FONT_JAPANESE:
            if (fontContext.japanese_font) {
                typeface = fontContext.japanese_font;
            } else {
                // 日本語フォントが利用できない場合はデフォルトフォントを使用
                typeface = fontContext.default_font;
            }
            break;
        case FONT_DEFAULT:
        default:
            typeface = fontContext.default_font;
            break;
    }

    SkFont font(typeface, size);
    font.setSubpixel(true);
    font.setEdging(SkFont::Edging::kSubpixelAntiAlias);
    return font;
}

// フォントリソースを解放する関数
void cleanupFontContext() {
    if (fontContext.initialized) {
        // Skiaのスマートポインタは自動的に解放されるため、
        // 明示的な解放は不要
        fontContext.default_font = nullptr;
        fontContext.japanese_font = nullptr;
        fontContext.initialized = false;
    }
}
