#include "../../include/ui/piano_roll.h"
#include "../../include/app_state.h"
#include "../../include/utils/color.h"
#include "../../include/utils/config_manager.h"
#include "../../include/ui/piano_keyboard.h"
#include "../../include/utils/texture_manager.h"
#include "../../include/ui/piano_roll_thread.h"
#include "../../include/gpu_context.h"
#include "../../include/midi_utils.h"
#include "../../include/note_processor.h"
#include "../../include/performance.h"
#include "../../include/frame_renderer.h"
#include "../../include/skia/include/effects/SkGradientShader.h" // グラデーションシェーダー用
#include <skia/include/core/SkPath.h> // パス描画用
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h> // rand()用
#include <time.h>   // time()用
#include <stdlib.h>
#include <iostream>
#include <vector>

static PianoRollCache pianoRollCache = {nullptr, nullptr, 0.0f, false, 0, 0};
static bool isBlackKey(int noteNumber) {
    int position = noteNumber % 12;
    return (position == 1 || position == 3 || position == 6 || position == 8 || position == 10);
}

static int getWhiteKeyIndex(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;
    int whiteKeyIndex = octave * 7;

    if (position == 0) whiteKeyIndex += 0;
    else if (position == 2) whiteKeyIndex += 1;
    else if (position == 4) whiteKeyIndex += 2;
    else if (position == 5) whiteKeyIndex += 3;
    else if (position == 7) whiteKeyIndex += 4;
    else if (position == 9) whiteKeyIndex += 5;
    else if (position == 11) whiteKeyIndex += 6;

    return whiteKeyIndex;
}

static int getBlackKeyPosition(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;
    int whiteKeyIndex = octave * 7;

    if (position == 1) return whiteKeyIndex + 0;
    else if (position == 3) return whiteKeyIndex + 1;
    else if (position == 6) return whiteKeyIndex + 3;
    else if (position == 8) return whiteKeyIndex + 4;
    else if (position == 10) return whiteKeyIndex + 5;

    return -1;
}

bool showPianoRoll = true;

static uint8_t* invalidatedNotesBitmap = NULL;
static uint32_t bitmapSize = 0;
static uint32_t totalInvalidatedNotes = 0;
static uint32_t cumulativeNoteCount = 0;
static uint32_t cumulativeNoteCountOffset = 0;

void initPianoRoll(void) {
    cumulativeNoteCount = 0;
    cumulativeNoteCountOffset = 0;
    initPianoRollThreadPool(0);
}

static void initInvalidatedNotesBitmap(uint32_t noteCount) {
    if (invalidatedNotesBitmap) {
        free(invalidatedNotesBitmap);
        invalidatedNotesBitmap = NULL;
    }

    if (noteCount == 0) {
        bitmapSize = 0;
        totalInvalidatedNotes = 0;
        return;
    }

    bitmapSize = (noteCount + 7) / 8;
    invalidatedNotesBitmap = (uint8_t*)calloc(bitmapSize, sizeof(uint8_t));
    if (!invalidatedNotesBitmap) {
        fprintf(stderr, "Failed to allocate memory for invalidated notes bitmap\n");
        bitmapSize = 0;
        return;
    }

    totalInvalidatedNotes = 0;
}

bool isNoteInvalidated(uint32_t noteIndex) {
    if (!invalidatedNotesBitmap) {
        return false;
    }

    if (noteIndex >= appState.noteTimeEventCount) {
        return false;
    }

    uint32_t byteIndex = noteIndex / 8;
    uint8_t bitIndex = noteIndex % 8;

    if (byteIndex >= bitmapSize) {
        return false;
    }

    return (invalidatedNotesBitmap[byteIndex] & (1 << bitIndex)) != 0;
}

static void invalidateNote(uint32_t noteIndex) {
    if (!invalidatedNotesBitmap) {
        return;
    }

    if (noteIndex >= appState.noteTimeEventCount) {
        return;
    }

    uint32_t byteIndex = noteIndex / 8;
    uint8_t bitIndex = noteIndex % 8;

    if (byteIndex >= bitmapSize) {
        return;
    }

    if ((invalidatedNotesBitmap[byteIndex] & (1 << bitIndex)) == 0) {
        invalidatedNotesBitmap[byteIndex] |= (1 << bitIndex);
        totalInvalidatedNotes++;
    }
}

static void removeInvalidatedNotes() {
    if (!appState.noteTimeEvents) {
        return;
    }

    if (appState.noteTimeEventCount == 0) {
        return;
    }

    if (!invalidatedNotesBitmap) {
        return;
    }

    if (totalInvalidatedNotes == 0) {
        return;
    }

    uint32_t newSize = appState.noteTimeEventCount - totalInvalidatedNotes;

    if (newSize == 0) {
        free(appState.noteTimeEvents);
        appState.noteTimeEvents = NULL;
        appState.noteTimeEventCount = 0;

        cumulativeNoteCountOffset += totalInvalidatedNotes;

        free(invalidatedNotesBitmap);
        invalidatedNotesBitmap = NULL;
        bitmapSize = 0;
        totalInvalidatedNotes = 0;

        return;
    }

    NoteTimeEvent* newEvents = (NoteTimeEvent*)malloc(newSize * sizeof(NoteTimeEvent));
    if (!newEvents) {
        fprintf(stderr, "Failed to allocate memory for new note events array\n");
        return;
    }

    uint32_t newIndex = 0;

    for (uint32_t i = 0; i < appState.noteTimeEventCount; i++) {
        if (!isNoteInvalidated(i)) {
            newEvents[newIndex++] = appState.noteTimeEvents[i];
        }
    }

    cumulativeNoteCountOffset += totalInvalidatedNotes;

    free(appState.noteTimeEvents);

    appState.noteTimeEvents = newEvents;
    appState.noteTimeEventCount = newSize;

    initInvalidatedNotesBitmap(newSize);
}

void updatePianoRoll(float currentTime) {
    if (!invalidatedNotesBitmap && appState.noteTimeEventCount > 0) {
        initInvalidatedNotesBitmap(appState.noteTimeEventCount);
    }

    if (appState.noteTimeEvents && appState.noteTimeEventCount > 0) {
        int left = 0;
        int right = appState.noteTimeEventCount - 1;

        while (left <= right) {
            int mid = left + (right - left) / 2;

            if (appState.noteTimeEvents[mid].time <= currentTime) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        uint32_t currentCount = left;

        if (currentCount > cumulativeNoteCount) {
            cumulativeNoteCount = currentCount;
        }
    }
}

uint32_t getPianoRollInvalidatedNoteCount(void) {
    return totalInvalidatedNotes;
}

uint32_t getPianoRollActiveNoteCount(void) {
    return appState.noteTimeEventCount - totalInvalidatedNotes;
}

uint32_t getPianoRollTotalNoteCount(void) {
    if (appState.midiFile) {
        return appState.midiFile->totalNotes;
    }
    return appState.noteTimeEventCount;
}

uint32_t getPianoRollCumulativeNoteCount(void) {
    uint32_t count = cumulativeNoteCount + cumulativeNoteCountOffset;

    uint32_t totalNotes = getPianoRollTotalNoteCount();
    if (count > totalNotes) {
        count = totalNotes;
    }

    return count;
}

uint32_t getPianoRollCumulativeNoteCountOffset(void) {
    return cumulativeNoteCountOffset;
}

// 最適化されたピアノロール範囲描画関数（vectorベース）
void drawPianoRollRange(SkCanvas* canvas, float currentTime, int startIndex, int endIndex) {
    if (!showPianoRoll) return;

    if (!appState.noteTimeEvents) {
        return;
    }

    if (appState.noteTimeEventCount == 0) {
        return;
    }

    // ConfigManagerからfunny設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const FunnyConfig& funnyConfig = configManager->getFunnyConfig();

    if (startIndex < 0) startIndex = 0;
    if (endIndex >= appState.noteTimeEventCount) endIndex = appState.noteTimeEventCount - 1;
    if (startIndex > endIndex) {
        return;
    }

    uint32_t localRenderingNotes = 0;

    // 静的ペイントオブジェクトを事前作成（最適化）
    static SkPaint borderPaint;
    static bool borderInitialized = false;

    if (!borderInitialized) {
        borderPaint.setStyle(SkPaint::kStroke_Style);
        borderPaint.setStrokeWidth(1.0f);
        borderPaint.setAntiAlias(false);
        borderInitialized = true;
    }

    // バッチ処理用のvector（描画データを一時保存）
    struct NoteDrawData {
        int x, y, width, height;
        float r, g, b;
    };
    static std::vector<NoteDrawData> notesToDraw;
    notesToDraw.clear();
    notesToDraw.reserve(1000); // 予想される最大ノート数

    int screenWidth = WIDTH;
    int screenHeight = HEIGHT;

    int keyboardHeight = PIANO_WHITE_KEY_HEIGHT;
    int rollHeight = screenHeight - keyboardHeight;

    float startTime = currentTime;
    float endTime = currentTime + PIANO_ROLL_TIME_RANGE;
    if (startTime < 0.0f) startTime = 0.0f;

    int whiteKeyCount = 75;

    float whiteKeyWidth = (float)screenWidth / whiteKeyCount;
    float blackKeyWidth = whiteKeyWidth * PIANO_BLACK_KEY_WIDTH_RATIO;

    float timeToPixel = (float)rollHeight / PIANO_ROLL_TIME_RANGE;

    // vectorを使用してメモリ効率を改善
    static std::vector<float> noteEndTimes(128, 0.0f);

    // ティックベースの表示範囲を計算（元のロジックを保持）
    uint16_t division = appState.midiFile ? appState.midiFile->header.division : 480;
    uint32_t currentTick = secondsToTick(currentTime, division);
    uint32_t tickRange = (uint32_t)(PIANO_ROLL_TIME_RANGE * division * 1000000.0f / 500000);

    if (tickRange == 0) {
        tickRange = division * 4;
    }

    uint32_t displayStartTick = (currentTick > tickRange) ? (currentTick - tickRange) : 0;
    uint32_t displayEndTick = currentTick + tickRange + (tickRange / 2);

    // 新しいイベントが手前に表示されるように逆順で描画
    for (int i = endIndex; i >= startIndex; i--) {
        if (i < 0 || i >= appState.noteTimeEventCount) {
            continue;
        }

        NoteTimeEvent* note = &appState.noteTimeEvents[i];

        float noteEndTime = note->time + note->duration;
        uint32_t noteStartTick = note->tick;
        uint32_t noteEndTick = note->tick + note->tickDuration;

        // 長いノートかどうかを判定
        bool isLongNote = (note->duration >= 1.0f || note->tickDuration >= division * 4);

        // 現在再生中のノートかどうか
        bool isNoteActive = (note->time <= currentTime && noteEndTime > currentTime);

        // より安全な可視性チェック - 元のロジックと同様の判定を使用
        bool isPartiallyVisible = false;

        // ティックベースでの可視性チェック（元のコードと同じ）
        if (noteStartTick <= displayEndTick && noteEndTick >= displayStartTick) {
            isPartiallyVisible = true;
        }

        // 現在再生中のノートは常に表示
        if (isNoteActive) {
            isPartiallyVisible = true;
        }

        // 可視性チェックを通らないノートのみスキップ
        if (!isPartiallyVisible) {
            continue;
        }

        // 以下は元のコードと同じ描画処理
        uint8_t noteNumber = note->note;
        noteEndTimes[noteNumber] = noteEndTime;

        float noteX;
        float noteWidth;

        if (isBlackKey(note->note)) {
            int whiteKeyIndex = getBlackKeyPosition(note->note);
            noteX = whiteKeyIndex * whiteKeyWidth + whiteKeyWidth - blackKeyWidth / 2;
            noteWidth = blackKeyWidth;
            noteX = (float)((int)noteX);
            noteWidth = (float)((int)noteWidth);
        } else {
            int whiteKeyIndex = getWhiteKeyIndex(note->note);
            noteX = whiteKeyIndex * whiteKeyWidth;
            noteWidth = whiteKeyWidth;
            noteX = (float)((int)noteX);
            noteWidth = (float)((int)noteWidth);
        }

        float tickToPixel = (float)rollHeight / tickRange;

        float noteY = 0;
        float noteHeight = 0;

        uint32_t tickDuration = note->tickDuration;
        if (tickDuration == 0) {
            tickDuration = 1;
        }

        bool isCurrentlyPlaying = (note->time <= currentTime && note->time + note->duration > currentTime);

        if (note->tick <= currentTick) {
            if (note->tick + tickDuration > currentTick) {
                uint32_t remainingTicks = (note->tick + tickDuration) - currentTick;

                float noteBottom = rollHeight;

                float relativeDuration = (float)remainingTicks / (float)tickRange;
                noteHeight = relativeDuration * rollHeight;

                float minHeight;
                if (isLongNote) {
                    minHeight = 30.0f;
                } else if (isCurrentlyPlaying) {
                    minHeight = 15.0f;
                } else {
                    minHeight = 5.0f;
                }

                if (noteHeight < minHeight) {
                    noteHeight = minHeight;
                }

                noteY = noteBottom - noteHeight;

                noteY = (float)((int)noteY);
                noteHeight = (float)((int)noteHeight);
            } else {
                continue;
            }
        } else {
            uint32_t ticksFromNow = note->tick - currentTick;

            float relativePosition = (float)ticksFromNow / (float)tickRange;

            float relativeDuration = (float)tickDuration / (float)tickRange;

            float noteBottom;

            float displayPosition = relativePosition;

            if (displayPosition > 1.2f) {
                continue;
            }

            float clampedPosition = fmin(displayPosition, 1.0f);
            noteBottom = rollHeight - (clampedPosition * rollHeight);
            noteBottom = (float)((int)noteBottom);

            if (noteBottom <= 0 && displayPosition <= 1.2f) {
                noteBottom = 0;

                if (isLongNote) {
                    noteHeight = 15.0f;
                } else if (isCurrentlyPlaying) {
                    noteHeight = 10.0f;
                } else {
                    noteHeight = 8.0f;
                }
            }

            if (noteBottom > 0) {
                noteHeight = relativeDuration * rollHeight;

                float minHeight;
                if (isLongNote) {
                    minHeight = 20.0f;
                } else if (isCurrentlyPlaying) {
                    minHeight = 10.0f;
                } else {
                    minHeight = 5.0f;
                }

                noteHeight = (float)((int)noteHeight);

                if (noteHeight < minHeight) {
                    noteHeight = minHeight;
                }

                if (isLongNote) {
                    if (noteHeight > rollHeight * 0.9f) {
                        noteHeight = rollHeight * 0.9f;
                    }
                    if (noteHeight < 50.0f) {
                        noteHeight = 50.0f;
                    }
                }

                if (isCurrentlyPlaying && noteHeight < 10.0f) {
                    noteHeight = 10.0f;
                }
            }

            noteY = noteBottom - noteHeight;

            if (noteY < 0) {
                if (noteY + noteHeight > 0) {
                    noteHeight = noteY + noteHeight;
                    noteY = 0;

                    if (noteHeight < 3.0f) {
                        noteHeight = 3.0f;
                    }
                } else {
                    continue;
                }
            }

            if (noteY + noteHeight > rollHeight) {
                noteHeight = rollHeight - noteY;

                if (noteHeight <= 0) {
                    continue;
                }
            }
        }

        if (noteHeight <= 0) {
            continue;
        }

        // 設定マネージャーから色を取得（色モードに基づいて適切な色を選択）
        unsigned int color = ConfigManager::getInstance()->getPianoColor(note->channel, note->track);
        double r = ((color >> 16) & 0xFF) / 255.0;
        double g = ((color >> 8) & 0xFF) / 255.0;
        double b = (color & 0xFF) / 255.0;

        // ハイライト効果が有効な場合のみ明度を上げる
        ConfigManager* configManager = ConfigManager::getInstance();
        const VisualConfig& visualConfig = configManager->getVisualConfig();

        if (isNoteActive && visualConfig.showHighlight) {
            float brightnessFactor = 2.5f;
            r = fmin(r * brightnessFactor, 1.0);
            g = fmin(g * brightnessFactor, 1.0);
            b = fmin(b * brightnessFactor, 1.0);
        }

        int intNoteX = (int)(noteX + 0.5f);
        int intNoteY = (int)(noteY + 0.5f);
        int intNoteWidth = (int)(noteWidth + 0.5f);
        int intNoteHeight = (int)(noteHeight + 0.5f);

        if (intNoteWidth < 1) intNoteWidth = 1;
        if (intNoteHeight < 1) intNoteHeight = 1;

        // バグモード時の面白い効果
        if (funnyConfig.bugMode) {
            // ランダムな確率でグリッチ効果を適用
            static int glitchCounter = 0;
            glitchCounter++;

            // 頂点バッファ破損効果（誤ったインデックス）
            if (glitchCounter % 7 == 0) { // 7フレームに1回の確率
                // 頂点座標が範囲外（NaNや±∞など）
                if (rand() % 3 == 0) {
                    intNoteX = (rand() % 2 == 0) ? -99999 : 99999; // 極端に大きな値
                    intNoteY = (rand() % 2 == 0) ? -99999 : 99999;
                    intNoteWidth = (rand() % 2 == 0) ? 199999 : -199999; // 負の値も含む
                    intNoteHeight = (rand() % 2 == 0) ? 199999 : -199999;
                } else {
                    // 通常のグリッチ効果
                    intNoteX += (rand() % 21) - 10; // -10 to +10 pixels
                    intNoteY += (rand() % 11) - 5;  // -5 to +5 pixels
                    intNoteWidth += (rand() % 21) - 10; // -10 to +10 pixels
                    intNoteHeight += (rand() % 11) - 5; // -5 to +5 pixels
                }

                // 最小サイズを保証（正常値の場合のみ）
                if (abs(intNoteWidth) < 99999 && intNoteWidth < 1) intNoteWidth = 1;
                if (abs(intNoteHeight) < 99999 && intNoteHeight < 1) intNoteHeight = 1;

                // 頂点カラーの補間異常
                r = (rand() % 256) / 255.0f;
                g = (rand() % 256) / 255.0f;
                b = (rand() % 256) / 255.0f;
            }

            // シェーダーの問題（頂点シェーダーが出力する座標の異常）
            if (glitchCounter % 11 == 0) { // 11フレームに1回の確率
                // 巨大なポリゴンが描かれる効果
                int extremeX = intNoteX + (rand() % 4000) - 2000; // 画面外まで伸びる
                int extremeY = intNoteY + (rand() % 4000) - 2000;
                int extremeW = intNoteWidth + (rand() % 8000) - 4000;
                int extremeH = intNoteHeight + (rand() % 8000) - 4000;

                // 破綻したポリゴンを描画
                drawTextureWithColor(canvas, TEXTURE_NOTE, extremeX, extremeY, extremeW, extremeH,
                                   r, g, b, 0.3f);
            }

            // 投影行列破損効果（座標変換の異常）
            if (glitchCounter % 13 == 0) { // 13フレームに1回の確率
                // Near/Far平面の設定が壊れている効果
                canvas->save();

                // 異常なスケーリング
                float corruptedScale = (rand() % 2 == 0) ? (rand() % 50 + 1) : 1.0f / (rand() % 50 + 1);
                canvas->scale(corruptedScale, corruptedScale);

                drawTextureWithColor(canvas, TEXTURE_NOTE, intNoteX, intNoteY, intNoteWidth, intNoteHeight,
                                   1.0f - r, 1.0f - g, 1.0f - b, 0.5f);

                canvas->restore();
            }

            // 時々ノートを複製して描画（ダブルバッファ破損効果）
            if (glitchCounter % 17 == 0) { // 17フレームに1回の確率
                // 深度テスト異常で本来見えないはずのノートが見える
                for (int i = 0; i < 3; i++) {
                    int ghostX = intNoteX + (rand() % 401) - 200; // -200 to +200 pixels
                    int ghostY = intNoteY + (rand() % 401) - 200; // -200 to +200 pixels
                    float ghostAlpha = 0.2f + (rand() % 60) / 100.0f; // 0.2 to 0.8 alpha

                    drawTextureWithColor(canvas, TEXTURE_NOTE, ghostX, ghostY, intNoteWidth, intNoteHeight,
                                       1.0f - r, 1.0f - g, 1.0f - b, ghostAlpha);
                }
            }
        }

        drawTextureWithColor(canvas, TEXTURE_NOTE, intNoteX, intNoteY, intNoteWidth, intNoteHeight, r, g, b, PIANO_ROLL_ALPHA);

        // 最適化されたボーダー描画
        static SkPaint borderPaint;
        static bool borderInitialized = false;

        if (!borderInitialized) {
            borderPaint.setStyle(SkPaint::kStroke_Style);
            borderPaint.setStrokeWidth(1.0f);
            borderPaint.setAntiAlias(false);
            borderInitialized = true;
        }

        borderPaint.setColor(SkColorSetARGB((int)(PIANO_ROLL_ALPHA * 255), (int)(r * 0.3 * 255), (int)(g * 0.3 * 255), (int)(b * 0.3 * 255)));
        canvas->drawRect(SkRect::MakeXYWH(intNoteX, intNoteY, intNoteWidth, intNoteHeight), borderPaint);

        localRenderingNotes++;
    }

    setCurrentRenderingNotes(localRenderingNotes);
}

// 最適化されたピアノロール描画関数
void drawPianoRoll(SkCanvas* canvas, float currentTime) {
    // ConfigManagerからvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();
    const FunnyConfig& funnyConfig = configManager->getFunnyConfig();

    if (!visualConfig.showPianoroll || !appState.noteTimeEvents || appState.noteTimeEventCount == 0) return;

    startPerformanceMeasurement("PianoRollDraw");

    if (PIANO_ROLL_CACHE_ENABLED) {
        updatePianoRollCache(currentTime);

        if (isPianoRollCacheValid()) {
            drawPianoRollCache(canvas);

            endPerformanceMeasurement("PianoRollDraw");
            return;
        }
    }

    // 定数値を事前計算
    const int screenWidth = WIDTH;
    const int screenHeight = HEIGHT;
    const int keyboardHeight = PIANO_WHITE_KEY_HEIGHT;
    const int rollHeight = screenHeight - keyboardHeight;
    const int whiteKeyCount = 75;
    const float whiteKeyWidth = (float)screenWidth / whiteKeyCount;

    // 毎回ColorConfigから色を取得して使用する（設定ファイルの変更を反映するため）
    SkPaint bgPaint;
    SkPaint linePaint;
    SkPaint octavePaint;

    // ConfigManagerから色を取得（既に取得済みのconfigManagerを使用）
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    // 背景色（設定から取得したものを半透明に）
    uint32_t bgColor = colorConfig.backgroundColor;
    uint8_t r = (bgColor >> 16) & 0xFF;
    uint8_t g = (bgColor >> 8) & 0xFF;
    uint8_t b = bgColor & 0xFF;
    uint8_t a = 0xFF;
    bgPaint.setColor(SkColorSetARGB(a, r, g, b));
    
    // グリッド線の色
    uint32_t gridColor = colorConfig.gridLineColor;
    uint8_t gr = (gridColor >> 16) & 0xFF;
    uint8_t gg = (gridColor >> 8) & 0xFF;
    uint8_t gb = gridColor & 0xFF;
    uint8_t ga = 128; // 半透明（50%）
    linePaint.setColor(SkColorSetARGB(ga, gr, gg, gb));
    linePaint.setStrokeWidth(2.0f);
    linePaint.setStyle(SkPaint::kStroke_Style);
    
    // オクターブ線の色（グリッド色の薄いバージョンを使用）
    uint8_t oa = 77; // 半透明（30%）
    octavePaint.setColor(SkColorSetARGB(oa, gr, gg, gb));
    octavePaint.setStrokeWidth(1.0f);
    octavePaint.setStyle(SkPaint::kStroke_Style);

    // 背景を描画
    canvas->drawRect(SkRect::MakeXYWH(0, 0, screenWidth, rollHeight), bgPaint);

    // バグモード時の頂点データ破損効果（ピアノロール用）
    if (funnyConfig.bugMode) {
        static int rollCorruptionCounter = 0;
        rollCorruptionCounter++;

        // シェーダー問題のシミュレーション（gl_Position異常）
        if (rollCorruptionCounter % 29 == 0) { // 29フレームに1回の確率
            // 巨大なポリゴンが描かれる効果
            SkPaint corruptedPaint;

            // 頂点座標が範囲外（NaNや±∞のシミュレーション）
            float extremeCoords[] = {
                -999999.0f, 999999.0f, -999999.0f, 999999.0f,
                (float)screenWidth * 10, (float)screenHeight * 10,
                -(float)screenWidth * 5, -(float)screenHeight * 5
            };

            int coordIndex = rand() % 8;
            float corruptedX = extremeCoords[coordIndex];
            float corruptedY = extremeCoords[(coordIndex + 1) % 8];
            float corruptedW = extremeCoords[(coordIndex + 2) % 8];
            float corruptedH = extremeCoords[(coordIndex + 3) % 8];

            // 頂点カラーの補間異常（全体がグラデーションに）
            SkPoint gradientPoints[2] = {
                {corruptedX, corruptedY},
                {corruptedX + corruptedW, corruptedY + corruptedH}
            };
            SkColor gradientColors[3] = {
                SkColorSetARGB(80, rand() % 256, rand() % 256, rand() % 256),
                SkColorSetARGB(120, rand() % 256, rand() % 256, rand() % 256),
                SkColorSetARGB(60, rand() % 256, rand() % 256, rand() % 256)
            };
            float positions[3] = {0.0f, 0.5f, 1.0f};

            auto shader = SkGradientShader::MakeLinear(gradientPoints, gradientColors, positions, 3, SkTileMode::kRepeat);
            corruptedPaint.setShader(shader);

            // 破綻したポリゴンを描画
            canvas->drawRect(SkRect::MakeXYWH(corruptedX, corruptedY, corruptedW, corruptedH), corruptedPaint);
        }

        // シンプルな頂点データ破壊による美しいグラデーション効果
        if (rollCorruptionCounter % 41 == 0) { // 41フレームに1回の確率
            // 頂点バッファの破損をシミュレート - 座標が異常な値になる
            SkPaint corruptedPaint;

            // 破損した頂点データによる美しいグラデーション
            SkPoint gradientPoints[2] = {
                {(float)(rand() % screenWidth), (float)(rand() % rollHeight)},
                {(float)(rand() % screenWidth), (float)(rand() % rollHeight)}
            };

            // 画像のような美しい色合い
            SkColor gradientColors[] = {
                SkColorSetARGB(180, 0, 255, 255),    // シアン
                SkColorSetARGB(180, 100, 150, 255),  // 青紫
                SkColorSetARGB(180, 255, 0, 200),    // マゼンタ
                SkColorSetARGB(180, 255, 150, 0)     // オレンジ
            };

            auto gradientShader = SkGradientShader::MakeLinear(gradientPoints, gradientColors, nullptr, 4, SkTileMode::kClamp);
            corruptedPaint.setShader(gradientShader);

            // 破損した矩形描画（頂点座標が異常）
            float corruptedX = (rand() % (screenWidth * 2)) - screenWidth / 2;
            float corruptedY = (rand() % (rollHeight * 2)) - rollHeight / 2;
            float corruptedW = screenWidth + (rand() % screenWidth);
            float corruptedH = rollHeight + (rand() % rollHeight);

            canvas->drawRect(SkRect::MakeXYWH(corruptedX, corruptedY, corruptedW, corruptedH), corruptedPaint);

            // 中央分割線（画像の特徴）
            SkPaint linePaint;
            linePaint.setColor(SkColorSetARGB(100, 255, 255, 255));
            linePaint.setStrokeWidth(2.0f);
            canvas->drawLine(screenWidth / 2.0f, 0, screenWidth / 2.0f, rollHeight, linePaint);
        }

        // ダブルバッファ破損や描画ステート異常の効果
        if (rollCorruptionCounter % 47 == 0) { // 47フレームに1回の確率
            // 深度テスト、カリング、ビューポート設定の異常
            SkPaint statePaint;

            // ビューポート設定異常（画面の一部だけ描画される）
            canvas->save();

            // 異常なクリッピング領域
            SkRect corruptedClip = SkRect::MakeXYWH(
                (rand() % screenWidth) - screenWidth / 2,
                (rand() % rollHeight) - rollHeight / 2,
                rand() % (screenWidth * 2),
                rand() % (rollHeight * 2)
            );
            canvas->clipRect(corruptedClip);

            // カリング異常（本来見えないはずの面が見える）
            for (int i = 0; i < 10; i++) {
                float x = (rand() % (screenWidth * 3)) - screenWidth;
                float y = (rand() % (rollHeight * 3)) - rollHeight;
                float w = rand() % 100 + 20;
                float h = rand() % 100 + 20;

                statePaint.setColor(SkColorSetARGB(
                    rand() % 200 + 55,
                    rand() % 256,
                    rand() % 256,
                    rand() % 256
                ));

                canvas->drawRect(SkRect::MakeXYWH(x, y, w, h), statePaint);
            }

            canvas->restore();
        }
    }

    // 下部の線を描画
    canvas->drawLine(0, rollHeight - 1, screenWidth, rollHeight - 1, linePaint);

    // オクターブ線を描画
    for (int octave = 0; octave <= 10; octave++) {
        int noteNum = octave * 12;
        if (noteNum >= PIANO_ROLL_MIN_NOTE && noteNum <= PIANO_ROLL_MAX_NOTE) {
            int whiteKeyIndex = getWhiteKeyIndex(noteNum);
            float x = whiteKeyIndex * whiteKeyWidth;

            // バグモード時のオクターブ線の効果
            if (funnyConfig.bugMode) {
                static int lineGlitchCounter = 0;
                lineGlitchCounter++;

                if (lineGlitchCounter % 19 == 0) { // 19フレームに1回の確率
                    // 線の位置をランダムにずらす
                    x += (rand() % 21) - 10; // -10 to +10 pixels

                    // 線の色をランダムに変更
                    SkPaint glitchPaint = octavePaint;
                    uint8_t randomR = rand() % 256;
                    uint8_t randomG = rand() % 256;
                    uint8_t randomB = rand() % 256;
                    glitchPaint.setColor(SkColorSetARGB(77, randomR, randomG, randomB));

                    // 複数の線を描画
                    for (int i = 0; i < 3; i++) {
                        float offsetX = x + (rand() % 11) - 5; // -5 to +5 pixels
                        canvas->drawLine(offsetX, 0, offsetX, rollHeight, glitchPaint);
                    }
                } else {
                    canvas->drawLine(x, 0, x, rollHeight, octavePaint);
                }
            } else {
                canvas->drawLine(x, 0, x, rollHeight, octavePaint);
            }
        }
    }

    // ノートを描画
    int startIndex = 0;
    int endIndex = appState.noteTimeEventCount - 1;
    drawPianoRollRange(canvas, currentTime, startIndex, endIndex);

    endPerformanceMeasurement("PianoRollDraw");

    canvas->flush();
}

void cleanupPianoRoll(void) {
    if (invalidatedNotesBitmap) {
        free(invalidatedNotesBitmap);
        invalidatedNotesBitmap = NULL;
        bitmapSize = 0;
    }

    totalInvalidatedNotes = 0;

    cleanupPianoRollThreadPool();

    cleanupPianoRollCache();
}

void initPianoRollCache(int width, int height) {
    cleanupPianoRollCache();

    if (width <= 0 || height <= 0) {
        fprintf(stderr, "Error: Invalid piano roll cache size (%dx%d)\n", width, height);
        return;
    }

    // GPUサーフェスを優先的に作成
    sk_sp<SkSurface> surface = nullptr;

#ifdef GPU_RENDERING_ENABLED
    if (isGPUAvailable()) {
        surface = createGPUSurface(width, height);
    }
#endif

    // GPUサーフェスの作成に失敗した場合はCPUサーフェスにフォールバック
    if (!surface) {
        surface = SkSurface::MakeRasterN32Premul(width, height);
    }

    pianoRollCache.surface = surface;
    if (!pianoRollCache.surface) {
        fprintf(stderr, "Error: Failed to create piano roll cache surface\n");
        return;
    }

    pianoRollCache.lastUpdateTime = 0.0f;
    pianoRollCache.valid = false;
    pianoRollCache.width = width;
    pianoRollCache.height = height;

    printf("Piano roll cache initialized with size %dx%d\n", width, height);
}

void updatePianoRollCache(float currentTime) {
    if (!PIANO_ROLL_CACHE_ENABLED) {
        pianoRollCache.valid = false;
        return;
    }

    bool needsUpdate = !pianoRollCache.valid ||
                       fabs(currentTime - pianoRollCache.lastUpdateTime) >= PIANO_ROLL_CACHE_UPDATE_INTERVAL;

    if (needsUpdate) {
        if (!pianoRollCache.surface) {
            int keyboardHeight = PIANO_WHITE_KEY_HEIGHT;
            int rollHeight = HEIGHT - keyboardHeight;
            if (WIDTH > 0 && rollHeight > 0) {
                initPianoRollCache(WIDTH, rollHeight);
            } else {
                return;
            }
        }

        if (!pianoRollCache.surface) {
            fprintf(stderr, "Error: Piano roll cache surface is null, cannot update cache.\n");
            pianoRollCache.valid = false;
            return;
        }

        startPerformanceMeasurement("PianoRollCacheUpdate");

        SkCanvas* cacheCanvas = pianoRollCache.surface->getCanvas();
        cacheCanvas->clear(SK_ColorTRANSPARENT);

        renderPianoRollToCanvas(cacheCanvas, currentTime);

        pianoRollCache.image = pianoRollCache.surface->makeImageSnapshot();
        if (!pianoRollCache.image) {
             fprintf(stderr, "Error: Failed to create piano roll cache image snapshot.\n");
             pianoRollCache.valid = false;
        } else {
            pianoRollCache.valid = true;
        }
        pianoRollCache.lastUpdateTime = currentTime;

        endPerformanceMeasurement("PianoRollCacheUpdate");
    }
}

void drawPianoRollCache(SkCanvas* canvas) {
    if (pianoRollCache.valid && pianoRollCache.image) {
        canvas->drawImage(pianoRollCache.image, 0, 0);
    }
}

void cleanupPianoRollCache(void) {
    pianoRollCache.surface.reset();
    pianoRollCache.image.reset();
    pianoRollCache.valid = false;
}

bool isPianoRollCacheValid(void) {
    return pianoRollCache.valid;
}

void invalidatePianoRollCache(void) {
    pianoRollCache.valid = false;
}

void renderPianoRollToCanvas(SkCanvas* targetCanvas, float timeForRender) {
    // ConfigManagerからvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    if (!visualConfig.showPianoroll || !appState.noteTimeEvents || appState.noteTimeEventCount == 0) return;

    int screenWidth = WIDTH;
    int screenHeight = HEIGHT;

    int keyboardHeight = PIANO_WHITE_KEY_HEIGHT;
    int rollHeight = screenHeight - keyboardHeight;

    int whiteKeyCount = 75;

    float whiteKeyWidth = (float)screenWidth / whiteKeyCount;

    // ConfigManagerから色を取得（既に取得済みのconfigManagerを使用）
    const ColorConfig& colorConfig = configManager->getColorConfig();
    
    // 背景色
    SkPaint bgPaint;
    uint32_t bgColor = colorConfig.backgroundColor;
    uint8_t r = (bgColor >> 16) & 0xFF;
    uint8_t g = (bgColor >> 8) & 0xFF;
    uint8_t b = bgColor & 0xFF;
    uint8_t a = 77; // 半透明（30%）
    bgPaint.setColor(SkColorSetARGB(a, r, g, b));
    targetCanvas->drawRect(SkRect::MakeXYWH(0, 0, screenWidth, rollHeight), bgPaint);

    // グリッド線の色
    SkPaint linePaint;
    uint32_t gridColor = colorConfig.gridLineColor;
    uint8_t gr = (gridColor >> 16) & 0xFF;
    uint8_t gg = (gridColor >> 8) & 0xFF;
    uint8_t gb = gridColor & 0xFF;
    uint8_t ga = 128; // 半透明（50%）
    linePaint.setColor(SkColorSetARGB(ga, gr, gg, gb));
    linePaint.setStrokeWidth(2.0f);
    linePaint.setStyle(SkPaint::kStroke_Style);
    targetCanvas->drawLine(0, rollHeight - 1, screenWidth, rollHeight - 1, linePaint);

    // オクターブ線の色
    SkPaint octavePaint;
    uint8_t oa = 77; // 半透明（30%）
    octavePaint.setColor(SkColorSetARGB(oa, gr, gg, gb));
    octavePaint.setStrokeWidth(1.0f);
    octavePaint.setStyle(SkPaint::kStroke_Style);
    for (int octave = 0; octave <= 10; octave++) {
        int noteNum = octave * 12;
        if (noteNum >= PIANO_ROLL_MIN_NOTE && noteNum <= PIANO_ROLL_MAX_NOTE) {
            int whiteKeyIndex = getWhiteKeyIndex(noteNum);
            float x = whiteKeyIndex * whiteKeyWidth;
            targetCanvas->drawLine(x, 0, x, rollHeight, octavePaint);
        }
    }

    int startIndex = 0;
    int endIndex = appState.noteTimeEventCount - 1;

    drawPianoRollRange(targetCanvas, timeForRender, startIndex, endIndex);
}