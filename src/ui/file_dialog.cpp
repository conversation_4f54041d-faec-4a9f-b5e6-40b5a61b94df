#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// プラットフォーム別のヘッダー
#ifdef _WIN32
#include <windows.h>
#include <commdlg.h>
#include <shlobj.h>
#include <tchar.h>
#include <wchar.h>
#elif defined(__linux__)
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>
#include <libgen.h>
#include <limits.h>
#endif

#include "../../include/file_dialog.h"
#include "../../include/app_state.h"

// ファイルダイアログを表示してMIDIファイルを選択する関数
char* openMidiFileDialog() {
#ifdef _WIN32
    OPENFILENAMEW ofn;
    static wchar_t szFileW[MAX_PATH_LENGTH] = {0};
    static char szFileUtf8[MAX_PATH_LENGTH] = {0};
    wmemset(szFileW, 0, MAX_PATH_LENGTH);
    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = NULL;
    ofn.lpstrFile = szFileW;
    ofn.nMaxFile = MAX_PATH_LENGTH;
    ofn.lpstrFilter = L"MIDI Files\0*.mid;*.midi\0All Files\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
    if (GetOpenFileNameW(&ofn)) {
        WideCharToMultiByte(CP_UTF8, 0, szFileW, -1, szFileUtf8, MAX_PATH_LENGTH, NULL, NULL);
        return szFileUtf8;
    }
    return NULL;
#elif defined(__linux__)
    // Linux: zenityまたはkdialogを使用してファイルダイアログを表示
    static char selectedFile[MAX_PATH_LENGTH] = {0};
    FILE* fp;

    // zenityを試す
    fp = popen("zenity --file-selection --file-filter='MIDI Files | *.mid *.midi' --file-filter='All Files | *' 2>/dev/null", "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            // 改行文字を削除
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // zenityが失敗した場合、kdialogを試す
    fp = popen("kdialog --getopenfilename . '*.mid *.midi|MIDI Files' 2>/dev/null", "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // GUIダイアログが使用できない場合、コマンドラインで入力を求める
    printf("Please enter the path to the MIDI file: ");
    if (fgets(selectedFile, sizeof(selectedFile), stdin) != NULL) {
        selectedFile[strcspn(selectedFile, "\n")] = '\0';
        if (strlen(selectedFile) > 0) {
            return selectedFile;
        }
    }

    return NULL;
#else
    // その他のプラットフォーム（macOS等）
    printf("File dialog not implemented for this platform\n");
    return NULL;
#endif
}

// ファイルダイアログを表示してオーディオファイルを選択する関数
char* openAudioFileDialog() {
#ifdef _WIN32
    OPENFILENAMEW ofn;
    static wchar_t szFileW[MAX_PATH_LENGTH] = {0};
    static char szFileUtf8[MAX_PATH_LENGTH] = {0};
    wmemset(szFileW, 0, MAX_PATH_LENGTH);
    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = NULL;
    ofn.lpstrFile = szFileW;
    ofn.nMaxFile = MAX_PATH_LENGTH;
    ofn.lpstrFilter = L"Audio Files\0*.mp3;*.wav;*.ogg;*.flac\0All Files\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
    if (GetOpenFileNameW(&ofn)) {
        WideCharToMultiByte(CP_UTF8, 0, szFileW, -1, szFileUtf8, MAX_PATH_LENGTH, NULL, NULL);
        return szFileUtf8;
    }
    return NULL;
#elif defined(__linux__)
    // Linux: zenityまたはkdialogを使用してファイルダイアログを表示
    static char selectedFile[MAX_PATH_LENGTH] = {0};
    FILE* fp;

    // zenityを試す
    fp = popen("zenity --file-selection --file-filter='Audio Files | *.mp3 *.wav *.ogg *.flac' --file-filter='All Files | *' 2>/dev/null", "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // zenityが失敗した場合、kdialogを試す
    fp = popen("kdialog --getopenfilename . '*.mp3 *.wav *.ogg *.flac|Audio Files' 2>/dev/null", "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // GUIダイアログが使用できない場合、コマンドラインで入力を求める
    printf("Please enter the path to the audio file: ");
    if (fgets(selectedFile, sizeof(selectedFile), stdin) != NULL) {
        selectedFile[strcspn(selectedFile, "\n")] = '\0';
        if (strlen(selectedFile) > 0) {
            return selectedFile;
        }
    }

    return NULL;
#else
    // その他のプラットフォーム（macOS等）
    printf("File dialog not implemented for this platform\n");
    return NULL;
#endif
}

// ファイル保存ダイアログを表示して出力ファイルを選択する関数
char* saveOutputFileDialog(const char* defaultFileName) {
#ifdef _WIN32
    OPENFILENAMEW ofn;
    static wchar_t szFile[MAX_PATH_LENGTH] = {0};

    // バッファを明示的にゼロクリア
    wmemset(szFile, 0, MAX_PATH_LENGTH);
    ZeroMemory(&ofn, sizeof(ofn));

    // デフォルトのファイル名をUTF-8→UTF-16変換してコピー
    MultiByteToWideChar(CP_UTF8, 0, defaultFileName, -1, szFile, MAX_PATH_LENGTH - 1);

    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = NULL;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = MAX_PATH_LENGTH;
    ofn.lpstrFilter = L"MP4 Files\0*.mp4\0All Files\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.Flags = OFN_OVERWRITEPROMPT | OFN_PATHMUSTEXIST;
    ofn.lpstrDefExt = L"mp4";

    if (GetSaveFileNameW(&ofn)) {
        // UTF-16→UTF-8変換して返す（staticバッファ）
        static char szFileUtf8[MAX_PATH_LENGTH] = {0};
        WideCharToMultiByte(CP_UTF8, 0, szFile, -1, szFileUtf8, MAX_PATH_LENGTH, NULL, NULL);
        return szFileUtf8;
    }

    return NULL;
#elif defined(__linux__)
    // Linux: zenityまたはkdialogを使用してファイル保存ダイアログを表示
    static char selectedFile[MAX_PATH_LENGTH] = {0};
    FILE* fp;
    char command[MAX_PATH_LENGTH * 2];

    // zenityを試す
    snprintf(command, sizeof(command), "zenity --file-selection --save --confirm-overwrite --filename='%s' --file-filter='MP4 Files | *.mp4' --file-filter='All Files | *' 2>/dev/null", defaultFileName);
    fp = popen(command, "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // zenityが失敗した場合、kdialogを試す
    snprintf(command, sizeof(command), "kdialog --getsavefilename '%s' '*.mp4|MP4 Files' 2>/dev/null", defaultFileName);
    fp = popen(command, "r");
    if (fp != NULL) {
        if (fgets(selectedFile, sizeof(selectedFile), fp) != NULL) {
            selectedFile[strcspn(selectedFile, "\n")] = '\0';
            pclose(fp);
            if (strlen(selectedFile) > 0) {
                return selectedFile;
            }
        }
        pclose(fp);
    }

    // GUIダイアログが使用できない場合、コマンドラインで入力を求める
    printf("Please enter the output file path (default: %s): ", defaultFileName);
    if (fgets(selectedFile, sizeof(selectedFile), stdin) != NULL) {
        selectedFile[strcspn(selectedFile, "\n")] = '\0';
        if (strlen(selectedFile) > 0) {
            return selectedFile;
        } else {
            // 空の場合はデフォルトファイル名を使用
            strncpy(selectedFile, defaultFileName, MAX_PATH_LENGTH - 1);
            selectedFile[MAX_PATH_LENGTH - 1] = '\0';
            return selectedFile;
        }
    }

    return NULL;
#else
    // その他のプラットフォーム（macOS等）
    printf("File dialog not implemented for this platform\n");
    return NULL;
#endif
}

// 日本語を含まないパスを作成する関数
char* createNonJapanesePath(const char* originalPath, const char* extension, const char* prefix) {
    static char tempPath[MAX_PATH_LENGTH];

#ifdef _WIN32
    wchar_t wOutputDir[MAX_PATH_LENGTH];
    wchar_t wOriginalPath[MAX_PATH_LENGTH];
    wchar_t wPrefix[MAX_PATH_LENGTH];
    wchar_t wExtension[MAX_PATH_LENGTH];

    // UTF-8→UTF-16変換
    MultiByteToWideChar(CP_UTF8, 0, originalPath, -1, wOriginalPath, MAX_PATH_LENGTH - 1);
    MultiByteToWideChar(CP_UTF8, 0, prefix, -1, wPrefix, MAX_PATH_LENGTH - 1);
    if (!extension || strlen(extension) == 0) {
        wcscpy(wExtension, L".mp4");
    } else {
        MultiByteToWideChar(CP_UTF8, 0, extension, -1, wExtension, MAX_PATH_LENGTH - 1);
    }

    // 出力ディレクトリを取得
    const wchar_t *lastPathSep = wcsrchr(wOriginalPath, L'\\');
    if (!lastPathSep) lastPathSep = wcsrchr(wOriginalPath, L'/');

    if (lastPathSep) {
        int dirLength = (int)(lastPathSep - wOriginalPath);
        if (dirLength < MAX_PATH_LENGTH - 1) {
            wcsncpy(wOutputDir, wOriginalPath, dirLength);
            wOutputDir[dirLength] = L'\0';
        } else {
            GetCurrentDirectoryW(MAX_PATH_LENGTH, wOutputDir);
        }
    } else {
        GetCurrentDirectoryW(MAX_PATH_LENGTH, wOutputDir);
    }

    // 一時ファイル名を生成
    wchar_t wTempPath[MAX_PATH_LENGTH];
    swprintf(wTempPath, MAX_PATH_LENGTH, L"%s\\%s_temp%s", wOutputDir, wPrefix, wExtension);
    // UTF-16→UTF-8変換
    WideCharToMultiByte(CP_UTF8, 0, wTempPath, -1, tempPath, MAX_PATH_LENGTH, NULL, NULL);
    return tempPath;
#elif defined(__linux__)
    char outputDir[MAX_PATH_LENGTH];
    char finalExtension[64];

    // 拡張子の設定
    if (!extension || strlen(extension) == 0) {
        strcpy(finalExtension, ".mp4");
    } else {
        strncpy(finalExtension, extension, sizeof(finalExtension) - 1);
        finalExtension[sizeof(finalExtension) - 1] = '\0';
    }

    // 出力ディレクトリを取得
    const char *lastPathSep = strrchr(originalPath, '/');
    if (!lastPathSep) lastPathSep = strrchr(originalPath, '\\');

    if (lastPathSep) {
        int dirLength = (int)(lastPathSep - originalPath);
        if (dirLength < MAX_PATH_LENGTH - 1) {
            strncpy(outputDir, originalPath, dirLength);
            outputDir[dirLength] = '\0';
        } else {
            if (getcwd(outputDir, MAX_PATH_LENGTH) == NULL) {
                strcpy(outputDir, ".");
            }
        }
    } else {
        if (getcwd(outputDir, MAX_PATH_LENGTH) == NULL) {
            strcpy(outputDir, ".");
        }
    }

    // 一時ファイル名を生成
    snprintf(tempPath, MAX_PATH_LENGTH, "%s/%s_temp%s", outputDir, prefix, finalExtension);
    return tempPath;
#else
    // その他のプラットフォーム（macOS等）
    snprintf(tempPath, MAX_PATH_LENGTH, "%s_temp%s", prefix, extension ? extension : ".mp4");
    return tempPath;
#endif
}
