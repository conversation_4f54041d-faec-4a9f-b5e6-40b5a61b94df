#include "../../include/ui/piano_keyboard.h"
#include "../../include/app_state.h" // WIDTH, HEIGHTの定義のため
#include "../../include/midi_utils.h" // NoteTimeEventの定義のため
#include "../../include/utils/color.h" // チャンネルカラー用
#include "../../include/utils/config_manager.h" // 色設定の管理用
#include "../../include/utils/texture_manager.h" // テクスチャ管理用
#include "../../include/skia/include/effects/SkGradientShader.h" // グラデーションシェーダー用
#include <skia/include/core/SkPath.h> // パス描画用

#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h> // rand()用
#include <time.h>   // time()用

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// ピアノキーボードの表示/非表示フラグ（初期状態では表示）
bool showPianoKeyboard = true;

// フレーム時間（秒）
static float deltaTime = 1.0f / 60.0f; // デフォルト値（60FPS）
static float lastUpdateTime = 0.0f;    // 前回の更新時間

// ノートの状態を追跡する構造体
typedef struct {
    uint8_t velocity;       // 現在のベロシティ（0の場合はオフ）
    bool isActive;          // ノートがアクティブかどうか
} NoteState;

// チャンネルごとのノート状態を追跡する配列（16チャンネル × 128ノート）
static NoteState channelNoteStates[16][128] = {0};

// 表示用の統合ノート状態（すべてのチャンネルの状態を統合）
static struct {
    uint8_t velocity;       // 表示用のベロシティ
    uint8_t channel;        // 表示用のチャンネル（色の決定に使用）
    uint8_t track;          // 表示用のトラック（色の決定に使用）
    bool isActive;          // ノートがアクティブかどうか
} displayNoteStates[128] = {0};

// 黒鍵の位置を判定する関数（0-11の中で黒鍵は1, 3, 6, 8, 10）
static bool isBlackKey(int noteNumber) {
    int position = noteNumber % 12;
    return (position == 1 || position == 3 || position == 6 || position == 8 || position == 10);
}

// 白鍵の位置を計算する関数
static int getWhiteKeyIndex(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;

    // 各オクターブの白鍵の数は7つ
    int whiteKeyIndex = octave * 7;

    // 白鍵のインデックスを計算
    if (position == 0) whiteKeyIndex += 0; // C
    else if (position == 2) whiteKeyIndex += 1; // D
    else if (position == 4) whiteKeyIndex += 2; // E
    else if (position == 5) whiteKeyIndex += 3; // F
    else if (position == 7) whiteKeyIndex += 4; // G
    else if (position == 9) whiteKeyIndex += 5; // A
    else if (position == 11) whiteKeyIndex += 6; // B

    return whiteKeyIndex;
}

// 黒鍵の位置を計算する関数（黒鍵の左側の白鍵のインデックスを返す）
static int getBlackKeyPosition(int noteNumber) {
    int octave = noteNumber / 12;
    int position = noteNumber % 12;
    int whiteKeyIndex = octave * 7;

    // 黒鍵の位置を計算（左側の白鍵のインデックスを返す）
    if (position == 1) return whiteKeyIndex + 0; // C# (Cの右)
    else if (position == 3) return whiteKeyIndex + 1; // D# (Dの右)
    else if (position == 6) return whiteKeyIndex + 3; // F# (Fの右)
    else if (position == 8) return whiteKeyIndex + 4; // G# (Gの右)
    else if (position == 10) return whiteKeyIndex + 5; // A# (Aの右)

    // 黒鍵でない場合（エラー）
    return -1;
}

// ピアノキーボードの初期化関数
void initPianoKeyboard(void) {
    // チャンネルごとのノート状態の配列を初期化（16チャンネル × 128ノート）
    memset(channelNoteStates, 0, sizeof(channelNoteStates));

    // 表示用の統合ノート状態を初期化（128ノート）
    memset(displayNoteStates, 0, sizeof(displayNoteStates));
}

// ピアノキーボードの更新関数
void updatePianoKeyboard(float currentTime) {
    // 前回の更新時間を記録
    lastUpdateTime = currentTime;

    // 全てのノート状態をリセット
    for (int note = 0; note < 128; note++) {
        displayNoteStates[note].isActive = false;
        displayNoteStates[note].velocity = 0;
        displayNoteStates[note].channel = 0;
        displayNoteStates[note].track = 0;
    }

    // MIDIファイルとノートイベントが利用可能な場合のみ処理
    if (!appState.midiFile || !appState.noteTimeEvents || appState.noteTimeEventCount == 0) {
        return;
    }

    // 効率的な検索のため、二分探索で検索範囲を絞る
    // 現在時刻より前の適切な開始位置を見つける
    uint32_t startIndex = 0;
    uint32_t endIndex = appState.noteTimeEventCount;

    // 二分探索で開始位置を見つける（現在時刻の少し前から）
    float searchStartTime = currentTime - 10.0f; // 10秒前から検索（長いノートを見逃さないため）
    if (searchStartTime < 0) searchStartTime = 0;

    uint32_t left = 0, right = appState.noteTimeEventCount - 1;
    while (left <= right && left < appState.noteTimeEventCount) {
        uint32_t mid = (left + right) / 2;
        if (appState.noteTimeEvents[mid].time >= searchStartTime) {
            startIndex = mid;
            if (mid == 0) break;
            right = mid - 1;
        } else {
            left = mid + 1;
        }
    }

    // 現在時刻でアクティブなノートを検索
    for (uint32_t i = startIndex; i < appState.noteTimeEventCount; i++) {
        NoteTimeEvent* event = &appState.noteTimeEvents[i];

        // ノートの開始時刻が現在時刻より大幅に後の場合は検索終了
        if (event->time > currentTime + 0.1f) {
            break;
        }

        // 現在の時間がノートの演奏時間内にある場合
        float noteEndTime = event->time + event->duration;
        if (currentTime >= event->time && currentTime < noteEndTime) {
            uint8_t note = event->note;
            uint8_t channel = event->channel;
            uint8_t track = event->track;
            uint8_t velocity = event->velocity;

            // ノート番号の範囲チェック
            if (note >= 128) continue;

            // 表示用のノート状態を更新
            // 同じノートで複数のチャンネルがアクティブな場合は、
            // より高いベロシティまたは後のイベントを優先
            if (!displayNoteStates[note].isActive ||
                velocity >= displayNoteStates[note].velocity) {
                displayNoteStates[note].isActive = true;
                displayNoteStates[note].velocity = velocity;
                displayNoteStates[note].channel = channel;
                displayNoteStates[note].track = track;
            }
        }
    }
}

// ピアノキーボードの描画関数（Skia版）- 最適化版
void drawPianoKeyboard(SkCanvas* canvas) {
    // ConfigManagerからvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const VisualConfig& visualConfig = configManager->getVisualConfig();
    const FunnyConfig& funnyConfig = configManager->getFunnyConfig();

    if (!visualConfig.showPiano || !canvas) return;

    // 画面サイズを取得
    const int screenWidth = WIDTH;
    const int screenHeight = HEIGHT;

    // 白鍵の総数を計算（128鍵のうち白鍵は75鍵）
    const int whiteKeyCount = 75;

    // 白鍵の幅を画面幅に合わせて計算
    const float whiteKeyWidth = (float)screenWidth / whiteKeyCount;
    const float blackKeyWidth = whiteKeyWidth * PIANO_BLACK_KEY_WIDTH_RATIO;

    // キーボードの座標（事前計算）
    const int keyboardX = 0;
    const int keyboardY = screenHeight - PIANO_WHITE_KEY_HEIGHT;

    // サンプリングオプションを事前に作成
    const SkSamplingOptions samplingOptions(SkFilterMode::kLinear);

    // ピアノキーボード上部にフルの横長の四角を描画
	// DrawRectangle(0, offsetY - 6, GetScreenWidth(), 6, (Color){200, 200, 200, 255});
	// DrawRectangle(0, offsetY - 3, GetScreenWidth(), 3, (Color){130, 130, 130, 255});
    SkPaint paint;
    paint.setColor(SkColorSetARGB(255, 200, 200, 200));

    // バグモード時の頂点データ破損効果
    if (funnyConfig.bugMode) {
        static int vertexCorruptionCounter = 0;
        vertexCorruptionCounter++;

        // 頂点座標が範囲外になる効果（NaNや±∞のシミュレーション）
        if (vertexCorruptionCounter % 23 == 0) { // 23フレームに1回の確率
            // 破綻したポリゴンを描画（画面全体に伸びる巨大な四角形）
            float corruptedX = (rand() % 2 == 0) ? -99999.0f : 99999.0f; // 極端に大きな値
            float corruptedY = (rand() % 2 == 0) ? -99999.0f : 99999.0f;
            float corruptedWidth = (rand() % 2 == 0) ? 199999.0f : -199999.0f; // 負の値も含む
            float corruptedHeight = (rand() % 2 == 0) ? 199999.0f : -199999.0f;

            // 異常な色（頂点カラーの補間異常をシミュレート）
            uint8_t corruptedR = rand() % 256;
            uint8_t corruptedG = rand() % 256;
            uint8_t corruptedB = rand() % 256;
            uint8_t corruptedA = 50 + rand() % 100; // 半透明で重ねる

            paint.setColor(SkColorSetARGB(corruptedA, corruptedR, corruptedG, corruptedB));
            canvas->drawRect(SkRect::MakeXYWH(corruptedX, corruptedY, corruptedWidth, corruptedHeight), paint);

            // 画面中央で区切られたような効果（ビューポート変換異常）
            paint.setColor(SkColorSetARGB(80, 255 - corruptedR, 255 - corruptedG, 255 - corruptedB));
            canvas->drawRect(SkRect::MakeXYWH(screenWidth / 2, 0, screenWidth / 2, screenHeight), paint);
            canvas->drawRect(SkRect::MakeXYWH(0, screenHeight / 2, screenWidth, screenHeight / 2), paint);
        }

        // シンプルな頂点データ破壊による美しいグラデーション効果
        if (vertexCorruptionCounter % 31 == 0) { // 31フレームに1回の確率
            // 頂点バッファの破損をシミュレート
            SkPaint corruptedPaint;

            // 破損した頂点座標によるグラデーション
            SkPoint corruptedPoints[2] = {
                {(float)(rand() % screenWidth), (float)(rand() % screenHeight)},
                {(float)(rand() % screenWidth), (float)(rand() % screenHeight)}
            };

            // 画像のような美しい色合い
            SkColor corruptedColors[] = {
                SkColorSetARGB(150, 0, 255, 255),    // シアン
                SkColorSetARGB(150, 100, 150, 255),  // 青紫
                SkColorSetARGB(150, 255, 0, 200),    // マゼンタ
                SkColorSetARGB(150, 255, 150, 0)     // オレンジ
            };

            auto corruptedShader = SkGradientShader::MakeLinear(corruptedPoints, corruptedColors, nullptr, 4, SkTileMode::kClamp);
            corruptedPaint.setShader(corruptedShader);

            // 破損した矩形描画（頂点座標が範囲外）
            float corruptedX = (rand() % (screenWidth * 3)) - screenWidth;
            float corruptedY = (rand() % (screenHeight * 3)) - screenHeight;
            float corruptedW = screenWidth + (rand() % screenWidth);
            float corruptedH = screenHeight + (rand() % screenHeight);

            canvas->drawRect(SkRect::MakeXYWH(corruptedX, corruptedY, corruptedW, corruptedH), corruptedPaint);

            // 中央分割線（画像の特徴）
            SkPaint linePaint;
            linePaint.setColor(SkColorSetARGB(80, 255, 255, 255));
            linePaint.setStrokeWidth(2.0f);
            canvas->drawLine(screenWidth / 2.0f, 0, screenWidth / 2.0f, screenHeight, linePaint);
        }

        // 深度テスト異常効果
        if (vertexCorruptionCounter % 37 == 0) { // 37フレームに1回の確率
            // ランダムな位置に異常な形状を描画
            for (int i = 0; i < 5; i++) {
                float x = (rand() % (screenWidth * 2)) - screenWidth / 2; // 画面外も含む
                float y = (rand() % (screenHeight * 2)) - screenHeight / 2;
                float w = (rand() % 200) + 50;
                float h = (rand() % 200) + 50;

                paint.setColor(SkColorSetARGB(150, rand() % 256, rand() % 256, rand() % 256));
                canvas->drawRect(SkRect::MakeXYWH(x, y, w, h), paint);
            }
        }
    }

    paint.setColor(SkColorSetARGB(255, 200, 200, 200));
    canvas->drawRect(SkRect::MakeXYWH(0, keyboardY - 6, screenWidth, 6), paint);
    paint.setColor(SkColorSetARGB(255, 130, 130, 130));
    canvas->drawRect(SkRect::MakeXYWH(0, keyboardY - 3, screenWidth, 3), paint);

    // 白鍵を描画（最適化版：バッチ処理）
    // テクスチャを一度取得して再利用
    sk_sp<SkImage> whiteKeyTexture = getTexture(TEXTURE_KEY_WHITE);
    if (whiteKeyTexture) {
        // まず非アクティブな白鍵をまとめて描画
        for (int note = PIANO_FIRST_NOTE; note < PIANO_FIRST_NOTE + PIANO_KEY_COUNT; note++) {
            if (!isBlackKey(note) && !displayNoteStates[note].isActive) {
                int whiteKeyIndex = getWhiteKeyIndex(note);
                float keyX = keyboardX + whiteKeyIndex * whiteKeyWidth;
                SkRect destRect = SkRect::MakeXYWH(keyX, keyboardY, whiteKeyWidth, PIANO_WHITE_KEY_HEIGHT);
                canvas->drawImageRect(whiteKeyTexture, destRect, samplingOptions);
            }
        }
    }

    // 次にアクティブな白鍵を描画
    for (int note = PIANO_FIRST_NOTE; note < PIANO_FIRST_NOTE + PIANO_KEY_COUNT; note++) {
        if (!isBlackKey(note) && displayNoteStates[note].isActive) {
            int whiteKeyIndex = getWhiteKeyIndex(note);
            float keyX = keyboardX + whiteKeyIndex * whiteKeyWidth;

            // 色を取得（色モードに基づいて適切な色を選択）
            uint8_t channel = displayNoteStates[note].channel;
            uint8_t track = displayNoteStates[note].track;
            unsigned int color = ConfigManager::getInstance()->getPianoColor(channel, track);
            float r = ((color >> 16) & 0xFF) / 255.0f;
            float g = ((color >> 8) & 0xFF) / 255.0f;
            float b = (color & 0xFF) / 255.0f;

            // バグモード時の面白い効果
            float finalKeyX = keyX;
            float finalKeyY = keyboardY;
            float finalWidth = whiteKeyWidth;
            float finalHeight = PIANO_WHITE_KEY_HEIGHT;

            if (funnyConfig.bugMode) {
                static int keyGlitchCounter = 0;
                keyGlitchCounter++;

                // 頂点バッファ破損効果（白鍵用）
                if (keyGlitchCounter % 5 == 0) { // 5フレームに1回の確率
                    // 頂点座標が範囲外（NaNや±∞のシミュレーション）
                    if (rand() % 4 == 0) {
                        finalKeyX = (rand() % 2 == 0) ? -99999.0f : 99999.0f;
                        finalKeyY = (rand() % 2 == 0) ? -99999.0f : 99999.0f;
                        finalWidth = (rand() % 2 == 0) ? 199999.0f : -199999.0f;
                        finalHeight = (rand() % 2 == 0) ? 199999.0f : -199999.0f;
                    } else {
                        // 通常のグリッチ効果
                        finalKeyX += (rand() % 101) - 50; // -50 to +50 pixels
                        finalKeyY += (rand() % 101) - 50; // -50 to +50 pixels
                        finalWidth += (rand() % 201) - 100; // -100 to +100 pixels
                        finalHeight += (rand() % 201) - 100; // -100 to +100 pixels
                    }

                    // 最小サイズを保証（正常値の場合のみ）
                    if (abs(finalWidth) < 99999 && finalWidth < 5) finalWidth = 5;
                    if (abs(finalHeight) < 99999 && finalHeight < 5) finalHeight = 5;

                    // 頂点カラーの補間異常
                    r = (rand() % 256) / 255.0f;
                    g = (rand() % 256) / 255.0f;
                    b = (rand() % 256) / 255.0f;
                }

                // シェーダー問題（巨大なポリゴン）
                if (keyGlitchCounter % 9 == 0) { // 9フレームに1回の確率
                    // 画面全体に伸びる破綻したキー
                    float extremeX = keyX + (rand() % 8000) - 4000;
                    float extremeY = keyboardY + (rand() % 8000) - 4000;
                    float extremeW = whiteKeyWidth + (rand() % 16000) - 8000;
                    float extremeH = PIANO_WHITE_KEY_HEIGHT + (rand() % 16000) - 8000;

                    drawTextureWithColor(canvas, TEXTURE_KEY_WHITE_PRESSED, extremeX, extremeY,
                                        extremeW, extremeH, r, g, b, 0.3f);
                }

                // 投影行列破損効果
                if (keyGlitchCounter % 11 == 0) { // 11フレームに1回の確率
                    // カメラの向きが異常
                    canvas->save();

                    // 異常な変換を適用
                    canvas->translate(keyX + whiteKeyWidth / 2, keyboardY + PIANO_WHITE_KEY_HEIGHT / 2);
                    canvas->rotate((rand() % 360) * M_PI / 180.0f);
                    canvas->scale((rand() % 10 + 1) * (rand() % 2 == 0 ? 1 : -1),
                                 (rand() % 10 + 1) * (rand() % 2 == 0 ? 1 : -1));

                    drawTextureWithColor(canvas, TEXTURE_KEY_WHITE_PRESSED,
                                        -whiteKeyWidth / 2, -PIANO_WHITE_KEY_HEIGHT / 2,
                                        whiteKeyWidth, PIANO_WHITE_KEY_HEIGHT,
                                        1.0f - r, 1.0f - g, 1.0f - b, 0.6f);

                    canvas->restore();
                }

                // ダブルバッファ破損効果（複数のゴーストキー）
                if (keyGlitchCounter % 13 == 0) { // 13フレームに1回の確率
                    for (int i = 0; i < 5; i++) {
                        float ghostX = keyX + (rand() % 601) - 300; // -300 to +300 pixels
                        float ghostY = keyboardY + (rand() % 601) - 300; // -300 to +300 pixels

                        drawTextureWithColor(canvas, TEXTURE_KEY_WHITE_PRESSED, ghostX, ghostY,
                                            whiteKeyWidth, PIANO_WHITE_KEY_HEIGHT,
                                            1.0f - r, 1.0f - g, 1.0f - b, 0.4f);
                    }
                }
            }

            // 色付きでテクスチャを描画
            drawTextureWithColor(canvas, TEXTURE_KEY_WHITE_PRESSED, finalKeyX, finalKeyY,
                                finalWidth, finalHeight, r, g, b, 1.0f);
        }
    }

    // 黒鍵を描画（最適化版：バッチ処理）
    // テクスチャを一度取得して再利用
    sk_sp<SkImage> blackKeyTexture = getTexture(TEXTURE_KEY_BLACK);
    if (blackKeyTexture) {
        // まず非アクティブな黒鍵をまとめて描画
        for (int note = PIANO_FIRST_NOTE; note < PIANO_FIRST_NOTE + PIANO_KEY_COUNT; note++) {
            if (isBlackKey(note) && !displayNoteStates[note].isActive) {
                int whiteKeyIndex = getBlackKeyPosition(note);
                float keyX = keyboardX + whiteKeyIndex * whiteKeyWidth + whiteKeyWidth - blackKeyWidth / 2;
                // 黒鍵の描画位置を調整（通常時：Y座標を-4、高さはそのまま）
                SkRect destRect = SkRect::MakeXYWH(keyX, keyboardY - 4, blackKeyWidth, PIANO_BLACK_KEY_HEIGHT);
                canvas->drawImageRect(blackKeyTexture, destRect, samplingOptions);
            }
        }
    }

    // 次にアクティブな黒鍵を描画
    for (int note = PIANO_FIRST_NOTE; note < PIANO_FIRST_NOTE + PIANO_KEY_COUNT; note++) {
        if (isBlackKey(note) && displayNoteStates[note].isActive) {
            int whiteKeyIndex = getBlackKeyPosition(note);
            float keyX = keyboardX + whiteKeyIndex * whiteKeyWidth + whiteKeyWidth - blackKeyWidth / 2;

            // 色を取得（色モードに基づいて適切な色を選択）
            uint8_t channel = displayNoteStates[note].channel;
            uint8_t track = displayNoteStates[note].track;
            unsigned int color = ConfigManager::getInstance()->getPianoColor(channel, track);
            float r = ((color >> 16) & 0xFF) / 255.0f;
            float g = ((color >> 8) & 0xFF) / 255.0f;
            float b = (color & 0xFF) / 255.0f;

            // バグモード時の面白い効果（黒鍵用）
            float finalKeyX = keyX;
            float finalKeyY = keyboardY - 2;
            float finalWidth = blackKeyWidth;
            float finalHeight = PIANO_BLACK_KEY_HEIGHT - 2;

            if (funnyConfig.bugMode) {
                static int blackKeyGlitchCounter = 0;
                blackKeyGlitchCounter++;

                // ランダムな確率で黒鍵の位置と色を変更
                if (blackKeyGlitchCounter % 5 == 0) { // 5フレームに1回の確率（白鍵より頻繁）
                    finalKeyX += (rand() % 31) - 15; // -15 to +15 pixels
                    finalKeyY += (rand() % 21) - 10; // -10 to +10 pixels
                    finalWidth += (rand() % 21) - 10; // -10 to +10 pixels
                    finalHeight += (rand() % 21) - 10; // -10 to +10 pixels

                    // 最小サイズを保証
                    if (finalWidth < 3) finalWidth = 3;
                    if (finalHeight < 3) finalHeight = 3;

                    // 色をランダムに変更（黒鍵は明るい色に）
                    r = 0.5f + (rand() % 128) / 255.0f; // 0.5 to 1.0
                    g = 0.5f + (rand() % 128) / 255.0f;
                    b = 0.5f + (rand() % 128) / 255.0f;
                }

                // 時々黒鍵を複製して描画（より頻繁に）
                if (blackKeyGlitchCounter % 8 == 0) { // 8フレームに1回の確率
                    float ghostX = keyX + (rand() % 41) - 20; // -20 to +20 pixels
                    float ghostY = keyboardY - 2 + (rand() % 21) - 10; // -10 to +10 pixels

                    drawTextureWithColor(canvas, TEXTURE_KEY_BLACK_PRESSED, ghostX, ghostY,
                                        blackKeyWidth, PIANO_BLACK_KEY_HEIGHT - 2,
                                        1.0f - r, 1.0f - g, 1.0f - b, 0.7f);
                }
            }

            // 色付きでテクスチャを描画（押下時：Y座標を-2、高さを-2）
            drawTextureWithColor(canvas, TEXTURE_KEY_BLACK_PRESSED, finalKeyX, finalKeyY,
                                finalWidth, finalHeight, r, g, b, 1.0f);
        }
    }
}

// ノートオンイベントのハンドリング関数
// 注意: 新しい実装では直接ノートイベント配列を使用するため、この関数は使用されません
// 互換性のために残しておきますが、実際の処理は updatePianoKeyboard で行われます
void handleNoteOn(uint8_t note, uint8_t velocity, uint8_t channel) {
    // 新しい実装では updatePianoKeyboard で直接処理するため、何もしません
    // この関数は互換性のためにのみ残されています
}

// ノートオフイベントのハンドリング関数
// 注意: 新しい実装では直接ノートイベント配列を使用するため、この関数は使用されません
// 互換性のために残しておきますが、実際の処理は updatePianoKeyboard で行われます
void handleNoteOff(uint8_t note, uint8_t channel) {
    // 新しい実装では updatePianoKeyboard で直接処理するため、何もしません
    // この関数は互換性のためにのみ残されています
}

// ピアノキーボードの表示/非表示を切り替える関数
void togglePianoKeyboardVisibility(void) {
    showPianoKeyboard = !showPianoKeyboard;
}

// ピアノキーボードのクリーンアップ関数
void cleanupPianoKeyboard(void) {
    // チャンネルごとのノート状態の配列をクリア
    memset(channelNoteStates, 0, sizeof(channelNoteStates));

    // 表示用の統合ノート状態をクリア
    memset(displayNoteStates, 0, sizeof(displayNoteStates));
}
