#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <pthread.h>
#include <include/core/SkImage.h>
#include <chrono>
#include <thread>

#include "../include/threading.h"
#include "../include/video_encoder.h"
#include "../include/app_state.h"
#include "../include/performance.h"

// グローバル変数
ThreadData encoderThreadData = {THREAD_STATE_IDLE};
static pthread_t encoderThread;
static pthread_mutex_t encoderMutex;
static pthread_cond_t encoderCondition;

// スレッド待機の最適化用定数
static const int MAX_WAIT_TIME_MS = 10; // 最大待機時間（ミリ秒）

// エンコーダースレッドの実行関数
static void* encoderThreadFunc(void* arg) {
    (void)arg; // 未使用の引数を回避

    printf("Encoder thread started\n");

    while (true) {
        // ミューテックスをロック
        pthread_mutex_lock(&encoderMutex);

        // ジョブが利用可能か停止要求があるかチェック
        if (!encoderThreadData.jobAvailable &&
            encoderThreadData.state != THREAD_STATE_STOPPING) {
            // 短時間だけ条件変数で待機
            struct timespec ts;
            clock_gettime(CLOCK_REALTIME, &ts);
            ts.tv_nsec += MAX_WAIT_TIME_MS * 1000000; // ナノ秒に変換
            if (ts.tv_nsec >= 1000000000) {
                ts.tv_sec += 1;
                ts.tv_nsec -= 1000000000;
            }

            // 条件変数でタイムアウト付き待機
            pthread_cond_timedwait(&encoderCondition, &encoderMutex, &ts);
        }

        // スレッドが停止要求を受けた場合
        if (encoderThreadData.state == THREAD_STATE_STOPPING) {
            // 現在のジョブがあれば解放
            if (encoderThreadData.jobAvailable && encoderThreadData.currentJob.image) {
                encoderThreadData.currentJob.image = nullptr;
            }
            pthread_mutex_unlock(&encoderMutex);
            break;
        }

        // ジョブデータをローカルにコピー
        int frameCounter = encoderThreadData.currentJob.frameCounter;
        float currentTime = encoderThreadData.currentJob.currentTime;
        sk_sp<SkImage> image = encoderThreadData.currentJob.image;

        // ジョブ状態を更新
        encoderThreadData.jobAvailable = false;
        encoderThreadData.jobCompleted = false;
        encoderThreadData.currentJob.image = nullptr;  // 所有権を移動

        // ミューテックスをアンロック
        pthread_mutex_unlock(&encoderMutex);

        // フレームをエンコード（イメージがNULLでないことを確認）
        bool success = false;
        if (image) {
            success = encodeFrame(image, currentTime);

            // 使用済みのイメージを解放（スマートポインタなので自動的に解放される）
            image = nullptr;
        }

        // ミューテックスをロック
        pthread_mutex_lock(&encoderMutex);

        // 進捗情報を更新
        if (success) {
            encoderThreadData.progress = (float)frameCounter / (float)appState.totalFrames;
            snprintf(encoderThreadData.statusMessage, sizeof(encoderThreadData.statusMessage),
                    "Encoded frame %d/%d", frameCounter + 1, appState.totalFrames);
        } else {
            // エラーメッセージをより詳細に
            char errorDetails[128] = "";
            if (videoEncoder.videoCodecContext) {
                snprintf(errorDetails, sizeof(errorDetails),
                        " (codec: %s, pix_fmt: %d, bitrate: %ld)",
                        videoEncoder.videoCodecContext->codec ? videoEncoder.videoCodecContext->codec->name : "unknown",
                        videoEncoder.videoCodecContext->pix_fmt,
                        videoEncoder.videoCodecContext->bit_rate);
            }

            snprintf(encoderThreadData.statusMessage, sizeof(encoderThreadData.statusMessage),
                    "Error encoding frame %d%s", frameCounter, errorDetails);
        }

        // ジョブ完了フラグを設定
        encoderThreadData.jobCompleted = true;

        // ミューテックスをアンロック
        pthread_mutex_unlock(&encoderMutex);

        // メインスレッドに通知
        pthread_cond_signal(&encoderCondition);
    }

    printf("Encoder thread stopped\n");
    encoderThreadData.state = THREAD_STATE_STOPPED;

    return NULL;
}

// スレッド処理の初期化
bool initThreading(void) {
    // ミューテックスと条件変数の初期化
    if (pthread_mutex_init(&encoderMutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize encoder mutex\n");
        return false;
    }

    if (pthread_cond_init(&encoderCondition, NULL) != 0) {
        fprintf(stderr, "Failed to initialize encoder condition\n");
        pthread_mutex_destroy(&encoderMutex);
        return false;
    }

    // スレッドデータの初期化
    memset(&encoderThreadData, 0, sizeof(ThreadData));
    encoderThreadData.state = THREAD_STATE_IDLE;
    encoderThreadData.mutex = &encoderMutex;
    encoderThreadData.condition = &encoderCondition;

    return true;
}

// スレッド処理のクリーンアップ
void cleanupThreading(void) {
    // エンコーダースレッドが実行中なら停止
    if (encoderThreadData.state == THREAD_STATE_RUNNING ||
        encoderThreadData.state == THREAD_STATE_PAUSED) {
        stopEncoderThread();
    }

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // 残っているイメージがあれば解放
    if (encoderThreadData.currentJob.image) {
        encoderThreadData.currentJob.image = nullptr;
    }

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    // ミューテックスと条件変数の破棄
    pthread_mutex_destroy(&encoderMutex);
    pthread_cond_destroy(&encoderCondition);
}

// エンコーダースレッドの開始
bool startEncoderThread(void) {
    // スレッドが既に実行中なら何もしない
    if (encoderThreadData.state == THREAD_STATE_RUNNING) {
        return true;
    }

    // スレッドの状態を設定
    encoderThreadData.state = THREAD_STATE_RUNNING;

    // スレッドを作成
    if (pthread_create(&encoderThread, NULL, encoderThreadFunc, NULL) != 0) {
        fprintf(stderr, "Failed to create encoder thread\n");
        encoderThreadData.state = THREAD_STATE_IDLE;
        return false;
    }

    return true;
}

// エンコーダースレッドの停止
void stopEncoderThread(void) {
    // スレッドが実行中でなければ何もしない
    if (encoderThreadData.state != THREAD_STATE_RUNNING &&
        encoderThreadData.state != THREAD_STATE_PAUSED) {
        return;
    }

    printf("Stopping encoder thread...\n");

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // スレッドの状態を停止中に設定
    encoderThreadData.state = THREAD_STATE_STOPPING;

    // ジョブ状態をリセット（スレッドが待機状態から抜けるため）
    encoderThreadData.jobAvailable = true;
    encoderThreadData.jobCompleted = true;

    // 条件変数に通知
    pthread_cond_signal(&encoderCondition);

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    // スレッドの終了を待機
    printf("Waiting for encoder thread to terminate...\n");
    pthread_join(encoderThread, NULL);
    printf("Encoder thread stopped successfully\n");
}

// エンコーダースレッドの一時停止
void pauseEncoderThread(void) {
    // スレッドが実行中でなければ何もしない
    if (encoderThreadData.state != THREAD_STATE_RUNNING) {
        return;
    }

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // スレッドの状態を一時停止に設定
    encoderThreadData.state = THREAD_STATE_PAUSED;

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);
}

// エンコーダースレッドの再開
void resumeEncoderThread(void) {
    // スレッドが一時停止中でなければ何もしない
    if (encoderThreadData.state != THREAD_STATE_PAUSED) {
        return;
    }

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // スレッドの状態を実行中に設定
    encoderThreadData.state = THREAD_STATE_RUNNING;

    // 条件変数に通知
    pthread_cond_signal(&encoderCondition);

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);
}

// レンダリングジョブの送信
bool submitRenderJob(int frameCounter, float currentTime,
                    uint32_t currentNotes, uint32_t totalNotes,
                    uint32_t currentNps, uint32_t maxNps,
                    uint16_t currentPolyphony, uint16_t maxPolyphony,
                    sk_sp<SkImage> image) {
    // 引数チェック
    if (!image) {
        fprintf(stderr, "Error: Null image passed to submitRenderJob\n");
        return false;
    }

    // スレッドが実行中でなければ失敗
    if (encoderThreadData.state != THREAD_STATE_RUNNING) {
        return false;
    }

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // 前のジョブが完了していない場合は短時間待機
    if (!encoderThreadData.jobCompleted && encoderThreadData.jobAvailable) {
        // 短時間だけ条件変数で待機
        struct timespec ts;
        clock_gettime(CLOCK_REALTIME, &ts);
        ts.tv_nsec += MAX_WAIT_TIME_MS * 1000000; // ナノ秒に変換
        if (ts.tv_nsec >= 1000000000) {
            ts.tv_sec += 1;
            ts.tv_nsec -= 1000000000;
        }

        // 条件変数でタイムアウト付き待機
        int wait_result = pthread_cond_timedwait(&encoderCondition, &encoderMutex, &ts);

        // タイムアウトした場合でも、ジョブが完了していなければ一旦ロックを解放して再試行
        if (wait_result == ETIMEDOUT && !encoderThreadData.jobCompleted && encoderThreadData.jobAvailable) {
            pthread_mutex_unlock(&encoderMutex);
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            return submitRenderJob(frameCounter, currentTime, currentNotes, totalNotes,
                                  currentNps, maxNps, currentPolyphony, maxPolyphony, image);
        }
    }

    // 前のジョブのイメージが残っていれば解放
    if (encoderThreadData.currentJob.image) {
        encoderThreadData.currentJob.image = nullptr;
    }

    // ジョブデータを設定
    encoderThreadData.currentJob.frameCounter = frameCounter;
    encoderThreadData.currentJob.currentTime = currentTime;
    encoderThreadData.currentJob.currentNotes = currentNotes;
    encoderThreadData.currentJob.totalNotes = totalNotes;
    encoderThreadData.currentJob.currentNps = currentNps;
    encoderThreadData.currentJob.maxNps = maxNps;
    encoderThreadData.currentJob.currentPolyphony = currentPolyphony;
    encoderThreadData.currentJob.maxPolyphony = maxPolyphony;

    // イメージを共有（スマートポインタなので参照カウントが自動的に増加）
    encoderThreadData.currentJob.image = image;

    // ジョブ利用可能フラグを設定
    encoderThreadData.jobAvailable = true;

    // 条件変数に通知
    pthread_cond_signal(&encoderCondition);

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    return true;
}

// エンコーダースレッドがビジー状態かどうかを確認
bool isEncoderThreadBusy(void) {
    bool busy;

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // ジョブが利用可能かつ完了していない場合はビジー
    busy = encoderThreadData.jobAvailable && !encoderThreadData.jobCompleted;

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    return busy;
}

// エンコーダースレッドの進捗を取得
float getEncoderThreadProgress(void) {
    float progress;

    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // 進捗を取得
    progress = encoderThreadData.progress;

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    return progress;
}

// エンコーダースレッドのステータスを取得
const char* getEncoderThreadStatus(void) {
    // ミューテックスをロック
    pthread_mutex_lock(&encoderMutex);

    // ステータスメッセージのコピーを作成
    static char statusCopy[256];
    strcpy(statusCopy, encoderThreadData.statusMessage);

    // ミューテックスをアンロック
    pthread_mutex_unlock(&encoderMutex);

    return statusCopy;
}
