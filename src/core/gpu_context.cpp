#include "../../include/gpu_context.h"
#include <stdio.h>
#include <string.h>
#include <include/gpu/GrBackendSurface.h>
#include <include/gpu/gl/GrGLAssembleInterface.h>

// グローバルGPUコンテキスト
GPUContext gpuContext = {0};

// OpenGL エラーをチェックする関数
bool checkGLError(const char* operation) {
#ifdef GPU_RENDERING_ENABLED
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        fprintf(stderr, "OpenGL error in %s: 0x%x\n", operation, error);
        return false;
    }
#endif
    return true;
}

#ifdef WINDOWS_PLATFORM
// Windows用のOpenGLコンテキスト初期化
static bool initWindowsGL(int width, int height) {
    // ダミーウィンドウを作成（ヘッドレスモード用）
    WNDCLASSA wc = {0};
    wc.lpfnWndProc = DefWindowProcA;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = "SkiaGPUDummy";
    RegisterClassA(&wc);

    gpuContext.hwnd = CreateWindowA("SkiaGPUDummy", "Skia GPU Context",
                                    WS_OVERLAPPEDWINDOW, 0, 0, width, height,
                                    NULL, NULL, GetModuleHandle(NULL), NULL);

    if (!gpuContext.hwnd) {
        fprintf(stderr, "Failed to create dummy window\n");
        return false;
    }

    gpuContext.hdc = GetDC(gpuContext.hwnd);
    if (!gpuContext.hdc) {
        fprintf(stderr, "Failed to get device context\n");
        return false;
    }

    // ピクセルフォーマットを設定
    memset(&gpuContext.pfd, 0, sizeof(PIXELFORMATDESCRIPTOR));
    gpuContext.pfd.nSize = sizeof(PIXELFORMATDESCRIPTOR);
    gpuContext.pfd.nVersion = 1;
    gpuContext.pfd.dwFlags = PFD_DRAW_TO_WINDOW | PFD_SUPPORT_OPENGL | PFD_DOUBLEBUFFER;
    gpuContext.pfd.iPixelType = PFD_TYPE_RGBA;
    gpuContext.pfd.cColorBits = 32;
    gpuContext.pfd.cDepthBits = 24;
    gpuContext.pfd.cStencilBits = 8;

    int pixelFormat = ChoosePixelFormat(gpuContext.hdc, &gpuContext.pfd);
    if (!pixelFormat) {
        fprintf(stderr, "Failed to choose pixel format\n");
        return false;
    }

    if (!SetPixelFormat(gpuContext.hdc, pixelFormat, &gpuContext.pfd)) {
        fprintf(stderr, "Failed to set pixel format\n");
        return false;
    }

    // OpenGLコンテキストを作成
    gpuContext.hglrc = wglCreateContext(gpuContext.hdc);
    if (!gpuContext.hglrc) {
        fprintf(stderr, "Failed to create OpenGL context\n");
        return false;
    }

    if (!wglMakeCurrent(gpuContext.hdc, gpuContext.hglrc)) {
        fprintf(stderr, "Failed to make OpenGL context current\n");
        return false;
    }

    return true;
}
#endif

#ifdef LINUX_PLATFORM
// Linux用のEGLコンテキスト初期化
static bool initLinuxEGL(int width, int height) {
    // X11ディスプレイを開く（ヘッドレスモード用）
    gpuContext.display = XOpenDisplay(NULL);
    if (!gpuContext.display) {
        fprintf(stderr, "Failed to open X11 display\n");
        return false;
    }

    // EGLディスプレイを取得
    gpuContext.eglDisplay = eglGetDisplay((EGLNativeDisplayType)gpuContext.display);
    if (gpuContext.eglDisplay == EGL_NO_DISPLAY) {
        fprintf(stderr, "Failed to get EGL display\n");
        return false;
    }

    // EGLを初期化
    EGLint major, minor;
    if (!eglInitialize(gpuContext.eglDisplay, &major, &minor)) {
        fprintf(stderr, "Failed to initialize EGL\n");
        return false;
    }

    // EGL設定を選択
    EGLint configAttribs[] = {
        EGL_SURFACE_TYPE, EGL_PBUFFER_BIT,
        EGL_BLUE_SIZE, 8,
        EGL_GREEN_SIZE, 8,
        EGL_RED_SIZE, 8,
        EGL_ALPHA_SIZE, 8,
        EGL_DEPTH_SIZE, 24,
        EGL_STENCIL_SIZE, 8,
        EGL_RENDERABLE_TYPE, EGL_OPENGL_BIT,
        EGL_NONE
    };

    EGLint numConfigs;
    if (!eglChooseConfig(gpuContext.eglDisplay, configAttribs, &gpuContext.eglConfig, 1, &numConfigs)) {
        fprintf(stderr, "Failed to choose EGL config\n");
        return false;
    }

    // OpenGL APIを使用
    eglBindAPI(EGL_OPENGL_API);

    // EGLコンテキストを作成
    EGLint contextAttribs[] = {
        EGL_CONTEXT_MAJOR_VERSION, 3,
        EGL_CONTEXT_MINOR_VERSION, 3,
        EGL_NONE
    };

    gpuContext.eglContext = eglCreateContext(gpuContext.eglDisplay, gpuContext.eglConfig, EGL_NO_CONTEXT, contextAttribs);
    if (gpuContext.eglContext == EGL_NO_CONTEXT) {
        fprintf(stderr, "Failed to create EGL context\n");
        return false;
    }

    // PBufferサーフェスを作成
    EGLint pbufferAttribs[] = {
        EGL_WIDTH, width,
        EGL_HEIGHT, height,
        EGL_NONE
    };

    gpuContext.eglSurface = eglCreatePbufferSurface(gpuContext.eglDisplay, gpuContext.eglConfig, pbufferAttribs);
    if (gpuContext.eglSurface == EGL_NO_SURFACE) {
        fprintf(stderr, "Failed to create EGL surface\n");
        return false;
    }

    // コンテキストを現在のものにする
    if (!eglMakeCurrent(gpuContext.eglDisplay, gpuContext.eglSurface, gpuContext.eglSurface, gpuContext.eglContext)) {
        fprintf(stderr, "Failed to make EGL context current\n");
        return false;
    }

    return true;
}
#endif

// GPU コンテキストを初期化する関数
bool initGPUContext(int width, int height) {
    if (gpuContext.initialized) {
        return true;
    }

    printf("Initializing GPU context for Skia rendering (%dx%d)...\n", width, height);

    // ミューテックスを初期化
    if (pthread_mutex_init(&gpuContext.mutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize GPU context mutex\n");
        return false;
    }

    gpuContext.width = width;
    gpuContext.height = height;

    // プラットフォーム固有の初期化
#ifdef WINDOWS_PLATFORM
    if (!initWindowsGL(width, height)) {
        fprintf(stderr, "Failed to initialize Windows OpenGL context\n");
        return false;
    }
#elif defined(LINUX_PLATFORM)
    if (!initLinuxEGL(width, height)) {
        fprintf(stderr, "Failed to initialize Linux EGL context\n");
        return false;
    }
#else
    fprintf(stderr, "GPU rendering not supported on this platform\n");
    return false;
#endif

    // OpenGLインターフェースを作成
    gpuContext.glInterface = GrGLMakeNativeInterface();
    if (!gpuContext.glInterface) {
        fprintf(stderr, "Failed to create OpenGL interface\n");
        return false;
    }

    // Skia GPU コンテキストを作成
    gpuContext.grContext = GrDirectContext::MakeGL(gpuContext.glInterface);
    if (!gpuContext.grContext) {
        fprintf(stderr, "Failed to create Skia GPU context\n");
        return false;
    }

    // OpenGLエラーをチェック
    if (!checkGLError("GPU context initialization")) {
        return false;
    }

    gpuContext.initialized = true;
    printf("✓ GPU context initialized successfully\n");
    printf("  OpenGL Vendor: %s\n", glGetString(GL_VENDOR));
    printf("  OpenGL Renderer: %s\n", glGetString(GL_RENDERER));
    printf("  OpenGL Version: %s\n", glGetString(GL_VERSION));

    return true;
}

// GPU サーフェスを作成する関数
sk_sp<SkSurface> createGPUSurface(int width, int height) {
    // スレッド安全性のためにミューテックスをロック
    pthread_mutex_lock(&gpuContext.mutex);

    if (!gpuContext.initialized || !gpuContext.grContext) {
        fprintf(stderr, "GPU context not initialized\n");
        pthread_mutex_unlock(&gpuContext.mutex);
        return nullptr;
    }

    // GPU レンダーターゲットを作成
    SkImageInfo info = SkImageInfo::MakeN32Premul(width, height);
    sk_sp<SkSurface> surface = SkSurface::MakeRenderTarget(gpuContext.grContext.get(), SkBudgeted::kNo, info);

    if (!surface) {
        fprintf(stderr, "Failed to create GPU surface (%dx%d)\n", width, height);
        pthread_mutex_unlock(&gpuContext.mutex);
        return nullptr;
    }

    pthread_mutex_unlock(&gpuContext.mutex);
    return surface;
}

// GPU コンテキストをフラッシュする関数
void flushGPUContext() {
    pthread_mutex_lock(&gpuContext.mutex);
    if (gpuContext.initialized && gpuContext.grContext) {
        gpuContext.grContext->flushAndSubmit();
    }
    pthread_mutex_unlock(&gpuContext.mutex);
}

// GPU が利用可能かチェックする関数
bool isGPUAvailable() {
    return gpuContext.initialized && gpuContext.grContext != nullptr;
}

// GPU コンテキストを解放する関数
void cleanupGPUContext() {
    if (!gpuContext.initialized) {
        return;
    }

    printf("Cleaning up GPU context...\n");

    // Skia GPU コンテキストを解放
    if (gpuContext.grContext) {
        gpuContext.grContext->abandonContext();
        gpuContext.grContext = nullptr;
    }

    gpuContext.glInterface = nullptr;

    // ミューテックスを破棄
    pthread_mutex_destroy(&gpuContext.mutex);

    // プラットフォーム固有のクリーンアップ
#ifdef WINDOWS_PLATFORM
    if (gpuContext.hglrc) {
        wglMakeCurrent(NULL, NULL);
        wglDeleteContext(gpuContext.hglrc);
        gpuContext.hglrc = NULL;
    }
    if (gpuContext.hdc) {
        ReleaseDC(gpuContext.hwnd, gpuContext.hdc);
        gpuContext.hdc = NULL;
    }
    if (gpuContext.hwnd) {
        DestroyWindow(gpuContext.hwnd);
        gpuContext.hwnd = NULL;
    }
#elif defined(LINUX_PLATFORM)
    if (gpuContext.eglContext != EGL_NO_CONTEXT) {
        eglMakeCurrent(gpuContext.eglDisplay, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        eglDestroyContext(gpuContext.eglDisplay, gpuContext.eglContext);
        gpuContext.eglContext = EGL_NO_CONTEXT;
    }
    if (gpuContext.eglSurface != EGL_NO_SURFACE) {
        eglDestroySurface(gpuContext.eglDisplay, gpuContext.eglSurface);
        gpuContext.eglSurface = EGL_NO_SURFACE;
    }
    if (gpuContext.eglDisplay != EGL_NO_DISPLAY) {
        eglTerminate(gpuContext.eglDisplay);
        gpuContext.eglDisplay = EGL_NO_DISPLAY;
    }
    if (gpuContext.display) {
        XCloseDisplay(gpuContext.display);
        gpuContext.display = NULL;
    }
#endif

    gpuContext.initialized = false;
    printf("✓ GPU context cleaned up\n");
}
