#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../../include/app_state.h"

// グローバルアプリケーション状態
AppState appState = {0};

// アプリケーション状態を初期化する関数
void initAppState() {
    memset(&appState, 0, sizeof(AppState));
    appState.initialized = true;
    appState.previewMode = false;  // デフォルトではプレビューモードは無効
}

// アプリケーション状態をクリーンアップする関数
void cleanupAppState() {
    if (appState.midiFile) {
        freeMidiFile(appState.midiFile);
        appState.midiFile = NULL;
    }

    if (appState.noteTimeEvents) {
        free(appState.noteTimeEvents);
        appState.noteTimeEvents = NULL;
        appState.noteTimeEventCount = 0;
        appState.noteTimeEventCapacity = 0;
    }

    appState.initialized = false;
}

// 最大値を更新する関数
void updateMaxValues(uint32_t currentNps, uint16_t currentPolyphony) {
    if (currentNps > appState.maxNps) {
        appState.maxNps = currentNps;
    }

    if (currentPolyphony > appState.maxPolyphony) {
        appState.maxPolyphony = currentPolyphony;
    }
}
