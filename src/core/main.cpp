#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <time.h>
#ifdef _WIN32
#include <conio.h>
#include <windows.h>
#else
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#endif
#include "../../include/app_state.h"
#include "../../include/file_dialog.h"
#include "../../include/font_manager.h"
#include "../../include/midi_utils.h"
#include "../../include/note_processor.h"
#include "../../include/video_encoder.h"
#include "../../include/frame_renderer.h"
#include "../../include/performance.h"
#include "../../include/string_utils.h"
#include "../../include/graphs.h"
#include "../../include/threading.h"
#include "../../include/utils/texture_manager.h"
#include "../../include/utils/color.h"
#include "../../include/utils/config_manager.h"
#include "../../include/gpu_context.h"
#include "../../include/gpu_batch_renderer.h"
#include "../../include/utils/reload_config.h"

// キーボード入力をチェックする関数
static bool checkForQuitKey() {
#ifdef _WIN32
    // Windowsの場合
    if (_kbhit()) {
        int key = _getch();
        if (key == 'q' || key == 'Q') {
            return true;
        }
        if (key == 0 || key == 224) {  // 特殊キーの場合
            key = _getch();  // 実際のキーコードを取得
            if (key == 63) {  // F5キー (拡張キーの場合)
                reloadConfiguration();  // 設定を再読み込み
            }
        } else if (key == 116) {  // 通常の場合のF5キー
            reloadConfiguration();  // 設定を再読み込み
        }
    }
#else
    // Linux/Unixの場合
    struct termios oldt, newt;
    int ch;
    int oldf;

    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);

    ch = getchar();

    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    fcntl(STDIN_FILENO, F_SETFL, oldf);

    if (ch == 'q' || ch == 'Q') {
        return true;
    } else if (ch == 0x15) {  // Linux/UnixでのF5キーコード（環境により異なる場合があります）
        reloadConfiguration();  // 設定を再読み込み
    }
#endif
    return false;
}

// ヘッドレスモード用のダミー関数
static inline bool isImGuiWindowClosed(void) { return false; }
static inline void setImGuiUpdateInterval(int intervalMs) { (void)intervalMs; }
static inline void updateRenderingInfo(float currentTime, uint32_t currentNotes, uint32_t totalNotes,
                                      uint32_t currentNps, uint32_t maxNps,
                                      uint16_t currentPolyphony, uint16_t maxPolyphony,
                                      void* image, int width, int height) {
    (void)currentTime; (void)currentNotes; (void)totalNotes;
    (void)currentNps; (void)maxNps; (void)currentPolyphony; (void)maxPolyphony;
    (void)image; (void)width; (void)height;
}

int main(int argc, char *argv[]) {
    // アプリケーション状態を初期化
    initAppState();
    
    // 色設定を初期化（実行ファイルと同じディレクトリの設定ファイルを読み込み）
    initColors();
    
    // JSONスキーマも生成
    ConfigManager::getInstance()->generateJsonSchema();
    
    // コマンドライン引数をチェック
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--preview") == 0 || strcmp(argv[i], "-p") == 0) {
            // プレビューモードを有効化
            appState.previewMode = true;
            printf("Preview mode enabled. UI will be displayed without rendering.\n");
        }
    }

    // プレビューモードでない場合のみファイル選択を行う
    if (!appState.previewMode) {
        // ファイルダイアログを表示してMIDIファイルを選択
        char *midiFilePath = openMidiFileDialog();
        if (!midiFilePath || strlen(midiFilePath) == 0) {
            fprintf(stderr, "No MIDI file selected.\n");
            return -1;
        }

        strcpy(appState.midiFilePath, midiFilePath);
        printf("Selected MIDI file: %s\n", appState.midiFilePath);

        // オーディオファイルを選択（オプション）
        appState.hasAudio = false;

        char *selectedAudioPath = openAudioFileDialog();
        if (selectedAudioPath && strlen(selectedAudioPath) > 0) {
            strcpy(appState.audioFilePath, selectedAudioPath);
            appState.hasAudio = true;
            printf("Selected audio file: %s\n", appState.audioFilePath);
        } else {
            printf("No audio file selected, proceeding with silent video\n");
        }

        // MIDIファイルをロード
        appState.midiFile = loadMidiFile(appState.midiFilePath);
        if (!appState.midiFile) {
            fprintf(stderr, "Failed to load MIDI file: %s\n", appState.midiFilePath);
            cleanupAppState();
            return -1;
        }

        // MIDIファイルの情報を表示
        printf("MIDI File Information:\n");
        printf("  Total Notes: %u\n", appState.midiFile->totalNotes);
        printf("  Duration: %.2f seconds\n", appState.midiFile->durationSeconds);

        // ノートイベントを収集
        printf("Collecting note events...\n");
        if (!collectNoteEvents(appState.midiFile, &appState.noteTimeEvents, &appState.noteTimeEventCount)) {
            fprintf(stderr, "Failed to collect note events\n");
            cleanupAppState();
            return -1;
        }

        // ノート時間イベントを時間順にソート
        sortNoteEvents(appState.noteTimeEvents, appState.noteTimeEventCount);

        // 総ノート数を確認
        printf("Total notes in MIDI file (initial estimate): %u\n", appState.midiFile->totalNotes);
        printf("Collected note time events: %u\n", appState.noteTimeEventCount);

        // MIDIファイルの総ノート数とノート時間イベント数が一致しない場合は警告
        if (appState.midiFile->totalNotes != appState.noteTimeEventCount) {
            printf("Note: Initial total notes estimate (%u) differs from collected note events (%u).\n",
                appState.midiFile->totalNotes, appState.noteTimeEventCount);
        }
        // 実際に収集したノート数で totalNotes を更新
        appState.midiFile->totalNotes = appState.noteTimeEventCount;

        // MIDIファイルの長さに基づいて動画の長さを設定
        if (appState.noteTimeEventCount > 0) {
            // 最後のノートイベントの終了時刻を計算
            float lastEventEndTime = 0.0f;
            for (uint32_t i = 0; i < appState.noteTimeEventCount; ++i) {
                float eventEndTime = appState.noteTimeEvents[i].time + appState.noteTimeEvents[i].duration;
                if (eventEndTime > lastEventEndTime) {
                    lastEventEndTime = eventEndTime;
                }
            }
            // 最後のノートの後に余裕を持たせる（4秒）
            appState.videoDuration = lastEventEndTime + 4.0f;

            // MIDIファイルヘッダのdurationSecondsと比較して大きい方を取る
            if (appState.midiFile->durationSeconds + 4.0f > appState.videoDuration) {
                appState.videoDuration = appState.midiFile->durationSeconds + 4.0f;
            }
        } else if (appState.midiFile->durationSeconds > 0) {
            appState.videoDuration = appState.midiFile->durationSeconds + 4.0f;
        } else {
            appState.videoDuration = (float)DURATION; // デフォルトの長さ
        }

        printf("Video duration will be: %.2f seconds\n", appState.videoDuration);
        appState.totalFrames = (int)(FPS * appState.videoDuration);
        if (appState.totalFrames <= 0) {
            fprintf(stderr, "Error: Calculated totalFrames is zero or negative. videoDuration: %.2f, FPS: %d\n", appState.videoDuration, FPS);
            cleanupAppState();
            return -1;
        }

        // デフォルトの出力ファイル名を生成（MIDIファイル名に基づく）
        char defaultOutputFileName[MAX_PATH_LENGTH];
        char baseFileName[MAX_PATH_LENGTH];
        getBaseName(appState.midiFilePath, baseFileName, MAX_PATH_LENGTH);
        snprintf(defaultOutputFileName, MAX_PATH_LENGTH, "%s_stats.mp4", baseFileName);

        printf("Default output filename: %s\n", defaultOutputFileName);

        // 出力ファイルの保存先を選択
        printf("Select output file location...\n");
        char *outputFilePath = saveOutputFileDialog(defaultOutputFileName);
        if (!outputFilePath || strlen(outputFilePath) == 0) {
            fprintf(stderr, "No output file selected.\n");
            cleanupAppState();
            return -1;
        }

        strcpy(appState.outputFilePath, outputFilePath);
        printf("Output file: %s\n", appState.outputFilePath);

        // ビデオエンコーダーを初期化
        if (!initVideoEncoder(appState.outputFilePath, appState.audioFilePath, appState.hasAudio, appState.videoDuration)) {
            fprintf(stderr, "Failed to initialize video encoder\n");
            cleanupAppState();
            return -1;
        }
    } else {
        // プレビューモードの場合はダミーの値を設定
        printf("Preview mode: Skipping file selection and MIDI processing\n");
        appState.videoDuration = 60.0f;  // 1分のダミー動画長
        appState.totalFrames = (int)(FPS * appState.videoDuration);
    }

    // グラフを初期化
    initGraphs(WIDTH, HEIGHT);

    // フォントコンテキストを初期化
    if (!initFontContext()) {
        fprintf(stderr, "Warning: Failed to initialize font context. Using default fonts.\n");
    }

    // GPUコンテキストを初期化
#ifdef GPU_RENDERING_ENABLED
    if (!initGPUContext(WIDTH, HEIGHT)) {
        fprintf(stderr, "Warning: Failed to initialize GPU context. Falling back to CPU rendering.\n");
    } else {
        printf("✓ GPU rendering enabled\n");

        // GPUバッチレンダラーを初期化
        if (!initBatchRenderer()) {
            fprintf(stderr, "Warning: Failed to initialize GPU batch renderer.\n");
        } else {
            printf("✓ GPU batch rendering enabled\n");
        }
    }
#else
    printf("GPU rendering disabled (compiled without GPU support)\n");
#endif

    // ヘッドレスモードのため、ImGuiは初期化しないが、テクスチャマネージャーは必要
    printf("Running in headless mode - no GUI will be displayed\n");

    // テクスチャマネージャーを初期化（レンダリングに必要）
    if (!initTextureManager()) {
        fprintf(stderr, "Failed to initialize texture manager\n");
        cleanupAppState();
        return -1;
    }

    // スレッド処理を初期化
    if (!initThreading()) {
        fprintf(stderr, "Failed to initialize threading\n");
        cleanupAppState();
        return -1;
    }

    // エンコーダースレッドを開始
    if (!startEncoderThread()) {
        fprintf(stderr, "Failed to start encoder thread\n");
        cleanupThreading();
        cleanupAppState();
        return -1;
    }

    // パフォーマンスメトリクスと進捗表示を初期化
    initPerformanceMetrics();
    initProgressDisplay();

    // レンダリングループ
    int frameCounter = 0;
    bool renderingComplete = false;

    // プレビューモードの場合はエラーメッセージを表示して終了
    if (appState.previewMode) {
        printf("Error: Preview mode is not supported in headless mode.\n");
        printf("Please run without preview mode for video rendering.\n");
        cleanupAppState();
        return -1;
    }

    // ヘッドレスレンダリングループ
    printf("Starting headless rendering loop...\n");
    printf("Press 'q' to quit early.\n");
    while (!renderingComplete) {
        startFrameTimer();

        // qキーが押されたかチェック
        if (checkForQuitKey()) {
            printf("\nUser requested quit. Stopping rendering...\n");
            break;
        }

        // 定期的にメモリ状況をチェック
        if (frameCounter % 100 == 0) {
            printf("Processing frame %d/%d...\n", frameCounter, appState.totalFrames);
        }

        // 現在のフレームの情報を計算
        float currentTime = (float)frameCounter / FPS;
        uint32_t currentNotes = countNotesUpToTime(currentTime);

        // NPSの計算ウィンドウを現在の時間より前の1秒間に変更
        float windowStart = currentTime - 1.0f;
        float windowEnd = currentTime;
        if (windowStart < 0.0f) windowStart = 0.0f;
        uint32_t currentNps = countNotesInTimeWindow(windowStart, windowEnd);
        uint16_t currentPolyphony = calculatePolyphonyAtTime(currentTime);

        // 最大値を更新
        updateMaxValues(currentNps, currentPolyphony);

        // エンコーダースレッドがビジー状態でなければ新しいフレームを処理
        if (!isEncoderThreadBusy() && frameCounter < appState.totalFrames) {
            // 安全性チェック
            if (!appState.midiFile) {
                fprintf(stderr, "Error: MIDI file is null at frame %d\n", frameCounter);
                break;
            }

            // フレームをレンダリング
            sk_sp<SkImage> frameImage = nullptr;
            try {
                frameImage = renderFrame(currentTime, currentNotes, appState.midiFile->totalNotes,
                                       currentNps, currentPolyphony);
            } catch (...) {
                fprintf(stderr, "Exception occurred during frame rendering at frame %d\n", frameCounter);
                break;
            }

            // イメージの有効性チェック
            if (!frameImage) {
                fprintf(stderr, "Error: Failed to render frame %d\n", frameCounter);
                continue;
            }

            // エンコーダースレッドにジョブを送信
            if (!submitRenderJob(frameCounter, currentTime, currentNotes, appState.midiFile->totalNotes,
                                currentNps, appState.maxNps, currentPolyphony, appState.maxPolyphony, frameImage)) {
                fprintf(stderr, "Error submitting render job for frame %d\n", frameCounter);
                break;
            }

            // 次のフレームへ
            frameCounter++;
        }

        // 進捗表示を更新（3秒ごと）
        updateProgressDisplay(frameCounter, appState.totalFrames, currentTime,
                             currentNotes, appState.midiFile->totalNotes,
                             currentNps, appState.maxNps,
                             currentPolyphony, appState.maxPolyphony);

        // パフォーマンスメトリクスを更新
        endFrameTimer(frameCounter, appState.totalFrames);

        // レンダリングが完了したかチェック
        renderingComplete = (frameCounter >= appState.totalFrames) && !isEncoderThreadBusy();

        // 少し待機してCPU使用率を下げる
        if (isEncoderThreadBusy()) {
            // エンコーダースレッドがビジー状態の場合は短い待機
            struct timespec ts;
            ts.tv_sec = 0;
            ts.tv_nsec = 1000000; // 1ミリ秒
            nanosleep(&ts, NULL);
        }
    }

    // ビデオエンコーダーを終了
    finalizeVideoEncoder();

    // エンコーダースレッドを停止
    stopEncoderThread();

    // リソースを解放
    cleanupVideoEncoder();
    cleanupThreading();
    cleanupFontContext();
    cleanupTextureManager();
#ifdef GPU_RENDERING_ENABLED
    cleanupBatchRenderer();
    cleanupGPUContext();
#endif
    cleanupAppState();

    printf("Video encoding process completed.\n");
    return 0;
}
