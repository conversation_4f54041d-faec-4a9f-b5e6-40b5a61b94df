#include "../../include/gpu_batch_renderer.h"
#include "../../include/gpu_context.h"
#include "../../include/font_manager.h"
#include "../../include/graphs.h"
#include "../../include/utils/config_manager.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <iostream>
#include <include/core/SkColor.h>

// グローバルバッチレンダラー
GPUBatchRenderer batchRenderer = {0};

// バッチレンダラーの初期化
bool initBatchRenderer() {
    if (batchRenderer.initialized) {
        return true;
    }

    printf("Initializing GPU batch renderer...\n");

    // vectorの初期化
    batchRenderer.rects = new std::vector<BatchRect>();
    batchRenderer.filledRects = new std::vector<BatchRect>();
    batchRenderer.lines = new std::vector<BatchLine>();
    batchRenderer.texts = new std::vector<BatchText>();

    if (!batchRenderer.rects || !batchRenderer.filledRects ||
        !batchRenderer.lines || !batchRenderer.texts) {
        fprintf(stderr, "Failed to allocate batch vectors\n");
        return false;
    }

    // 容量を事前確保（パフォーマンス向上）
    batchRenderer.rects->reserve(10000);
    batchRenderer.filledRects->reserve(10000);
    batchRenderer.lines->reserve(5000);
    batchRenderer.texts->reserve(1000);

    // 共通ペイントオブジェクトの作成
    batchRenderer.rectPaint = new SkPaint();
    batchRenderer.linePaint = new SkPaint();
    batchRenderer.textPaint = new SkPaint();

    if (!batchRenderer.rectPaint || !batchRenderer.linePaint || !batchRenderer.textPaint) {
        fprintf(stderr, "Failed to allocate paint objects\n");
        return false;
    }

    // ペイントの初期設定
    batchRenderer.rectPaint->setAntiAlias(false); // 矩形は高速化のためアンチエイリアス無効
    batchRenderer.linePaint->setAntiAlias(true);
    batchRenderer.linePaint->setStrokeCap(SkPaint::kRound_Cap);
    batchRenderer.textPaint->setAntiAlias(true);

    batchRenderer.initialized = true;
    printf("✓ GPU batch renderer initialized\n");
    return true;
}

// バッチレンダラーのクリーンアップ
void cleanupBatchRenderer() {
    if (!batchRenderer.initialized) {
        return;
    }

    printf("Cleaning up GPU batch renderer...\n");

    // vectorの削除
    delete batchRenderer.rects;
    delete batchRenderer.filledRects;
    delete batchRenderer.lines;
    delete batchRenderer.texts;

    // ペイントオブジェクトの削除
    delete batchRenderer.rectPaint;
    delete batchRenderer.linePaint;
    delete batchRenderer.textPaint;

    memset(&batchRenderer, 0, sizeof(GPUBatchRenderer));
    printf("✓ GPU batch renderer cleaned up\n");
}

// バッチに矩形を追加
void addRectToBatch(float x, float y, float width, float height, uint32_t color, bool filled) {
    if (!batchRenderer.initialized) {
        return;
    }

    BatchRect rect = {x, y, width, height, color};

    if (filled) {
        batchRenderer.filledRects->push_back(rect);
    } else {
        batchRenderer.rects->push_back(rect);
    }
}

// バッチに線を追加
void addLineToBatch(float x1, float y1, float x2, float y2, uint32_t color, float strokeWidth) {
    if (!batchRenderer.initialized) {
        return;
    }

    BatchLine line = {x1, y1, x2, y2, color, strokeWidth};
    batchRenderer.lines->push_back(line);
}

// バッチにテキストを追加
void addTextToBatch(const char* text, float x, float y, float fontSize, uint32_t color) {
    if (!batchRenderer.initialized || !text) {
        return;
    }

    BatchText textItem = {text, x, y, fontSize, color};
    batchRenderer.texts->push_back(textItem);
}

// 矩形バッチを実行
void flushRectBatch(SkCanvas* canvas) {
    if (!batchRenderer.initialized || !canvas) {
        return;
    }

    // 塗りつぶし矩形を描画
    if (!batchRenderer.filledRects->empty()) {
        batchRenderer.rectPaint->setStyle(SkPaint::kFill_Style);

        for (const auto& rect : *batchRenderer.filledRects) {
            batchRenderer.rectPaint->setColor(rect.color);
            canvas->drawRect(SkRect::MakeXYWH(rect.x, rect.y, rect.width, rect.height),
                           *batchRenderer.rectPaint);
        }
    }

    // 枠線矩形を描画
    if (!batchRenderer.rects->empty()) {
        batchRenderer.rectPaint->setStyle(SkPaint::kStroke_Style);
        batchRenderer.rectPaint->setStrokeWidth(1.0f);

        for (const auto& rect : *batchRenderer.rects) {
            batchRenderer.rectPaint->setColor(rect.color);
            canvas->drawRect(SkRect::MakeXYWH(rect.x, rect.y, rect.width, rect.height),
                           *batchRenderer.rectPaint);
        }
    }
}

// 線バッチを実行
void flushLineBatch(SkCanvas* canvas) {
    if (!batchRenderer.initialized || !canvas || batchRenderer.lines->empty()) {
        return;
    }

    batchRenderer.linePaint->setStyle(SkPaint::kStroke_Style);

    for (const auto& line : *batchRenderer.lines) {
        batchRenderer.linePaint->setColor(line.color);
        batchRenderer.linePaint->setStrokeWidth(line.strokeWidth);
        canvas->drawLine(line.x1, line.y1, line.x2, line.y2, *batchRenderer.linePaint);
    }
}

// テキストバッチを実行
void flushTextBatch(SkCanvas* canvas) {
    if (!batchRenderer.initialized || !canvas || batchRenderer.texts->empty()) {
        return;
    }

    for (const auto& text : *batchRenderer.texts) {
        batchRenderer.textPaint->setColor(text.color);
        SkFont font = setFont(FONT_DEFAULT, text.fontSize);
        canvas->drawString(text.text, text.x, text.y, font, *batchRenderer.textPaint);
    }
}

// すべてのバッチを実行
void flushAllBatches(SkCanvas* canvas) {
    if (!batchRenderer.initialized || !canvas) {
        return;
    }

    // 描画順序: 背景 → 線 → テキスト
    flushRectBatch(canvas);
    flushLineBatch(canvas);
    flushTextBatch(canvas);
}

// バッチをクリア
void clearBatches() {
    if (!batchRenderer.initialized) {
        return;
    }

    batchRenderer.rects->clear();
    batchRenderer.filledRects->clear();
    batchRenderer.lines->clear();
    batchRenderer.texts->clear();
}

// 最適化されたピアノロール描画（バッチ処理版）
void drawPianoRollBatched(SkCanvas* canvas, float currentTime) {
    if (!batchRenderer.initialized) {
        return;
    }

    // バッチをクリア
    clearBatches();

    // 設定マネージャーから色を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();

    // 背景を追加 - 設定から背景色を使用
    uint32_t bgColor = colorConfig.backgroundColor;
    // 16進数の色フォーマットから各成分を抽出
    uint8_t r = (bgColor >> 16) & 0xFF;
    uint8_t g = (bgColor >> 8) & 0xFF;
    uint8_t b = bgColor & 0xFF;
    uint8_t a = 0xFF;
    
    addRectToBatch(0, 0, 1920, 600, SkColorSetARGB(a, r, g, b), true);

    // オクターブ線を追加 - 設定からグリッド線の色を使用
    uint32_t gridColor = colorConfig.gridLineColor;
    for (int octave = 0; octave <= 10; octave++) {
        float x = octave * 160.0f; // 仮の計算
        addLineToBatch(x, 0, x, 600, SkColorSetARGB(77, (gridColor >> 16) & 0xFF, 
                                                    (gridColor >> 8) & 0xFF, 
                                                    gridColor & 0xFF), 1.0f);
    }

    // すべてのバッチを実行
    flushAllBatches(canvas);
}

// 最適化されたグラフ描画（バッチ処理版）
void drawGraphsBatched(SkCanvas* canvas) {
    if (!batchRenderer.initialized) {
        return;
    }

    // バッチをクリア
    clearBatches();

    // 現在は既存のグラフ描画を呼び出して、バッチ処理は背景のみ
    // 将来的には完全にバッチ処理に移行予定

    // 設定マネージャーから色とvisual設定を取得
    ConfigManager* configManager = ConfigManager::getInstance();
    const ColorConfig& colorConfig = configManager->getColorConfig();
    const VisualConfig& visualConfig = configManager->getVisualConfig();

    // 共通色定義 - 設定から色を使用
    uint32_t graphBgColor = colorConfig.graphBackgroundColor;
    uint32_t borderColor = colorConfig.borderColor;
    
    // グラフ背景色とボーダー色を使用

    // グラフ背景色のRGBA成分
    uint8_t gr = (graphBgColor >> 16) & 0xFF;
    uint8_t gg = (graphBgColor >> 8) & 0xFF;
    uint8_t gb = graphBgColor & 0xFF;
    uint8_t ga = 204; // 80%不透明度
    
    // ボーダー色のRGBA成分
    uint8_t br = (borderColor >> 16) & 0xFF;
    uint8_t bg = (borderColor >> 8) & 0xFF;
    uint8_t bb = borderColor & 0xFF;
    uint8_t ba = 255; // 完全不透明
    
    // アルファを追加
    uint32_t bgColor = SkColorSetARGB(ga, gr, gg, gb);
    uint32_t uiBorderColor = SkColorSetARGB(ba, br, bg, bb);

    // 動的レイアウトを更新
    extern void updateGraphPositions(int width, int height);
    updateGraphPositions(1920, 1080); // 画面サイズは固定値を使用、実際の実装では適切な値を取得

    // 実際のグラフ位置を取得
    extern GraphPositions graphPos;

    // 主要なグラフの背景をバッチ処理で描画（設定に基づく）
    // NPSグラフ
    if (visualConfig.graphs.showNps) {
        addRectToBatch(graphPos.x_nps, graphPos.y_nps, graphPos.width_nps, graphPos.height_nps, bgColor, true);
        addRectToBatch(graphPos.x_nps, graphPos.y_nps, graphPos.width_nps, graphPos.height_nps, uiBorderColor, false);
    }

    // Polyphonyグラフ
    if (visualConfig.graphs.showPolyphony) {
        addRectToBatch(graphPos.x_poly, graphPos.y_poly, graphPos.width_poly, graphPos.height_poly, bgColor, true);
        addRectToBatch(graphPos.x_poly, graphPos.y_poly, graphPos.width_poly, graphPos.height_poly, uiBorderColor, false);
    }

    // BPMグラフ
    if (visualConfig.graphs.showBpm) {
        addRectToBatch(graphPos.x_bpm, graphPos.y_bpm, graphPos.width_bpm, graphPos.height_bpm, bgColor, true);
        addRectToBatch(graphPos.x_bpm, graphPos.y_bpm, graphPos.width_bpm, graphPos.height_bpm, uiBorderColor, false);
    }

    // バッチを実行（背景のみ）
    flushAllBatches(canvas);

    // 既存のグラフ描画を呼び出し（線とテキスト）
    // TODO: これもバッチ処理に移行する
    extern void drawGraphs(SkCanvas* canvas);
    drawGraphs(canvas);

    // 最初のフレームでのみ統計を出力
    static bool firstFrame = true;
    if (firstFrame) {
        printf("✓ GPU batch rendering: %zu background rects processed\n",
               batchRenderer.filledRects->size() + batchRenderer.rects->size());
        firstFrame = false;
    }
}
