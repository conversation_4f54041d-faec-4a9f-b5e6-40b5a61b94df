#include <stdio.h>
#include <string.h>
#include "../../include/string_utils.h"

// 数値をカンマ区切りの文字列に変換する関数
void formatNumberWithCommas(char *buffer, size_t bufferSize, uint32_t number) {
    char tempBuffer[32];
    snprintf(tempBuffer, sizeof(tempBuffer), "%u", number);

    int length = strlen(tempBuffer);
    int commaCount = (length - 1) / 3;

    if (commaCount == 0) {
        // カンマが不要な場合はそのままコピー
        strncpy(buffer, tempBuffer, bufferSize - 1);
        buffer[bufferSize - 1] = '\0';
        return;
    }

    // カンマを含めた長さを計算
    int resultLength = length + commaCount;
    if (resultLength >= bufferSize) {
        // バッファサイズが足りない場合は切り詰め
        strncpy(buffer, tempBuffer, bufferSize - 1);
        buffer[bufferSize - 1] = '\0';
        return;
    }

    // カンマを挿入しながらコピー
    buffer[resultLength] = '\0';
    int tempIndex = length - 1;
    int resultIndex = resultLength - 1;
    int count = 0;

    while (tempIndex >= 0) {
        buffer[resultIndex--] = tempBuffer[tempIndex--];
        count++;
        if (count % 3 == 0 && tempIndex >= 0) {
            buffer[resultIndex--] = ',';
        }
    }
}

// ファイル名から拡張子を取得する関数
const char* getFileExtension(const char* filePath) {
    const char* lastDot = strrchr(filePath, '.');
    if (lastDot && lastDot != filePath) {
        return lastDot;
    }
    return ".mp4"; // デフォルトの拡張子
}

// ファイル名からベース名を取得する関数
void getBaseName(const char* filePath, char* baseNameBuffer, size_t bufferSize) {
    char *lastSlash = strrchr(filePath, '\\');
    if (!lastSlash) lastSlash = strrchr(filePath, '/'); // Linux/Mac パス区切り文字も考慮
    char *lastDot = strrchr(filePath, '.');

    if (lastSlash && lastDot && lastDot > lastSlash) {
        // ファイル名部分を抽出
        int nameLength = lastDot - (lastSlash + 1);
        if (nameLength > 0 && nameLength < bufferSize - 1) {
            strncpy(baseNameBuffer, lastSlash + 1, nameLength);
            baseNameBuffer[nameLength] = '\0';
        } else {
            strncpy(baseNameBuffer, "midi_output", bufferSize - 1);
            baseNameBuffer[bufferSize - 1] = '\0';
        }
    } else if (lastDot) { // パス区切りなし、拡張子あり (e.g. "myfile.mid")
        int nameLength = lastDot - filePath;
        if (nameLength > 0 && nameLength < bufferSize - 1) {
            strncpy(baseNameBuffer, filePath, nameLength);
            baseNameBuffer[nameLength] = '\0';
        } else {
            strncpy(baseNameBuffer, "midi_output", bufferSize - 1);
            baseNameBuffer[bufferSize - 1] = '\0';
        }
    } else {
        // パス区切りも拡張子もない、または不正な場合
        strncpy(baseNameBuffer, "midi_output", bufferSize - 1);
        baseNameBuffer[bufferSize - 1] = '\0';
    }
}

// 文字列に日本語文字が含まれているかを確認する関数
bool containsJapaneseChars(const char* path) {
    if (!path) return false;

    const unsigned char* p = (const unsigned char*)path;
    while (*p) {
        // UTF-8エンコードされた日本語文字の検出
        // 3バイト文字（ほとんどの日本語文字）
        if ((*p & 0xE0) == 0xE0 && (*(p+1) & 0xC0) == 0x80 && (*(p+2) & 0xC0) == 0x80) {
            return true;
        }
        // 4バイト文字（一部の特殊な日本語文字）
        if ((*p & 0xF0) == 0xF0 && (*(p+1) & 0xC0) == 0x80 && (*(p+2) & 0xC0) == 0x80 && (*(p+3) & 0xC0) == 0x80) {
            return true;
        }

        // 次の文字へ
        if ((*p & 0x80) == 0) {
            p += 1; // ASCII
        } else if ((*p & 0xE0) == 0xC0) {
            p += 2; // 2バイト文字
        } else if ((*p & 0xF0) == 0xE0) {
            p += 3; // 3バイト文字
        } else if ((*p & 0xF8) == 0xF0) {
            p += 4; // 4バイト文字
        } else {
            p += 1; // 不正なUTF-8シーケンス、1バイト進める
        }
    }

    return false;
}