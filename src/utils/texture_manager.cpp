#include "../../include/utils/texture_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <include/core/SkData.h>
#include <include/core/SkImage.h>
#include <include/core/SkCanvas.h>
#include <include/core/SkPaint.h>
#include <include/core/SkColorFilter.h>

// バッファリングシステムを削除して最適化

// bin2cで生成されたテクスチャデータの定義
// 各テクスチャデータを配列として定義
static unsigned char g_key_white_data[] = {
    #include "keyWhite.png.h"
};

static unsigned char g_key_white_pressed_data[] = {
    #include "keyWhitePressed.png.h"
};

static unsigned char g_key_black_data[] = {
    #include "keyBlack.png.h"
};

static unsigned char g_key_black_pressed_data[] = {
    #include "keyBlackPressed.png.h"
};

static unsigned char g_note_data[] = {
    #include "note.png.h"
};

// テクスチャイメージの配列
static sk_sp<SkImage> textures[TEXTURE_COUNT] = {nullptr};

// PNGデータからSkiaイメージを作成する関数
static sk_sp<SkImage> createImageFromPngData(const unsigned char* data, size_t length) {
    // データからSkDataを作成
    sk_sp<SkData> skData = SkData::MakeWithCopy(data, length);
    if (!skData) {
        fprintf(stderr, "Failed to create SkData from PNG data\n");
        return nullptr;
    }

    // SkDataからSkImageを作成
    sk_sp<SkImage> image = SkImage::MakeFromEncoded(skData);
    if (!image) {
        fprintf(stderr, "Failed to create SkImage from encoded data\n");
        return nullptr;
    }

    return image;
}

// テクスチャマネージャーの初期化
bool initTextureManager(void) {
    // 各テクスチャを読み込む
    textures[TEXTURE_KEY_WHITE] = createImageFromPngData(g_key_white_data, sizeof(g_key_white_data));
    textures[TEXTURE_KEY_WHITE_PRESSED] = createImageFromPngData(g_key_white_pressed_data, sizeof(g_key_white_pressed_data));
    textures[TEXTURE_KEY_BLACK] = createImageFromPngData(g_key_black_data, sizeof(g_key_black_data));
    textures[TEXTURE_KEY_BLACK_PRESSED] = createImageFromPngData(g_key_black_pressed_data, sizeof(g_key_black_pressed_data));
    textures[TEXTURE_NOTE] = createImageFromPngData(g_note_data, sizeof(g_note_data));

    // すべてのテクスチャが正常に読み込まれたか確認
    for (int i = 0; i < TEXTURE_COUNT; i++) {
        if (!textures[i]) {
            fprintf(stderr, "Failed to load texture %d\n", i);
            cleanupTextureManager();
            return false;
        }
    }

    return true;
}

// テクスチャマネージャーのクリーンアップ
void cleanupTextureManager(void) {
    // 通常のテクスチャを解放
    for (int i = 0; i < TEXTURE_COUNT; i++) {
        textures[i] = nullptr;
    }
}

// 指定されたテクスチャを取得
sk_sp<SkImage> getTexture(TextureType type) {
    if (type >= 0 && type < TEXTURE_COUNT) {
        return textures[type];
    }
    return nullptr;
}

// テクスチャを描画する（指定された位置とサイズ）- 最適化版
void drawTexture(SkCanvas* canvas, TextureType type, float x, float y, float width, float height) {
    if (!canvas) return;
    if (type < 0 || type >= TEXTURE_COUNT) return;
    if (width <= 0 || height <= 0) return;

    sk_sp<SkImage> texture = getTexture(type);
    if (!texture) return;

    // 描画先の矩形を設定
    SkRect destRect = SkRect::MakeXYWH(x, y, width, height);

    // 高速サンプリングオプション
    SkSamplingOptions samplingOptions(SkFilterMode::kLinear);
    canvas->drawImageRect(texture, destRect, samplingOptions);
}

// テクスチャを描画する（色付き）- 最適化版
void drawTextureWithColor(SkCanvas* canvas, TextureType type, float x, float y, float width, float height,
                         float r, float g, float b, float a) {
    // 安全チェック：パラメータが有効か確認
    if (!canvas) return;
    if (type < 0 || type >= TEXTURE_COUNT) return;
    if (width <= 0 || height <= 0) return;

    // テクスチャを取得
    sk_sp<SkImage> texture = getTexture(type);
    if (!texture) return;

    // 色の値を0.0〜1.0の範囲に制限
    r = (r < 0.0f) ? 0.0f : ((r > 1.0f) ? 1.0f : r);
    g = (g < 0.0f) ? 0.0f : ((g > 1.0f) ? 1.0f : g);
    b = (b < 0.0f) ? 0.0f : ((b > 1.0f) ? 1.0f : b);
    a = (a < 0.0f) ? 0.0f : ((a > 1.0f) ? 1.0f : a);

    // 描画先の矩形を設定
    SkRect destRect = SkRect::MakeXYWH(x, y, width, height);

    // 最適化されたペイント設定
    SkPaint paint;
    paint.setAlphaf(a);

    // シンプルなカラーフィルターを使用（乗算モード）
    paint.setColorFilter(SkColorFilters::Blend(
        SkColorSetARGB(255, (int)(r * 255), (int)(g * 255), (int)(b * 255)),
        SkBlendMode::kModulate));

    // 高速サンプリングオプション
    SkSamplingOptions samplingOptions(SkFilterMode::kLinear);
    canvas->drawImageRect(texture, destRect, samplingOptions, &paint);
}



// バッファリングシステムを削除し、最適化されたテクスチャ描画のみを提供
