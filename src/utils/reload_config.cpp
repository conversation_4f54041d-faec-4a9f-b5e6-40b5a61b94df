#include "../../include/utils/config_manager.h"
#include "../../include/utils/color.h"
#include <iostream>

// 設定ファイルを再読み込みする関数
void reloadConfiguration() {
    // ConfigManagerのインスタンスを取得
    ConfigManager* configManager = ConfigManager::getInstance();
      // 設定ファイルを再読み込み
    if (configManager->loadConfig()) {
        // 色設定を再適用
        const ColorConfig& colors = configManager->getColorConfig();
        
        // チャンネルカラーを更新
        for (int i = 0; i < 16; i++) {
            channel_colors[i] = colors.channelColors[i];
        }
    }
}
