#include "../../include/utils/executable_path.h"

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <limits.h>
#endif

#include <filesystem>
#include <iostream>

// 実行ファイルのパスを取得する
std::string getExecutablePath() {
    std::string executablePath;

#ifdef _WIN32
    // Windowsの場合
    char path[MAX_PATH];
    DWORD result = GetModuleFileNameA(NULL, path, MAX_PATH);
    if (result > 0) {
        executablePath = path;
    }
#else
    // Linux/MacOSの場合
    char path[PATH_MAX];
    ssize_t count = readlink("/proc/self/exe", path, PATH_MAX);
    if (count != -1) {
        path[count] = '\0';
        executablePath = path;
    }
#endif

    return executablePath;
}

// 実行ファイルのディレクトリを取得する
std::string getExecutableDir() {
    std::string path = getExecutablePath();
    if (!path.empty()) {
        std::filesystem::path exePath(path);
        return exePath.parent_path().string();
    }
    return "";
}
