#include "../../include/utils/color.h"
#include "../../include/utils/config_manager.h"
#include <iostream>

// チャンネルカラーの定義（初期値として暗めの色を設定）
unsigned int channel_colors[16] = {
    0x882222, // チャンネル0: 暗めの赤
    0x228822, // チャンネル1: 暗めの緑
    0x222288, // チャンネル2: 暗めの青
    0x886622, // チャンネル3: 暗めの黄
    0x882288, // チャンネル4: 暗めのマゼンタ
    0x228888, // チャンネル5: 暗めのシアン
    0x884422, // チャンネル6: 暗めのオレンジ
    0x552288, // チャンネル7: 暗めの紫
    0x228844, // チャンネル8: 暗めのライトグリーン
    0x225588, // チャンネル9: 暗めのライトブルー
    0x882244, // チャンネル10: 暗めのピンク
    0x448822, // チャンネル11: 暗めのライム
    0x444488, // チャンネル12: 暗めのラベンダー
    0x888844, // チャンネル13: 暗めのライトイエロー
    0x884488, // チャンネル14: 暗めのライトマゼンタ
    0x448888  // チャンネル15: 暗めのライトシアン
};

// カラー初期化関数
void initColors(void) {
    // configファイルから色を読み込む（実行ファイルと同じディレクトリの設定ファイルを使用）
    ConfigManager* configManager = ConfigManager::getInstance();
    
    // デフォルト設定ファイルのパスを取得
    std::string defaultConfigPath = ConfigManager::getDefaultConfigFilePath();
    
    if (!configManager->loadConfig()) {
        // デフォルトの色を使用
    } else {
        // 設定から色を取得
        const ColorConfig& colors = configManager->getColorConfig();
        
        // グローバル配列に設定された値を適用
        for (int i = 0; i < 16; i++) {
            channel_colors[i] = colors.channelColors[i];
        }
    }
}

// RGBからunsigned intへの変換
unsigned int rgb_to_uint(uint8_t r, uint8_t g, uint8_t b) {
    return ((unsigned int)r << 16) | ((unsigned int)g << 8) | (unsigned int)b;
}

// unsigned intからRGBへの変換
void uint_to_rgb(unsigned int color, uint8_t* r, uint8_t* g, uint8_t* b) {
    if (r) *r = (color >> 16) & 0xFF;
    if (g) *g = (color >> 8) & 0xFF;
    if (b) *b = color & 0xFF;
}

// カラーの明るさを調整する関数
unsigned int adjust_brightness(unsigned int color, float factor) {
    uint8_t r, g, b;
    uint_to_rgb(color, &r, &g, &b);

    r = (uint8_t)(r * factor > 255 ? 255 : r * factor);
    g = (uint8_t)(g * factor > 255 ? 255 : g * factor);
    b = (uint8_t)(b * factor > 255 ? 255 : b * factor);

    return rgb_to_uint(r, g, b);
}
