#include <stdio.h>
#include <windows.h>
#include <psapi.h>
#include <map>
#include <string>
#include <chrono>
#include "../../include/performance.h"
#include "../../include/string_utils.h"

// パフォーマンス計測用のマップ
static std::map<std::string, std::chrono::high_resolution_clock::time_point> performanceTimers;

// グローバルパフォーマンスメトリクス
PerformanceMetrics perfMetrics = {0};

// 進捗表示用の変数
static std::chrono::high_resolution_clock::time_point lastProgressTime;
static bool progressDisplayInitialized = false;

// パフォーマンスメトリクスを初期化する関数
void initPerformanceMetrics() {
    perfMetrics.totalStartTime = clock();
    perfMetrics.frameStartTime = 0;
    perfMetrics.totalElapsedSeconds = 0.0;
    perfMetrics.frameElapsedSeconds = 0.0;
    perfMetrics.averageFrameTime = 0.0;
    perfMetrics.currentFps = 0.0;
    perfMetrics.cpuUsage = 0.0;
}

// フレームタイマーを開始する関数
void startFrameTimer() {
    perfMetrics.frameStartTime = clock();
}

// フレームタイマーを終了し、メトリクスを更新する関数
void endFrameTimer(int frameCounter, int totalFrames) {
    clock_t frameEndTime = clock();
    perfMetrics.frameElapsedSeconds = (double)(frameEndTime - perfMetrics.frameStartTime) / CLOCKS_PER_SEC;
    perfMetrics.totalElapsedSeconds = (double)(frameEndTime - perfMetrics.totalStartTime) / CLOCKS_PER_SEC;

    if (frameCounter > 0) {
        perfMetrics.averageFrameTime = perfMetrics.totalElapsedSeconds / (frameCounter + 1);
        perfMetrics.currentFps = (perfMetrics.averageFrameTime > 0) ? (1.0 / perfMetrics.averageFrameTime) : 60.0; // 0除算防止
    } else {
        perfMetrics.currentFps = (perfMetrics.frameElapsedSeconds > 0) ? (1.0 / perfMetrics.frameElapsedSeconds) : 60.0;
    }

    perfMetrics.cpuUsage = getCPUUsage();
}

// CPU使用率を取得する関数
double getCPUUsage() {
    FILETIME idleTime, kernelTime, userTime;
    static FILETIME prevIdleTime = {0}, prevKernelTime = {0}, prevUserTime = {0};
    static ULARGE_INTEGER prevIdle = {0}, prevKernel = {0}, prevUser = {0};
    static ULARGE_INTEGER idle, kernel, user;
    static double cpuUsage = 0.0;

    // システム時間情報を取得
    if (!GetSystemTimes(&idleTime, &kernelTime, &userTime)) {
        return 0.0;
    }

    // FILETIMEをULARGE_INTEGERに変換
    idle.LowPart = idleTime.dwLowDateTime;
    idle.HighPart = idleTime.dwHighDateTime;
    kernel.LowPart = kernelTime.dwLowDateTime;
    kernel.HighPart = kernelTime.dwHighDateTime;
    user.LowPart = userTime.dwLowDateTime;
    user.HighPart = userTime.dwHighDateTime;

    // 初回呼び出し時は前回値を設定して0を返す
    if (prevIdle.QuadPart == 0) {
        prevIdle = idle;
        prevKernel = kernel;
        prevUser = user;
        return 0.0;
    }

    // 差分を計算
    ULONGLONG idleDiff = idle.QuadPart - prevIdle.QuadPart;
    ULONGLONG kernelDiff = kernel.QuadPart - prevKernel.QuadPart;
    ULONGLONG userDiff = user.QuadPart - prevUser.QuadPart;
    ULONGLONG totalDiff = kernelDiff + userDiff;

    // CPU使用率を計算（アイドル時間以外の割合）
    if (totalDiff > 0) {
        cpuUsage = 100.0 - ((double)idleDiff * 100.0 / (double)totalDiff);
    }

    // 現在値を前回値として保存
    prevIdle = idle;
    prevKernel = kernel;
    prevUser = user;

    return cpuUsage;
}

// 残り時間を推定する関数
double getEstimatedRemainingTime(int frameCounter, int totalFrames) {
    double estimatedTotalTime = perfMetrics.averageFrameTime * totalFrames;
    double remainingTime = (frameCounter > 0 && perfMetrics.averageFrameTime > 0) ?
                          (estimatedTotalTime - perfMetrics.totalElapsedSeconds) :
                          (totalFrames * (1.0/60.0) - perfMetrics.totalElapsedSeconds);

    if (remainingTime < 0) remainingTime = 0;

    return remainingTime;
}

// 詳細なパフォーマンス計測を開始する関数
void startPerformanceMeasurement(const char* name) {
    if (!name) return;

    // 現在時刻を記録
    performanceTimers[name] = std::chrono::high_resolution_clock::now();
}

// 詳細なパフォーマンス計測を終了し、結果を表示する関数
void endPerformanceMeasurement(const char* name) {
    if (!name) return;

    // 名前に対応するタイマーが存在するか確認
    auto it = performanceTimers.find(name);
    if (it == performanceTimers.end()) {
        return;
    }

    // 経過時間を計算
    auto endTime = std::chrono::high_resolution_clock::now();
    auto startTime = it->second;
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();

    // マイクロ秒からミリ秒に変換
    double milliseconds = duration / 1000.0;

    // デバッグ出力（Windowsではデバッグ出力が動作に影響するため、コメントアウト）
    // printf("Performance [%s]: %.3f ms\n", name, milliseconds);

    // タイマーを削除
    performanceTimers.erase(it);
}

// 進捗表示を初期化する関数
void initProgressDisplay() {
    lastProgressTime = std::chrono::high_resolution_clock::now();
    progressDisplayInitialized = true;
    printf("=== HMP7 Headless Renderer Started ===\n");
    printf("Progress will be displayed every 3 seconds.\n\n");
}

// 時間を時:分:秒形式に変換するヘルパー関数
static void formatTimeString(double seconds, char* buffer, size_t bufferSize) {
    int hours = (int)(seconds / 3600);
    int minutes = (int)((seconds - hours * 3600) / 60);
    int secs = (int)(seconds - hours * 3600 - minutes * 60);

    if (hours > 0) {
        snprintf(buffer, bufferSize, "%d:%02d:%02d", hours, minutes, secs);
    } else {
        snprintf(buffer, bufferSize, "%d:%02d", minutes, secs);
    }
}

// 進捗表示を更新する関数（3秒ごとに表示）
void updateProgressDisplay(int frameCounter, int totalFrames, float currentTime,
                          uint32_t currentNotes, uint32_t totalNotes,
                          uint32_t currentNps, uint32_t maxNps,
                          uint16_t currentPolyphony, uint16_t maxPolyphony) {
    if (!progressDisplayInitialized) {
        initProgressDisplay();
    }

    auto currentTimePoint = std::chrono::high_resolution_clock::now();
    auto timeSinceLastProgress = std::chrono::duration_cast<std::chrono::seconds>(
        currentTimePoint - lastProgressTime).count();

    // 3秒経過したか、最初のフレームか、最後のフレームの場合に表示
    if (timeSinceLastProgress >= 1 || frameCounter == 0 || frameCounter >= totalFrames - 1) {
        lastProgressTime = currentTimePoint;

        // 進捗率を計算
        float progress = (totalFrames > 0) ? ((float)frameCounter / (float)totalFrames) * 100.0f : 0.0f;

        // 残り時間を計算
        double remainingTime = getEstimatedRemainingTime(frameCounter, totalFrames);

        // 経過時間を計算
        double elapsedTime = perfMetrics.totalElapsedSeconds;

        // 時間を時:分:秒形式に変換
        char elapsedStr[32];
        char remainingStr[32];
        char currentTimeStr[32];
        formatTimeString(elapsedTime, elapsedStr, sizeof(elapsedStr));
        formatTimeString(remainingTime, remainingStr, sizeof(remainingStr));
        formatTimeString(currentTime, currentTimeStr, sizeof(currentTimeStr));

        // 数値をカンマ区切りに変換
        char frameCounterStr[32], totalFramesStr[32];
        char currentNotesStr[32], totalNotesStr[32];
        char currentNpsStr[32], maxNpsStr[32];

        formatNumberWithCommas(frameCounterStr, sizeof(frameCounterStr), frameCounter);
        formatNumberWithCommas(totalFramesStr, sizeof(totalFramesStr), totalFrames);
        formatNumberWithCommas(currentNotesStr, sizeof(currentNotesStr), currentNotes);
        formatNumberWithCommas(totalNotesStr, sizeof(totalNotesStr), totalNotes);
        formatNumberWithCommas(currentNpsStr, sizeof(currentNpsStr), currentNps);
        formatNumberWithCommas(maxNpsStr, sizeof(maxNpsStr), maxNps);

        printf("=== Progress Report ===\n");
        printf("Frame: %s / %s (%.1f%%)\n", frameCounterStr, totalFramesStr, progress);
        printf("Time: %s / %.1fs\n", currentTimeStr, currentTime);
        printf("Notes: %s / %s\n", currentNotesStr, totalNotesStr);
        printf("NPS: %s (Max: %s)\n", currentNpsStr, maxNpsStr);
        printf("Polyphony: %d (Max: %d)\n", currentPolyphony, maxPolyphony);
        printf("Elapsed: %s\n", elapsedStr);
        printf("ETA: %s\n", remainingStr);
        printf("FPS: %.1f\n", perfMetrics.currentFps);
        printf("CPU: %.1f%%\n", perfMetrics.cpuUsage);
        printf("========================\n\n");
    }
}
