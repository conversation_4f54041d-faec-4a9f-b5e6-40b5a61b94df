#include "../../include/utils/config_manager.h"
#include "../../include/utils/color.h"
#include "../../include/utils/executable_path.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <algorithm>
#include <cctype>

// 静的インスタンス初期化
ConfigManager* ConfigManager::instance = nullptr;

// コンストラクタ
ConfigManager::ConfigManager() : configFormat(FORMAT_UNKNOWN) {
    // デフォルト値を設定
    setDefaults();
}

// インスタンスを取得
ConfigManager* ConfigManager::getInstance() {
    if (!instance) {
        instance = new ConfigManager();
    }
    return instance;
}

// ファイル形式を検出
ConfigFormat ConfigManager::detectFileFormat(const std::string& filePath) {
    // 拡張子でファイル形式を判断
    std::filesystem::path path(filePath);
    std::string extension = path.extension().string();
    
    if (extension == ".toml") {
        return FORMAT_TOML;
    } else if (extension == ".json") {
        return FORMAT_JSON;
    }
    
    // 拡張子で判断できない場合は、ファイルの内容を見る
    std::ifstream file(filePath);
    if (!file.is_open()) {
        return FORMAT_UNKNOWN;
    }
    
    std::string line;
    if (std::getline(file, line)) {
        // JSONはほとんどの場合、{から始まる
        if (line.find('{') != std::string::npos) {
            return FORMAT_JSON;
        }
        // TOMLは[セクション]やkey = valueの形式
        else if (line.find('[') != std::string::npos || line.find('=') != std::string::npos) {
            return FORMAT_TOML;
        }
    }
    
    return FORMAT_UNKNOWN;
}

// デフォルト値を設定
void ConfigManager::setDefaults() {
    // 色モードのデフォルト値
    colorConfig.colorMode = COLOR_MODE_CHANNEL;     // デフォルトはチャンネルモード

    // 既存のチャンネル色を使用
    for (int i = 0; i < 16; i++) {
        colorConfig.channelColors[i] = channel_colors[i];
    }
      // UI色のデフォルト値
    colorConfig.backgroundColor = 0x000000;         // 黒
    colorConfig.gridLineColor = 0x444444;           // 濃いグレー
    colorConfig.borderColor = 0x808080;             // グレー
    colorConfig.graphBackgroundColor = 0x333333;    // 暗いグレー
    colorConfig.counter_textColor = 0xFFFFFF;       // カウンター表示テキスト（デフォルトは白）
    colorConfig.graph_textColor = 0xFFFFFF;         // グラフ内テキスト（デフォルトは白）
    colorConfig.graphProgressBarColor = 0x00CC00;   // 進捗バー色（緑）
    colorConfig.graphCenterLineColor = 0x808080;    // 中央線色（グレー）

    // Visual設定のデフォルト値
    visualConfig.showAll = true;
    visualConfig.showPiano = true;
    visualConfig.showPianoroll = true;
    visualConfig.showHighlight = true;
    visualConfig.showGraphs = true;
    visualConfig.showCounters = true;
    visualConfig.showTextEvents = true;
    visualConfig.showDebug = true;

    // 個別グラフ設定のデフォルト値
    visualConfig.graphs.showNotes = true;
    visualConfig.graphs.showPolyphony = true;
    visualConfig.graphs.showNps = true;
    visualConfig.graphs.showBpm = true;
    visualConfig.graphs.showPan = true;
    visualConfig.graphs.showPitchBend = true;
    visualConfig.graphs.showVolume = true;
    visualConfig.graphs.showSustain = true;
    visualConfig.graphs.showFilterCutoff = true;
    visualConfig.graphs.showFilterResonance = true;
    visualConfig.graphs.showReleaseTime = true;

    // カウンター設定のデフォルト値
    visualConfig.counters.showTime = true;
    visualConfig.counters.showNotes = true;
    visualConfig.counters.showNps = true;
    visualConfig.counters.showPolyphony = true;

    // テキストイベント設定のデフォルト値
    visualConfig.textEvents.showMarkers = true;
    visualConfig.textEvents.showText = true;
    visualConfig.textEvents.showLyrics = true;

    // Funny設定のデフォルト値
    funnyConfig.bugMode = false;
}

// TOMLファイルから読み込む
bool ConfigManager::loadFromToml(const std::string& filePath) {
    try {
        // TOMLファイルを解析
        tomlData = toml::parse_file(filePath);        // 色設定を読み込み
        if (auto colorTable = tomlData["colors"]) {
            // 色モードを読み込み
            if (auto colorModeValue = colorTable["color_mode"]) {
                std::string colorModeStr = colorModeValue.as_string()->get();
                if (colorModeStr == "channel") {
                    colorConfig.colorMode = COLOR_MODE_CHANNEL;
                } else if (colorModeStr == "track") {
                    colorConfig.colorMode = COLOR_MODE_TRACK;
                }
            }

            // チャンネル色を読み込み
            if (auto channelTable = colorTable["channel"]) {
                for (int i = 0; i < 16; i++) {
                    std::string key = "ch" + std::to_string(i);
                    if (auto colorValue = channelTable[key]) {
                        std::string hexColor = colorValue.as_string()->get();
                        colorConfig.channelColors[i] = parseHexColor(hexColor);
                    }
                }
            }
              // UI色を読み込み
            if (auto background = colorTable["background"]) {
                std::string hexColor = background.as_string()->get();
                colorConfig.backgroundColor = parseHexColor(hexColor);
            }
            
            if (auto gridLine = colorTable["grid_line"]) {
                std::string hexColor = gridLine.as_string()->get();
                colorConfig.gridLineColor = parseHexColor(hexColor);
            }
              if (auto border = colorTable["border"]) {
                std::string hexColor = border.as_string()->get();
                colorConfig.borderColor = parseHexColor(hexColor);
            }
              if (auto graphBg = colorTable["graph_background"]) {
                std::string hexColor = graphBg.as_string()->get();
                colorConfig.graphBackgroundColor = parseHexColor(hexColor);
            }
            
            if (auto counterText = colorTable["counter_text"]) {
                std::string hexColor = counterText.as_string()->get();
                colorConfig.counter_textColor = parseHexColor(hexColor);
            }
            
            if (auto graphText = colorTable["graph_text"]) {
                std::string hexColor = graphText.as_string()->get();
                colorConfig.graph_textColor = parseHexColor(hexColor);
            }

            if (auto graphProgressBar = colorTable["graph_progress_bar"]) {
                std::string hexColor = graphProgressBar.as_string()->get();
                colorConfig.graphProgressBarColor = parseHexColor(hexColor);
            }

            if (auto graphCenterLine = colorTable["graph_center_line"]) {
                std::string hexColor = graphCenterLine.as_string()->get();
                colorConfig.graphCenterLineColor = parseHexColor(hexColor);
            }
        }

        // Visual設定を読み込み
        if (auto visualTable = tomlData["visual"]) {
            if (auto showAll = visualTable["show_all"]) {
                visualConfig.showAll = showAll.as_boolean()->get();
            }
            if (auto showPiano = visualTable["show_piano"]) {
                visualConfig.showPiano = showPiano.as_boolean()->get();
            }
            if (auto showPianoroll = visualTable["show_pianoroll"]) {
                visualConfig.showPianoroll = showPianoroll.as_boolean()->get();
            }
            if (auto showHighlight = visualTable["show_highlight"]) {
                visualConfig.showHighlight = showHighlight.as_boolean()->get();
            }
            if (auto showGraphs = visualTable["show_graphs"]) {
                visualConfig.showGraphs = showGraphs.as_boolean()->get();
            }
            if (auto showCounters = visualTable["show_counters"]) {
                visualConfig.showCounters = showCounters.as_boolean()->get();
            }
            if (auto showTextEvents = visualTable["show_text_events"]) {
                visualConfig.showTextEvents = showTextEvents.as_boolean()->get();
            }
            if (auto showDebug = visualTable["show_debug"]) {
                visualConfig.showDebug = showDebug.as_boolean()->get();
            }

            // 個別グラフ設定を読み込み
            if (auto graphsTable = visualTable["graphs"]) {
                if (auto showNotes = graphsTable["show_notes"]) {
                    visualConfig.graphs.showNotes = showNotes.as_boolean()->get();
                }
                if (auto showPolyphony = graphsTable["show_polyphony"]) {
                    visualConfig.graphs.showPolyphony = showPolyphony.as_boolean()->get();
                }
                if (auto showNps = graphsTable["show_nps"]) {
                    visualConfig.graphs.showNps = showNps.as_boolean()->get();
                }

                if (auto showBpm = graphsTable["show_bpm"]) {
                    visualConfig.graphs.showBpm = showBpm.as_boolean()->get();
                }
                if (auto showPan = graphsTable["show_pan"]) {
                    visualConfig.graphs.showPan = showPan.as_boolean()->get();
                }
                if (auto showPitchBend = graphsTable["show_pitch_bend"]) {
                    visualConfig.graphs.showPitchBend = showPitchBend.as_boolean()->get();
                }
                if (auto showVolume = graphsTable["show_volume"]) {
                    visualConfig.graphs.showVolume = showVolume.as_boolean()->get();
                }
                if (auto showSustain = graphsTable["show_sustain"]) {
                    visualConfig.graphs.showSustain = showSustain.as_boolean()->get();
                }
                if (auto showFilterCutoff = graphsTable["show_filter_cutoff"]) {
                    visualConfig.graphs.showFilterCutoff = showFilterCutoff.as_boolean()->get();
                }
                if (auto showFilterResonance = graphsTable["show_filter_resonance"]) {
                    visualConfig.graphs.showFilterResonance = showFilterResonance.as_boolean()->get();
                }
                if (auto showReleaseTime = graphsTable["show_release_time"]) {
                    visualConfig.graphs.showReleaseTime = showReleaseTime.as_boolean()->get();
                }
            }

            // カウンター設定を読み込み
            if (auto countersTable = visualTable["counters"]) {
                if (auto showTime = countersTable["show_time"]) {
                    visualConfig.counters.showTime = showTime.as_boolean()->get();
                }
                if (auto showNotes = countersTable["show_notes"]) {
                    visualConfig.counters.showNotes = showNotes.as_boolean()->get();
                }
                if (auto showNps = countersTable["show_nps"]) {
                    visualConfig.counters.showNps = showNps.as_boolean()->get();
                }
                if (auto showPolyphony = countersTable["show_polyphony"]) {
                    visualConfig.counters.showPolyphony = showPolyphony.as_boolean()->get();
                }
            }

            // テキストイベント設定を読み込み
            if (auto textEventsTable = visualTable["text_events"]) {
                if (auto showMarkers = textEventsTable["show_markers"]) {
                    visualConfig.textEvents.showMarkers = showMarkers.as_boolean()->get();
                }
                if (auto showText = textEventsTable["show_text"]) {
                    visualConfig.textEvents.showText = showText.as_boolean()->get();
                }
                if (auto showLyrics = textEventsTable["show_lyrics"]) {
                    visualConfig.textEvents.showLyrics = showLyrics.as_boolean()->get();
                }
            }
        }

        // Funny設定を読み込み
        if (auto funnyTable = tomlData["funny"]) {
            if (auto bugMode = funnyTable["bug_mode"]) {
                funnyConfig.bugMode = bugMode.as_boolean()->get();
            }
        }

        configFormat = FORMAT_TOML;
        return true;
    } catch (const toml::parse_error& e) {
        std::cerr << "Error parsing TOML config file: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error loading TOML config: " << e.what() << std::endl;
        return false;
    }
}

// JSONファイルから読み込む
bool ConfigManager::loadFromJson(const std::string& filePath) {
    try {        // JSONファイルを解析
        std::ifstream file(filePath);
        if (!file.is_open()) {
            std::cerr << "Failed to open JSON config file: " << filePath << std::endl;
            return false;
        }
        
        jsonData = nlohmann::json::parse(file);
        
        // 色設定を読み込み
        if (jsonData.contains("colors")) {
            auto& colors = jsonData["colors"];

            // 色モードを読み込み
            if (colors.contains("color_mode")) {
                std::string colorModeStr = colors["color_mode"];
                if (colorModeStr == "channel") {
                    colorConfig.colorMode = COLOR_MODE_CHANNEL;
                } else if (colorModeStr == "track") {
                    colorConfig.colorMode = COLOR_MODE_TRACK;
                }
            }

            // チャンネル色を読み込み
            if (colors.contains("channel")) {
                auto& channel = colors["channel"];

                for (int i = 0; i < 16; i++) {
                    std::string key = "ch" + std::to_string(i);
                    if (channel.contains(key)) {
                        std::string hexColor = channel[key];
                        colorConfig.channelColors[i] = parseHexColor(hexColor);
                    }
                }
            }
            
            // UI色を読み込み
            if (colors.contains("background")) {
                colorConfig.backgroundColor = parseHexColor(colors["background"]);
            }
            
            if (colors.contains("grid_line")) {
                colorConfig.gridLineColor = parseHexColor(colors["grid_line"]);
            }
              if (colors.contains("border")) {
                colorConfig.borderColor = parseHexColor(colors["border"]);
            }
              if (colors.contains("graph_background")) {
                colorConfig.graphBackgroundColor = parseHexColor(colors["graph_background"]);
            }
            
            if (colors.contains("counter_text")) {
                colorConfig.counter_textColor = parseHexColor(colors["counter_text"]);
            }
            
            if (colors.contains("graph_text")) {
                colorConfig.graph_textColor = parseHexColor(colors["graph_text"]);
            }

            if (colors.contains("graph_progress_bar")) {
                colorConfig.graphProgressBarColor = parseHexColor(colors["graph_progress_bar"]);
            }

            if (colors.contains("graph_center_line")) {
                colorConfig.graphCenterLineColor = parseHexColor(colors["graph_center_line"]);
            }
        }

        // Visual設定を読み込み
        if (jsonData.contains("visual")) {
            auto& visual = jsonData["visual"];

            if (visual.contains("show_all")) {
                visualConfig.showAll = visual["show_all"];
            }
            if (visual.contains("show_piano")) {
                visualConfig.showPiano = visual["show_piano"];
            }
            if (visual.contains("show_pianoroll")) {
                visualConfig.showPianoroll = visual["show_pianoroll"];
            }
            if (visual.contains("show_graphs")) {
                visualConfig.showGraphs = visual["show_graphs"];
            }
            if (visual.contains("show_counters")) {
                visualConfig.showCounters = visual["show_counters"];
            }
            if (visual.contains("show_text_events")) {
                visualConfig.showTextEvents = visual["show_text_events"];
            }
            if (visual.contains("show_debug")) {
                visualConfig.showDebug = visual["show_debug"];
            }

            // 個別グラフ設定を読み込み
            if (visual.contains("graphs")) {
                auto& graphs = visual["graphs"];
                if (graphs.contains("show_notes")) {
                    visualConfig.graphs.showNotes = graphs["show_notes"];
                }
                if (graphs.contains("show_polyphony")) {
                    visualConfig.graphs.showPolyphony = graphs["show_polyphony"];
                }
                if (graphs.contains("show_nps")) {
                    visualConfig.graphs.showNps = graphs["show_nps"];
                }

                if (graphs.contains("show_bpm")) {
                    visualConfig.graphs.showBpm = graphs["show_bpm"];
                }
                if (graphs.contains("show_pan")) {
                    visualConfig.graphs.showPan = graphs["show_pan"];
                }
                if (graphs.contains("show_pitch_bend")) {
                    visualConfig.graphs.showPitchBend = graphs["show_pitch_bend"];
                }
                if (graphs.contains("show_volume")) {
                    visualConfig.graphs.showVolume = graphs["show_volume"];
                }
                if (graphs.contains("show_sustain")) {
                    visualConfig.graphs.showSustain = graphs["show_sustain"];
                }
                if (graphs.contains("show_filter_cutoff")) {
                    visualConfig.graphs.showFilterCutoff = graphs["show_filter_cutoff"];
                }
                if (graphs.contains("show_filter_resonance")) {
                    visualConfig.graphs.showFilterResonance = graphs["show_filter_resonance"];
                }
                if (graphs.contains("show_release_time")) {
                    visualConfig.graphs.showReleaseTime = graphs["show_release_time"];
                }
            }

            // カウンター設定を読み込み
            if (visual.contains("counters")) {
                auto& counters = visual["counters"];
                if (counters.contains("show_time")) {
                    visualConfig.counters.showTime = counters["show_time"];
                }
                if (counters.contains("show_notes")) {
                    visualConfig.counters.showNotes = counters["show_notes"];
                }
                if (counters.contains("show_nps")) {
                    visualConfig.counters.showNps = counters["show_nps"];
                }
                if (counters.contains("show_polyphony")) {
                    visualConfig.counters.showPolyphony = counters["show_polyphony"];
                }
            }

            // テキストイベント設定を読み込み
            if (visual.contains("text_events")) {
                auto& textEvents = visual["text_events"];
                if (textEvents.contains("show_markers")) {
                    visualConfig.textEvents.showMarkers = textEvents["show_markers"];
                }
                if (textEvents.contains("show_text")) {
                    visualConfig.textEvents.showText = textEvents["show_text"];
                }
                if (textEvents.contains("show_lyrics")) {
                    visualConfig.textEvents.showLyrics = textEvents["show_lyrics"];
                }
            }
        }

        // Funny設定を読み込み
        if (jsonData.contains("funny")) {
            auto& funny = jsonData["funny"];
            if (funny.contains("bug_mode")) {
                funnyConfig.bugMode = funny["bug_mode"];
            }
        }

        configFormat = FORMAT_JSON;
        return true;
    } catch (const nlohmann::json::parse_error& e) {
        std::cerr << "Error parsing JSON config file: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error loading JSON config: " << e.what() << std::endl;
        return false;
    }
}

// 設定ファイルを読み込む
bool ConfigManager::loadConfig(const std::string& filePath) {
    try {
        // まず実行ファイルのディレクトリを取得
        std::string exeDir = getExecutableDir();
        
        // ファイルパスが指定されていれば保存する
        if (!filePath.empty()) {
            // 相対パスの場合は実行ファイルディレクトリからの相対パスとする
            if (std::filesystem::path(filePath).is_relative()) {
                configFilePath = exeDir + "/" + filePath;
            } else {
                configFilePath = filePath;
            }
        } else if (configFilePath.empty()) {
            // 実行ファイルと同じディレクトリにあるconfig.tomlとconfig.jsonを探す
            std::string tomlPath = exeDir + "/config.toml";
            std::string jsonPath = exeDir + "/config.json";
              if (std::filesystem::exists(tomlPath)) {
                configFilePath = tomlPath;
            } else if (std::filesystem::exists(jsonPath)) {
                configFilePath = jsonPath;
            } else {
                return false;
            }
        }
        
        // ファイル形式を検出
        configFormat = detectFileFormat(configFilePath);
        bool success = false;
        
        // 形式に応じた読み込み処理
        switch (configFormat) {
            case FORMAT_TOML:
                success = loadFromToml(configFilePath);
                break;
                
            case FORMAT_JSON:
                success = loadFromJson(configFilePath);
                break;
                  default:
                // 未知のファイル形式の場合はTOMLとJSONの両方を試す
                success = loadFromToml(configFilePath);
                if (!success) {
                    success = loadFromJson(configFilePath);
                }
                break;
        }
        
        if (!success) {
            std::cerr << "Failed to load config file, using defaults." << std::endl;
            return false;
        }
        
        // チャンネル色を既存の配列にも反映
        for (int i = 0; i < 16; i++) {
            channel_colors[i] = colorConfig.channelColors[i];
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading config: " << e.what() << std::endl;
        std::cerr << "Using default values." << std::endl;
        return false;
    }
}

// TOMLファイルとして保存
bool ConfigManager::saveToToml(const std::string& filePath) {
    try {
        // TOMLデータを作成
        toml::table data;
        toml::table colors;
        toml::table channelColors;
        
        // 色モードを設定
        std::string colorModeStr = (colorConfig.colorMode == COLOR_MODE_CHANNEL) ? "channel" : "track";
        colors.insert_or_assign("color_mode", colorModeStr);

        // チャンネル色を設定
        for (int i = 0; i < 16; i++) {
            std::string key = "ch" + std::to_string(i);
            channelColors.insert_or_assign(key, colorToHexString(colorConfig.channelColors[i]));
        }

        // UI色を設定
        colors.insert_or_assign("background", colorToHexString(colorConfig.backgroundColor));        colors.insert_or_assign("grid_line", colorToHexString(colorConfig.gridLineColor));
        colors.insert_or_assign("border", colorToHexString(colorConfig.borderColor));
        colors.insert_or_assign("graph_background", colorToHexString(colorConfig.graphBackgroundColor));
        colors.insert_or_assign("counter_text", colorToHexString(colorConfig.counter_textColor));
        colors.insert_or_assign("graph_text", colorToHexString(colorConfig.graph_textColor));
        colors.insert_or_assign("graph_progress_bar", colorToHexString(colorConfig.graphProgressBarColor));
        colors.insert_or_assign("graph_center_line", colorToHexString(colorConfig.graphCenterLineColor));

        // テーブルを構築
        colors.insert_or_assign("channel", channelColors);
        data.insert_or_assign("colors", colors);

        // Visual設定を追加
        toml::table visual;
        visual.insert_or_assign("show_all", visualConfig.showAll);
        visual.insert_or_assign("show_piano", visualConfig.showPiano);
        visual.insert_or_assign("show_pianoroll", visualConfig.showPianoroll);
        visual.insert_or_assign("show_highlight", visualConfig.showHighlight);
        visual.insert_or_assign("show_graphs", visualConfig.showGraphs);
        visual.insert_or_assign("show_counters", visualConfig.showCounters);
        visual.insert_or_assign("show_text_events", visualConfig.showTextEvents);
        visual.insert_or_assign("show_debug", visualConfig.showDebug);

        // 個別グラフ設定
        toml::table graphs;
        graphs.insert_or_assign("show_notes", visualConfig.graphs.showNotes);
        graphs.insert_or_assign("show_polyphony", visualConfig.graphs.showPolyphony);
        graphs.insert_or_assign("show_nps", visualConfig.graphs.showNps);

        graphs.insert_or_assign("show_bpm", visualConfig.graphs.showBpm);
        graphs.insert_or_assign("show_pan", visualConfig.graphs.showPan);
        graphs.insert_or_assign("show_pitch_bend", visualConfig.graphs.showPitchBend);
        graphs.insert_or_assign("show_volume", visualConfig.graphs.showVolume);
        graphs.insert_or_assign("show_sustain", visualConfig.graphs.showSustain);
        graphs.insert_or_assign("show_filter_cutoff", visualConfig.graphs.showFilterCutoff);
        graphs.insert_or_assign("show_filter_resonance", visualConfig.graphs.showFilterResonance);
        graphs.insert_or_assign("show_release_time", visualConfig.graphs.showReleaseTime);
        visual.insert_or_assign("graphs", graphs);

        // カウンター設定
        toml::table counters;
        counters.insert_or_assign("show_time", visualConfig.counters.showTime);
        counters.insert_or_assign("show_notes", visualConfig.counters.showNotes);
        counters.insert_or_assign("show_nps", visualConfig.counters.showNps);
        counters.insert_or_assign("show_polyphony", visualConfig.counters.showPolyphony);
        visual.insert_or_assign("counters", counters);

        // テキストイベント設定
        toml::table textEvents;
        textEvents.insert_or_assign("show_markers", visualConfig.textEvents.showMarkers);
        textEvents.insert_or_assign("show_text", visualConfig.textEvents.showText);
        textEvents.insert_or_assign("show_lyrics", visualConfig.textEvents.showLyrics);
        visual.insert_or_assign("text_events", textEvents);

        data.insert_or_assign("visual", visual);

        // Funny設定
        toml::table funny;
        funny.insert_or_assign("bug_mode", funnyConfig.bugMode);
        data.insert_or_assign("funny", funny);

          // ファイルに書き込み
        std::ofstream file(filePath);
        if (file) {
            file << "# HachimitsuMIDIPlayer 色設定ファイル\n";
            file << "# $schema = \"./config.schema.json\"\n\n";
            file << data;
            return true;
        } else {
            std::cerr << "Failed to open TOML file for writing: " << filePath << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error saving TOML config: " << e.what() << std::endl;
        return false;
    }
}

// JSONファイルとして保存
bool ConfigManager::saveToJson(const std::string& filePath) {
    try {
        // JSONデータを作成
        nlohmann::json data;
        nlohmann::json colors;
        nlohmann::json channelColors;
        
        // 色モードを設定
        std::string colorModeStr = (colorConfig.colorMode == COLOR_MODE_CHANNEL) ? "channel" : "track";
        colors["color_mode"] = colorModeStr;

        // チャンネル色を設定
        for (int i = 0; i < 16; i++) {
            std::string key = "ch" + std::to_string(i);
            channelColors[key] = colorToHexString(colorConfig.channelColors[i]);
        }

        // UI色を設定
        colors["background"] = colorToHexString(colorConfig.backgroundColor);
        colors["grid_line"] = colorToHexString(colorConfig.gridLineColor);
        colors["border"] = colorToHexString(colorConfig.borderColor);
        colors["graph_background"] = colorToHexString(colorConfig.graphBackgroundColor);
        colors["counter_text"] = colorToHexString(colorConfig.counter_textColor);
        colors["graph_text"] = colorToHexString(colorConfig.graph_textColor);
        colors["graph_progress_bar"] = colorToHexString(colorConfig.graphProgressBarColor);
        colors["graph_center_line"] = colorToHexString(colorConfig.graphCenterLineColor);

        // テーブルを構築
        colors["channel"] = channelColors;
        data["colors"] = colors;
        data["$schema"] = "./config.schema.json";

        // Visual設定を追加
        nlohmann::json visual;
        visual["show_all"] = visualConfig.showAll;
        visual["show_piano"] = visualConfig.showPiano;
        visual["show_pianoroll"] = visualConfig.showPianoroll;
        visual["show_graphs"] = visualConfig.showGraphs;
        visual["show_counters"] = visualConfig.showCounters;
        visual["show_text_events"] = visualConfig.showTextEvents;
        visual["show_debug"] = visualConfig.showDebug;

        // 個別グラフ設定
        nlohmann::json graphs;
        graphs["show_notes"] = visualConfig.graphs.showNotes;
        graphs["show_polyphony"] = visualConfig.graphs.showPolyphony;
        graphs["show_nps"] = visualConfig.graphs.showNps;

        graphs["show_bpm"] = visualConfig.graphs.showBpm;
        graphs["show_pan"] = visualConfig.graphs.showPan;
        graphs["show_pitch_bend"] = visualConfig.graphs.showPitchBend;
        graphs["show_volume"] = visualConfig.graphs.showVolume;
        graphs["show_sustain"] = visualConfig.graphs.showSustain;
        graphs["show_filter_cutoff"] = visualConfig.graphs.showFilterCutoff;
        graphs["show_filter_resonance"] = visualConfig.graphs.showFilterResonance;
        graphs["show_release_time"] = visualConfig.graphs.showReleaseTime;
        visual["graphs"] = graphs;

        // カウンター設定
        nlohmann::json counters;
        counters["show_time"] = visualConfig.counters.showTime;
        counters["show_notes"] = visualConfig.counters.showNotes;
        counters["show_nps"] = visualConfig.counters.showNps;
        counters["show_polyphony"] = visualConfig.counters.showPolyphony;
        visual["counters"] = counters;

        // テキストイベント設定
        nlohmann::json textEvents;
        textEvents["show_markers"] = visualConfig.textEvents.showMarkers;
        textEvents["show_text"] = visualConfig.textEvents.showText;
        textEvents["show_lyrics"] = visualConfig.textEvents.showLyrics;
        visual["text_events"] = textEvents;

        data["visual"] = visual;

        // Funny設定
        nlohmann::json funny;
        funny["bug_mode"] = funnyConfig.bugMode;
        data["funny"] = funny;

          // ファイルに書き込み
        std::ofstream file(filePath);
        if (file) {
            file << data.dump(2); // インデント2でフォーマット
            return true;
        } else {
            std::cerr << "Failed to open JSON file for writing: " << filePath << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error saving JSON config: " << e.what() << std::endl;
        return false;
    }
}

// 設定ファイルを保存する
bool ConfigManager::saveConfig(const std::string& filePath, ConfigFormat format) {
    try {
        // まず実行ファイルのディレクトリを取得
        std::string exeDir = getExecutableDir();
        
        // ファイルパスが指定されていれば保存する
        if (!filePath.empty()) {
            // 相対パスの場合は実行ファイルディレクトリからの相対パスとする
            if (std::filesystem::path(filePath).is_relative()) {
                configFilePath = exeDir + "/" + filePath;
            } else {
                configFilePath = filePath;
            }
        } else if (configFilePath.empty()) {
            // 設定ファイルのパスが未設定の場合は、実行ファイルと同じディレクトリにconfig.tomlとして保存
            configFilePath = exeDir + "/config.toml";
        }
        
        // 保存形式が指定されていない場合は現在の形式または拡張子から判断
        if (format == FORMAT_UNKNOWN) {
            if (configFormat != FORMAT_UNKNOWN) {
                format = configFormat;
            } else {
                format = detectFileFormat(configFilePath);
                if (format == FORMAT_UNKNOWN) {
                    // デフォルトはTOML
                    format = FORMAT_TOML;
                }
            }
        }
        
        // 形式に応じた保存処理
        switch (format) {
            case FORMAT_TOML:
                return saveToToml(configFilePath);
                
            case FORMAT_JSON:
                return saveToJson(configFilePath);
                
            default:
                std::cerr << "Unknown format specified for saving." << std::endl;
                return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error saving config: " << e.what() << std::endl;
        return false;
    }
}

// 色設定を取得
const ColorConfig& ConfigManager::getColorConfig() const {
    return colorConfig;
}

// 色設定を更新
void ConfigManager::updateColorConfig(const ColorConfig& newColors) {
    colorConfig = newColors;

    // チャンネル色を既存の配列にも反映
    for (int i = 0; i < 16; i++) {
        channel_colors[i] = colorConfig.channelColors[i];
    }
}

// Visual設定を取得
const VisualConfig& ConfigManager::getVisualConfig() const {
    return visualConfig;
}

// Visual設定を更新
void ConfigManager::updateVisualConfig(const VisualConfig& newVisual) {
    visualConfig = newVisual;
}

// Funny設定を取得
const FunnyConfig& ConfigManager::getFunnyConfig() const {
    return funnyConfig;
}

// Funny設定を更新
void ConfigManager::updateFunnyConfig(const FunnyConfig& newFunny) {
    funnyConfig = newFunny;
}

// 特定のチャンネル色を取得
uint32_t ConfigManager::getChannelColor(int channel) const {
    if (channel >= 0 && channel < 16) {
        return colorConfig.channelColors[channel];
    }
    return 0xFFFFFF; // デフォルトは白
}

// 特定のチャンネル色を設定
void ConfigManager::setChannelColor(int channel, uint32_t color) {
    if (channel >= 0 && channel < 16) {
        colorConfig.channelColors[channel] = color;
        channel_colors[channel] = color; // 既存の配列にも反映
    }
}

// トラックベースの色を取得（トラック番号に基づく）
uint32_t ConfigManager::getTrackColor(int trackIndex) const {
    if (trackIndex < 0) {
        return 0xFFFFFF; // デフォルトは白
    }

    // 最初の16トラックは既存のチャンネル色を使用
    if (trackIndex < 16) {
        return colorConfig.channelColors[trackIndex];
    }

    // 16以上のトラックはランダムな色を生成（シード値としてトラック番号を使用）
    // 同じトラック番号に対して常に同じ色を返すように、決定論的な方法で色を生成
    uint32_t seed = trackIndex * 0x9E3779B9; // 黄金比に基づく定数
    seed ^= seed >> 16;
    seed *= 0x85EBCA6B;
    seed ^= seed >> 13;
    seed *= 0xC2B2AE35;
    seed ^= seed >> 16;

    // RGB値を生成（暗すぎず明るすぎない範囲で）
    uint8_t r = 0x44 + (seed & 0x77);        // 68-187の範囲
    uint8_t g = 0x44 + ((seed >> 8) & 0x77); // 68-187の範囲
    uint8_t b = 0x44 + ((seed >> 16) & 0x77);// 68-187の範囲

    return (r << 16) | (g << 8) | b;
}

// 色モードに基づいて適切な色を取得（ピアノロール・ピアノキーボード用）
uint32_t ConfigManager::getPianoColor(int channel, int trackIndex) const {
    switch (colorConfig.colorMode) {
        case COLOR_MODE_CHANNEL:
            return getChannelColor(channel);
        case COLOR_MODE_TRACK:
            return getTrackColor(trackIndex);
        default:
            return getChannelColor(channel); // デフォルトはチャンネルモード
    }
}

// 16進数文字列から色を解析 (#RRGGBB or RRGGBB)
uint32_t ConfigManager::parseHexColor(const std::string& hexColor) {
    std::string cleanHex = hexColor;
    
    // #で始まる場合は削除
    if (!cleanHex.empty() && cleanHex[0] == '#') {
        cleanHex = cleanHex.substr(1);
    }
    
    // 全て小文字に変換（大文字小文字を区別しない）
    std::transform(cleanHex.begin(), cleanHex.end(), cleanHex.begin(),
                   [](unsigned char c){ return std::tolower(c); });
    
    // 16進数を解析
    try {
        // 16進数の長さを確認
        if (cleanHex.length() != 6) {
            std::cerr << "Invalid hex color format (should be 6 characters): " << hexColor << std::endl;
            return 0xFFFFFF; // デフォルトは白
        }
          // 16進数文字のみを含むことを確認
        for (char c : cleanHex) {
            if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f'))) {
                std::cerr << "Invalid hex character in color: " << hexColor << std::endl;
                return 0xFFFFFF; // デフォルトは白
            }
        }
        
        // 16進数文字列をRGB値に変換
        unsigned int r, g, b;
        std::stringstream ss;
        ss << std::hex << cleanHex.substr(0, 2);
        ss >> r;
        ss.clear();
        ss << std::hex << cleanHex.substr(2, 2);
        ss >> g;
        ss.clear();
        ss << std::hex << cleanHex.substr(4, 2);
        ss >> b;
          // RGB値を結合して32ビット整数値として返す
        unsigned int color = (r << 16) | (g << 8) | b;
        return color;
    } catch (const std::exception& e) {
        std::cerr << "Failed to parse color: " << hexColor << " - " << e.what() << std::endl;
        return 0xFFFFFF; // デフォルトは白
    }
}

// 色を16進数文字列に変換
std::string ConfigManager::colorToHexString(uint32_t color) {
    std::stringstream ss;
    ss << "#" << std::hex << std::setfill('0') << std::setw(6) << color;
    return ss.str();
}

// JSONスキーマを検証
bool ConfigManager::validateJsonSchema(const std::string& jsonFilePath, const std::string& schemaFilePath) {
    try {
        // JSONファイルを読み込み
        std::ifstream jsonFile(jsonFilePath);
        if (!jsonFile.is_open()) {
            std::cerr << "Failed to open JSON file: " << jsonFilePath << std::endl;
            return false;
        }
        
        // スキーマファイルを読み込み
        std::ifstream schemaFile(schemaFilePath);
        if (!schemaFile.is_open()) {
            std::cerr << "Failed to open schema file: " << schemaFilePath << std::endl;
            return false;
        }
        
        nlohmann::json jsonData = nlohmann::json::parse(jsonFile);
        nlohmann::json schemaData = nlohmann::json::parse(schemaFile);
          // 実際のスキーマ検証
        // 注: nlohmann/jsonには組み込みの検証機能がないため、
        // 完全な実装にはnlohmann/json-schemaなどの追加ライブラリが必要
        
        return true;
        
    } catch (const nlohmann::json::parse_error& e) {
        std::cerr << "Error parsing JSON for schema validation: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error during schema validation: " << e.what() << std::endl;
        return false;
    }
}

// 実行ファイルと同じディレクトリの設定ファイルパスを取得
std::string ConfigManager::getDefaultConfigFilePath() {
    std::string exeDir = getExecutableDir();
    
    // まずTOML形式を探す
    std::string tomlPath = exeDir + "/config.toml";
    if (std::filesystem::exists(tomlPath)) {
        return tomlPath;
    }
    
    // 次にJSON形式を探す
    std::string jsonPath = exeDir + "/config.json";
    if (std::filesystem::exists(jsonPath)) {
        return jsonPath;
    }
    
    // 見つからない場合はデフォルトのTOMLパスを返す
    return tomlPath;
}

// JSONスキーマファイルを生成
bool ConfigManager::generateJsonSchema(const std::string& schemaFilePath) {
    try {
        std::string finalSchemaPath;
        
        // ファイルパスが指定されていない場合は実行ファイルと同じディレクトリに生成
        if (schemaFilePath.empty()) {
            finalSchemaPath = getExecutableDir() + "/config.schema.json";
        } else {
            // 相対パスの場合は実行ファイルディレクトリからの相対パスとする
            if (std::filesystem::path(schemaFilePath).is_relative()) {
                finalSchemaPath = getExecutableDir() + "/" + schemaFilePath;
            } else {
                finalSchemaPath = schemaFilePath;
            }
        }
        
        // JSONスキーマを作成
        nlohmann::json schema;
        schema["$schema"] = "http://json-schema.org/draft-07/schema#";
        schema["title"] = "HachimitsuMIDIPlayer 設定ファイル";
        schema["description"] = "HachimitsuMIDIPlayer の色設定を定義するスキーマ";
        schema["type"] = "object";
        
        nlohmann::json colorsProps;
        
        // UI色のスキーマ定義
        colorsProps["background"] = {
            {"type", "string"},
            {"description", "アプリケーション全体の背景色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#000000", "000000"})}
        };
        
        colorsProps["grid_line"] = {
            {"type", "string"},
            {"description", "グリッド線の色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#444444", "444444"})}
        };
        
        colorsProps["text"] = {
            {"type", "string"},
            {"description", "テキストの色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#FFFFFF", "FFFFFF"})}
        };
        
        colorsProps["border"] = {
            {"type", "string"},
            {"description", "ボーダーの色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#808080", "808080"})}
        };
          colorsProps["graph_background"] = {
            {"type", "string"},
            {"description", "グラフの背景色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#333333", "333333"})}
        };
        
        colorsProps["counter_text"] = {
            {"type", "string"},
            {"description", "カウンター表示用テキスト色（Time, Notes, NPS, Polyphony, Markerなど）"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#FFFFFF", "FFFFFF"})}
        };
        
        colorsProps["graph_text"] = {
            {"type", "string"},
            {"description", "グラフ内のテキスト色"},
            {"pattern", "^#?[0-9a-fA-F]{6}$"},
            {"examples", nlohmann::json::array({"#FFFFFF", "FFFFFF"})}
        };
        
        // チャンネル色のスキーマ定義
        nlohmann::json channelProps;
        for (int i = 0; i < 16; i++) {
            std::string key = "ch" + std::to_string(i);
            channelProps[key] = {
                {"type", "string"},
                {"description", "チャンネル " + std::to_string(i) + " の色"},
                {"pattern", "^#?[0-9a-fA-F]{6}$"},
                {"examples", nlohmann::json::array({colorToHexString(colorConfig.channelColors[i])})}
            };
        }
        
        // チャンネル色を設定に追加
        nlohmann::json channelSchema;
        channelSchema["type"] = "object";
        channelSchema["description"] = "MIDIチャンネルの色設定";
        channelSchema["properties"] = channelProps;
        colorsProps["channel"] = channelSchema;
        
        // 色設定をスキーマに追加
        nlohmann::json colorsSchema;
        colorsSchema["type"] = "object";
        colorsSchema["description"] = "全ての色設定";
        colorsSchema["properties"] = colorsProps;
        
        nlohmann::json properties;
        properties["colors"] = colorsSchema;
        schema["properties"] = properties;
          // ファイルに書き込み
        std::ofstream file(finalSchemaPath);
        if (!file.is_open()) {
            std::cerr << "Failed to open schema file for writing: " << finalSchemaPath << std::endl;
            return false;
        }
        
        file << schema.dump(2); // インデント2でフォーマット
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating JSON schema: " << e.what() << std::endl;
        return false;
    }
}
