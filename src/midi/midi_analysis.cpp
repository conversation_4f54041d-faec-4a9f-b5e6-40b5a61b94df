#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include "../../include/midi_utils.h"

// グローバル変数
static uint32_t totalNotes = 0;

// ノート数をカウントする関数（総ノート数のみをカウント）
void countNotes(MidiFile* midiFile) {
    if (!midiFile || !midiFile->tracks) return;

    totalNotes = 0;

    // 各トラックを一度スキャンしてノート数をカウント
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        uint32_t pos = 0;
        uint32_t tick = 0;
        uint8_t runStatus = 0;

        while (pos < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (pos >= track->length) break;
                byte = track->data[pos++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            tick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (pos >= track->length) break;

            if (track->data[pos] & 0x80) {
                statusByte = track->data[pos++];
                runStatus = statusByte;
            } else {
                statusByte = runStatus;
            }

            // ノートONイベント (0x90) でベロシティが0より大きい場合のみカウント
            if ((statusByte & 0xF0) == 0x90) {
                if (pos + 1 < track->length) {
                    uint8_t velocity = track->data[pos + 1];
                    if (velocity > 0) {
                        totalNotes++;
                    }
                }
            }

            // データスキップ
            if (statusByte < 0xF0) {
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) {
                    pos += 1;
                } else {
                    pos += 2;
                }
            } else if (statusByte == 0xFF) {
                if (pos >= track->length) break;
                pos++; // メタタイプをスキップ

                uint32_t length = 0;
                do {
                    if (pos >= track->length) break;
                    byte = track->data[pos++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                pos += length;
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                uint32_t length = 0;
                do {
                    if (pos >= track->length) break;
                    byte = track->data[pos++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                pos += length;
            }
        }
    }

    printf("Total notes counted: %u\n", totalNotes);

    // MIDIファイル構造体に総ノート数を設定
    midiFile->totalNotes = totalNotes;
}

// 総再生時間を計算する関数
float calculateDuration(MidiFile* midiFile) {
    if (!midiFile) return 0.0f;

    // 最後のティックの時間を計算
    float duration = tickToSeconds(midiFile->totalTicks, midiFile->header.division);
    printf("Total duration: %.2f seconds\n", duration);

    return duration;
}

// MIDIファイルを解析する関数
void analyzeMidiFile(MidiFile* midiFile) {
    if (!midiFile) return;

    // テンポマップを初期化
    initTempoMap();

    // 総ティック数を計算（最後のイベントのティック位置）
    midiFile->totalTicks = 0;
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        uint32_t position = 0;
        uint32_t currentTick = 0;
        uint8_t runningStatus = 0;

        while (position < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (position >= track->length) break;
                byte = track->data[position++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (position >= track->length) break;

            if (track->data[position] & 0x80) {
                statusByte = track->data[position++];
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // データスキップ
            if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) {
                    position += 1;
                } else {
                    position += 2;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (position >= track->length) break;
                position++; // メタタイプをスキップ

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                position += length;
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    if (position >= track->length) break;
                    byte = track->data[position++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                position += length;
            }

            // 最大ティック数を更新
            if (currentTick > midiFile->totalTicks) {
                midiFile->totalTicks = currentTick;
            }
        }
    }

    printf("Total ticks: %u\n", midiFile->totalTicks);

    // 各トラックのテンポイベントをスキャン
    scanTempoEvents(midiFile);

    // ノートイベントをカウント
    countNotes(midiFile);

    // テキストイベントをスキャン
    scanTextEvents(midiFile);

    // 総再生時間を計算
    midiFile->durationSeconds = calculateDuration(midiFile);

    // 総ノート数のみ設定
    midiFile->totalNotes = totalNotes;
}
