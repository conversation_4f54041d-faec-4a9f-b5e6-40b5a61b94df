#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <ctype.h>
#include <windows.h>
#include <iconv.h>
#include "../../include/midi_utils.h"

// テキストイベントリストを初期化する関数
void initTextEventList(TextEventList* list) {
    if (!list) return;

    list->events = (TextEvent*)malloc(sizeof(TextEvent) * 10);
    list->count = 0;
    list->capacity = 10;

    if (!list->events) {
        fprintf(stderr, "Error: Memory allocation failed for text event list\n");
        list->capacity = 0;
    }
}

// テキストイベントを追加する関数
void addTextEvent(TextEventList* list, uint32_t tick, TextEventType type, const char* text, float time) {
    if (!list || !text) return;

    // テキストが空の場合でも追加する（デバッグ出力のみ）
    if (!*text) {
        printf("Adding empty %s event at tick %u\n",
               type == EVENT_TEXT ? "TEXT" :
               type == EVENT_LYRIC ? "LYRIC" :
               type == EVENT_MARKER ? "MARKER" : "UNKNOWN",
               tick);
    }

    // テキストが無効な場合でも追加する（デバッグ出力のみ）
    if (!isValidTextEvent(text)) {
        printf("Adding invalid %s event at tick %u: '%s'\n",
               type == EVENT_TEXT ? "TEXT" :
               type == EVENT_LYRIC ? "LYRIC" :
               type == EVENT_MARKER ? "MARKER" : "UNKNOWN",
               tick, text);
    }

    // 配列の拡張が必要な場合
    if (list->count >= list->capacity) {
        list->capacity *= 2;
        list->events = (TextEvent*)realloc(list->events, sizeof(TextEvent) * list->capacity);
        if (!list->events) {
            fprintf(stderr, "Error: Failed to reallocate memory for text event list\n");
            return;
        }
    }

    // 新しいテキストイベントを追加
    list->events[list->count].tick = tick;
    list->events[list->count].type = type;
    list->events[list->count].time = time;

    // テキストをコピー
    list->events[list->count].text = strdup(text);
    if (!list->events[list->count].text) {
        fprintf(stderr, "Error: Failed to allocate memory for text event string\n");
        return;
    }

    // デバッグ出力は削除

    list->count++;

    // テキストイベントをソート（ティック順）
    for (uint32_t i = 0; i < list->count - 1; i++) {
        for (uint32_t j = 0; j < list->count - i - 1; j++) {
            if (list->events[j].tick > list->events[j + 1].tick) {
                TextEvent temp = list->events[j];
                list->events[j] = list->events[j + 1];
                list->events[j + 1] = temp;
            }
        }
    }
}

// テキストイベントリストを解放する関数
void freeTextEventList(TextEventList* list) {
    if (!list) return;

    if (list->events) {
        for (uint32_t i = 0; i < list->count; i++) {
            if (list->events[i].text) {
                free(list->events[i].text);
            }
        }
        free(list->events);
    }

    list->events = NULL;
    list->count = 0;
    list->capacity = 0;
}

// 文字コードをUTF-8に変換する関数
char* convertToUTF8(const char* input, size_t length) {
    if (!input || length == 0) return NULL;

    // 入力がすでにUTF-8かどうかをチェック
    bool isUtf8 = true;
    for (size_t i = 0; i < length; i++) {
        if ((unsigned char)input[i] > 127) {
            isUtf8 = false;
            break;
        }
    }

    // すでにUTF-8の場合はそのままコピー
    if (isUtf8) {
        char* result = (char*)malloc(length + 1);
        if (!result) return NULL;
        memcpy(result, input, length);
        result[length] = '\0';
        return result;
    }

    // Windows APIを使用して変換を試みる
    // まず、CP932（日本語Shift-JIS）を試す
    int wideLen = MultiByteToWideChar(932, MB_ERR_INVALID_CHARS, input, length, NULL, 0);
    if (wideLen > 0) {
        wchar_t* wideStr = (wchar_t*)malloc((wideLen + 1) * sizeof(wchar_t));
        if (!wideStr) return NULL;

        MultiByteToWideChar(932, 0, input, length, wideStr, wideLen);
        wideStr[wideLen] = L'\0';

        int utf8Len = WideCharToMultiByte(CP_UTF8, 0, wideStr, wideLen, NULL, 0, NULL, NULL);
        if (utf8Len > 0) {
            char* utf8Str = (char*)malloc(utf8Len + 1);
            if (!utf8Str) {
                free(wideStr);
                return NULL;
            }

            WideCharToMultiByte(CP_UTF8, 0, wideStr, wideLen, utf8Str, utf8Len, NULL, NULL);
            utf8Str[utf8Len] = '\0';

            free(wideStr);
            return utf8Str;
        }
        free(wideStr);
    }

    // CP932が失敗した場合、CP_ACPを試す（システムのデフォルトコードページ）
    wideLen = MultiByteToWideChar(CP_ACP, 0, input, length, NULL, 0);
    if (wideLen > 0) {
        wchar_t* wideStr = (wchar_t*)malloc((wideLen + 1) * sizeof(wchar_t));
        if (!wideStr) return NULL;

        MultiByteToWideChar(CP_ACP, 0, input, length, wideStr, wideLen);
        wideStr[wideLen] = L'\0';

        int utf8Len = WideCharToMultiByte(CP_UTF8, 0, wideStr, wideLen, NULL, 0, NULL, NULL);
        if (utf8Len > 0) {
            char* utf8Str = (char*)malloc(utf8Len + 1);
            if (!utf8Str) {
                free(wideStr);
                return NULL;
            }

            WideCharToMultiByte(CP_UTF8, 0, wideStr, wideLen, utf8Str, utf8Len, NULL, NULL);
            utf8Str[utf8Len] = '\0';

            free(wideStr);
            return utf8Str;
        }
        free(wideStr);
    }

    // iconvを使用した変換を試みる
    const char* encodings[] = {"SHIFT-JIS", "EUC-JP", "ISO-2022-JP", "UTF-16LE", "UTF-16BE"};
    for (int i = 0; i < sizeof(encodings) / sizeof(encodings[0]); i++) {
        iconv_t cd = iconv_open("UTF-8", encodings[i]);
        if (cd == (iconv_t)-1) continue;

        size_t inBytesLeft = length;
        size_t outBytesLeft = length * 4; // UTF-8は最大で元の4倍のサイズになる可能性がある
        char* outBuf = (char*)malloc(outBytesLeft + 1);
        if (!outBuf) {
            iconv_close(cd);
            continue;
        }

        char* outPtr = outBuf;
        char* inPtr = (char*)input;

        if (iconv(cd, &inPtr, &inBytesLeft, &outPtr, &outBytesLeft) != (size_t)-1) {
            *outPtr = '\0';
            iconv_close(cd);
            return outBuf;
        }

        free(outBuf);
        iconv_close(cd);
    }

    // すべての変換が失敗した場合、元の文字列をASCII文字のみにフィルタリングして返す
    char* filtered = (char*)malloc(length + 1);
    if (!filtered) return NULL;

    size_t j = 0;
    for (size_t i = 0; i < length; i++) {
        if ((unsigned char)input[i] >= 32 && (unsigned char)input[i] <= 126) {
            filtered[j++] = input[i];
        }
    }
    filtered[j] = '\0';

    return filtered;
}

// テキストが有効かどうかをチェックする関数（空白や単体の文字は無効）
bool isValidTextEvent(const char* text) {
    if (!text || !*text) return false;  // NULLまたは空文字列

    // 空白のみかチェック
    bool onlyWhitespace = true;
    bool hasControlChars = false;
    bool hasVisibleChars = false;
    int charCount = 0;

    for (const char* p = text; *p; p++) {
        unsigned char c = (unsigned char)*p;

        // 制御文字をチェック（ASCII 0-31, 127）
        if (c < 32 || c == 127) {
            hasControlChars = true;
        }

        // 空白以外の文字があるかチェック
        if (!isspace(c)) {
            onlyWhitespace = false;
        }

        // 表示可能な文字があるかチェック
        if (c >= 32 && c != 127) {
            hasVisibleChars = true;
        }

        charCount++;
    }

    // 空白のみの場合は無効
    if (onlyWhitespace) {
        printf("Invalid text: only whitespace\n");
        return false;
    }

    // 制御文字のみの短い文字列は無効
    if (hasControlChars && charCount <= 2) {
        printf("Invalid text: only control chars and too short\n");
        return false;
    }

    // 表示可能な文字がない場合は無効
    if (!hasVisibleChars) {
        printf("Invalid text: no visible chars\n");
        return false;
    }

    // 文字数が1文字以下の場合は無効
    if (charCount <= 1) {
        printf("Invalid text: too short (only %d chars)\n", charCount);
        return false;
    }

    return true;
}

// 現在の時間に対応するテキストイベントを取得する関数
TextEvent* getCurrentTextEvent(MidiFile* midiFile, float currentTime, TextEventType type) {
    if (!midiFile || !midiFile->textEvents.events) return NULL;

    TextEvent* currentEvent = NULL;
    float closestTime = -1.0f;
    static TextEventType lastType = EVENT_UNKNOWN;  // 不明なイベントタイプで初期化
    static float lastTime = -1.0f;
    static TextEvent* lastEvent = NULL;

    // 同じタイプと時間で前回と同じイベントを返す（パフォーマンス最適化）
    if (lastType == type && lastTime == currentTime && lastEvent != NULL) {
        return lastEvent;
    }

    // 指定されたタイプのイベントで、現在の時間に最も近いものを探す
    for (uint32_t i = 0; i < midiFile->textEvents.count; i++) {
        TextEvent* event = &midiFile->textEvents.events[i];

        // タイプが一致するかチェック
        if (event->type != type) continue;

        // テキストがNULLの場合のみスキップ（安全性のため）
        if (!event->text) {
            continue;
        }

        // 空白や無効なテキストも含めて処理する

        // 現在の時間より前のイベントで、最も近いものを選択
        if (event->time <= currentTime && (closestTime < 0 || event->time > closestTime)) {
            closestTime = event->time;
            currentEvent = event;
        }
    }

    // 結果をキャッシュ
    lastType = type;
    lastTime = currentTime;
    lastEvent = currentEvent;

    return currentEvent;
}

// テキストイベントをスキャンする関数
void scanTextEvents(MidiFile* midiFile) {
    if (!midiFile || !midiFile->tracks) return;

    // テキストイベントリストを初期化
    initTextEventList(&midiFile->textEvents);

    // 各トラックをスキャン
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        uint32_t pos = 0;
        uint32_t tick = 0;
        uint8_t runStatus = 0;

        while (pos < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (pos >= track->length) break;
                byte = track->data[pos++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            tick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (pos >= track->length) break;

            if (track->data[pos] & 0x80) {
                statusByte = track->data[pos++];
                runStatus = statusByte;
            } else {
                statusByte = runStatus;
            }

            // メタイベント処理
            if (statusByte == 0xFF) {
                if (pos >= track->length) break;
                uint8_t metaType = track->data[pos++];

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (pos >= track->length) break;
                    byte = track->data[pos++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (pos + length > track->length) break;

                // テキスト関連のメタイベント処理
                if (metaType == 0x01 || metaType == 0x05 || metaType == 0x06) {
                    // テキストデータを取得
                    char* rawText = (char*)malloc(length + 1);
                    if (!rawText) {
                        fprintf(stderr, "Error: Memory allocation failed for text event\n");
                        pos += length;
                        continue;
                    }

                    memcpy(rawText, &track->data[pos], length);
                    rawText[length] = '\0';

                    // UTF-8に変換
                    char* utf8Text = convertToUTF8(rawText, length);

                    // 変換に失敗した場合は元のテキストをそのまま使用
                    if (!utf8Text) {
                        fprintf(stderr, "Warning: Failed to convert text to UTF-8, using original text\n");
                        // 元のテキストをASCII文字のみにフィルタリング
                        utf8Text = (char*)malloc(length + 1);
                        if (!utf8Text) {
                            fprintf(stderr, "Error: Memory allocation failed for filtered text\n");
                            free(rawText);
                            pos += length;
                            continue;
                        }

                        size_t j = 0;
                        for (size_t i = 0; i < length; i++) {
                            if ((unsigned char)rawText[i] >= 32 && (unsigned char)rawText[i] <= 126) {
                                utf8Text[j++] = rawText[i];
                            }
                        }
                        utf8Text[j] = '\0';
                    }

                    // 空白や無効なテキストでもスキップしない
                    // デバッグ出力は削除

                    free(rawText);

                    // イベントタイプを決定
                    TextEventType eventType;
                    if (metaType == 0x01) {
                        eventType = EVENT_TEXT;
                    } else if (metaType == 0x05) {
                        eventType = EVENT_LYRIC;
                    } else { // metaType == 0x06
                        eventType = EVENT_MARKER;
                    }

                    // ティックを時間（秒）に変換
                    float eventTime = tickToSeconds(tick, midiFile->header.division);

                    // テキストイベントを追加
                    addTextEvent(&midiFile->textEvents, tick, eventType, utf8Text, eventTime);
                    free(utf8Text);
                }

                pos += length;
            }
            // データスキップ
            else if (statusByte < 0xF0) {
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) {
                    pos += 1;
                } else {
                    pos += 2;
                }
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                uint32_t length = 0;
                do {
                    if (pos >= track->length) break;
                    byte = track->data[pos++];
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                pos += length;
            }
        }
    }
}