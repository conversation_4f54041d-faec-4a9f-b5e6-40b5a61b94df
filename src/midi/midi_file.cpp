#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>

// プラットフォーム別のヘッダー
#ifdef _WIN32
#include <windows.h>
#elif defined(__linux__)
#include <locale.h>
#include <wchar.h>
#include <iconv.h>
#endif

#include "../../include/app_state.h"
#include "../../include/midi_utils.h"

// MIDIファイルのヘッダーとトラック情報だけを読み込む関数
MidiFile* loadMidiFileHeaderAndTracks(const char* filename) {
#ifdef _WIN32
    // Windows: UTF-8パスをUTF-16に変換して_wfopenを使う
    wchar_t wfilename[MAX_PATH_LENGTH];
    MultiByteToWideChar(CP_UTF8, 0, filename, -1, wfilename, MAX_PATH_LENGTH);
    FILE* file = _wfopen(wfilename, L"rb");
#else
    FILE* file = fopen(filename, "rb");
#endif
    if (!file) {
        fprintf(stderr, "Error: Could not open file %s\n", filename);
        return NULL;
    }

    // ファイルサイズを取得
    fseek(file, 0, SEEK_END);
    long fileSize = ftell(file);
    fseek(file, 0, SEEK_SET);

    // MIDIファイル構造体を確保
    MidiFile* midiFile = (MidiFile*)malloc(sizeof(MidiFile));
    if (!midiFile) {
        fprintf(stderr, "Error: Memory allocation failed\n");
        fclose(file);
        return NULL;
    }
    memset(midiFile, 0, sizeof(MidiFile));

    // ヘッダーチャンクを読み込む
    char chunkId[5] = {0};
    uint32_t chunkSize;

    if (fread(chunkId, 1, 4, file) != 4 ||
        fread(&chunkSize, 4, 1, file) != 1) {
        fprintf(stderr, "Error: Failed to read chunk header\n");
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // エンディアン変換（ビッグエンディアン→リトルエンディアン）
    chunkSize = ((chunkSize & 0xFF) << 24) | ((chunkSize & 0xFF00) << 8) |
                ((chunkSize & 0xFF0000) >> 8) | ((chunkSize & 0xFF000000) >> 24);

    if (strncmp(chunkId, "MThd", 4) != 0) {
        fprintf(stderr, "Error: Not a valid MIDI file (invalid header signature)\n");
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // ヘッダーデータを読み込む
    uint16_t format, tracks, division;
    if (fread(&format, 2, 1, file) != 1 ||
        fread(&tracks, 2, 1, file) != 1 ||
        fread(&division, 2, 1, file) != 1) {
        fprintf(stderr, "Error: Failed to read MIDI header data\n");
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // エンディアン変換
    format = ((format & 0xFF) << 8) | ((format & 0xFF00) >> 8);
    tracks = ((tracks & 0xFF) << 8) | ((tracks & 0xFF00) >> 8);
    division = ((division & 0xFF) << 8) | ((division & 0xFF00) >> 8);

    midiFile->header.format = format;
    midiFile->header.tracks = tracks;
    midiFile->header.division = division;

    printf("MIDI Format: %d, Tracks: %d, Division: %d\n", format, tracks, division);

    // トラック配列を確保
    midiFile->tracks = (MidiTrack*)malloc(sizeof(MidiTrack) * tracks);
    if (!midiFile->tracks) {
        fprintf(stderr, "Error: Memory allocation failed for tracks\n");
        free(midiFile);
        fclose(file);
        return NULL;
    }
    memset(midiFile->tracks, 0, sizeof(MidiTrack) * tracks);

    // 各トラックを読み込む
    for (int i = 0; i < tracks; i++) {
        if (fread(chunkId, 1, 4, file) != 4 ||
            fread(&chunkSize, 4, 1, file) != 1) {
            fprintf(stderr, "Error: Failed to read track chunk header\n");
            // クリーンアップ
            for (int j = 0; j < i; j++) {
                free(midiFile->tracks[j].data);
            }
            free(midiFile->tracks);
            free(midiFile);
            fclose(file);
            return NULL;
        }

        // エンディアン変換
        chunkSize = ((chunkSize & 0xFF) << 24) | ((chunkSize & 0xFF00) << 8) |
                    ((chunkSize & 0xFF0000) >> 8) | ((chunkSize & 0xFF000000) >> 24);

        if (strncmp(chunkId, "MTrk", 4) != 0) {
            fprintf(stderr, "Error: Invalid track chunk signature\n");
            // クリーンアップ
            for (int j = 0; j < i; j++) {
                free(midiFile->tracks[j].data);
            }
            free(midiFile->tracks);
            free(midiFile);
            fclose(file);
            return NULL;
        }

        // トラックデータを読み込む
        midiFile->tracks[i].data = (uint8_t*)malloc(chunkSize);
        if (!midiFile->tracks[i].data) {
            fprintf(stderr, "Error: Memory allocation failed for track data\n");
            // クリーンアップ
            for (int j = 0; j < i; j++) {
                free(midiFile->tracks[j].data);
            }
            free(midiFile->tracks);
            free(midiFile);
            fclose(file);
            return NULL;
        }

        if (fread(midiFile->tracks[i].data, 1, chunkSize, file) != chunkSize) {
            fprintf(stderr, "Error: Failed to read track data\n");
            // クリーンアップ
            for (int j = 0; j <= i; j++) {
                free(midiFile->tracks[j].data);
            }
            free(midiFile->tracks);
            free(midiFile);
            fclose(file);
            return NULL;
        }

        midiFile->tracks[i].length = chunkSize;
        midiFile->tracks[i].position = 0;
        midiFile->tracks[i].ended = false;
    }

    fclose(file);

    // この時点ではトラック情報だけを読み込んだ状態
    printf("MIDI file header and tracks loaded successfully\n");

    return midiFile;
}

// トラック情報を処理する関数
void processMidiFileTracks(MidiFile* midiFile) {
    if (!midiFile) return;

    // MIDIファイルの解析（ノートデータの収集はスキップ）
    analyzeMidiFile(midiFile);

    printf("MIDI tracks processing completed\n");
}

// MIDIファイルをロードする関数
MidiFile* loadMidiFile(const char* filename) {
    // 最初にヘッダーとトラック情報だけを読み込む
    MidiFile* midiFile = loadMidiFileHeaderAndTracks(filename);
    if (!midiFile) {
        return NULL;
    }

    // トラック情報を処理する
    processMidiFileTracks(midiFile);

    return midiFile;
}

// 可変長数値を読み取る関数
uint32_t readVariableLengthValue(uint8_t *data, uint32_t *position, uint32_t maxLength) {
    uint32_t value = 0;
    uint8_t byte;

    do {
        if (*position >= maxLength) {
            return 0;
        }

        byte = data[(*position)++];
        value = (value << 7) | (byte & 0x7F);
    } while (byte & 0x80);

    return value;
}

// MIDIファイルを解放する関数
void freeMidiFile(MidiFile* midiFile) {
    if (!midiFile) return;

    if (midiFile->tracks) {
        for (int i = 0; i < midiFile->header.tracks; i++) {
            if (midiFile->tracks[i].data) {
                free(midiFile->tracks[i].data);
            }
        }
        free(midiFile->tracks);
    }

    // テキストイベントリストを解放
    freeTextEventList(&midiFile->textEvents);

    free(midiFile);
}
