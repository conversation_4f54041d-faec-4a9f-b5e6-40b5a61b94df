#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <math.h>
#include "../../include/midi_utils.h"

// グローバル変数
TempoMap tempoMap = {NULL, 0, 0};

// テンポマップを初期化する関数
void initTempoMap() {
    if (tempoMap.events) {
        free(tempoMap.events);
    }

    tempoMap.events = (TempoEvent*)malloc(sizeof(TempoEvent) * 10);
    tempoMap.count = 1;  // デフォルトテンポを含む
    tempoMap.capacity = 10;

    // デフォルトテンポ: 120 BPM (500,000マイクロ秒/四分音符)
    tempoMap.events[0].tick = 0;
    tempoMap.events[0].tempo = 500000;
}

// テンポイベントを比較する関数（ソート用）
static int compareTempoEvents(const void *a, const void *b) {
    const TempoEvent *tempoA = (const TempoEvent *)a;
    const TempoEvent *tempoB = (const TempoEvent *)b;

    if (tempoA->tick < tempoB->tick) return -1;
    if (tempoA->tick > tempoB->tick) return 1;
    return 0;
}

// テンポ変更を追加する関数（最適化版）
void addTempoChange(uint32_t tick, uint32_t tempo) {
    // 無効なテンポ値をチェック
    if (tempo == 0) {
        tempo = 500000; // デフォルトテンポを使用
    }

    // テンポイベントの間引き処理を削除（すべてのテンポイベントを保持）

    // 同じティック位置のテンポ変更があれば上書き
    for (uint32_t i = 0; i < tempoMap.count; i++) {
        if (tempoMap.events[i].tick == tick) {
            tempoMap.events[i].tempo = tempo;
            return;
        }
    }

    // 配列の拡張が必要な場合
    if (tempoMap.count >= tempoMap.capacity) {
        // 通常の拡張（テンポイベントの間引きを行わない）
        tempoMap.capacity *= 2;
        tempoMap.events = (TempoEvent*)realloc(tempoMap.events, sizeof(TempoEvent) * tempoMap.capacity);
        if (!tempoMap.events) {
            fprintf(stderr, "Error: Failed to reallocate memory for tempo map\n");
            return;
        }
    }

    // 新しいテンポ変更を追加
    tempoMap.events[tempoMap.count].tick = tick;
    tempoMap.events[tempoMap.count].tempo = tempo;
    tempoMap.count++;

    // テンポマップをソート（ティック順）- クイックソートを使用（O(n log n)）
    qsort(tempoMap.events, tempoMap.count, sizeof(TempoEvent), compareTempoEvents);
}

// キャッシュ変数（最適化用）
static uint32_t lastQueryTick = 0;
static float lastQueryResult = 0.0f;
static bool cacheInitialized = false;

// ティックを時間（秒）に変換する関数（改良版）
float tickToSeconds(uint32_t tick, uint16_t division) {
    // キャッシュヒット - 同じティックの場合は前回の結果を返す
    if (cacheInitialized && tick == lastQueryTick) {
        return lastQueryResult;
    }

    // キャッシュミス - 新しい計算が必要
    float seconds = 0.0f;
    uint32_t lastTick = 0;
    uint32_t tempo = 500000;  // デフォルトテンポ

    // テンポマップが空の場合はデフォルトテンポで計算
    if (tempoMap.count == 0) {
        float result = (float)tick * tempo / (division * 1000000.0f);

        // 結果をキャッシュ
        lastQueryTick = tick;
        lastQueryResult = result;
        cacheInitialized = true;

        return result;
    }

    // テンポマップは既にソート済みと仮定（addTempoChangeでソート済み）

    // 二分探索で目標ティック以下の最大のテンポイベントを見つける
    int lastValidIndex = -1;

    // テンポイベントが多い場合は二分探索を使用
    if (tempoMap.count > 100) {
        int left = 0;
        int right = tempoMap.count - 1;

        while (left <= right) {
            int mid = left + (right - left) / 2;

            if (tempoMap.events[mid].tick <= tick) {
                lastValidIndex = mid;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
    } else {
        // テンポイベントが少ない場合は線形探索
        for (int i = tempoMap.count - 1; i >= 0; i--) {
            if (tempoMap.events[i].tick <= tick) {
                lastValidIndex = i;
                break;
            }
        }
    }

    // 最初のテンポイベントより前の場合
    if (lastValidIndex == -1) {
        // デフォルトテンポで計算
        seconds = (float)tick * tempo / (division * 1000000.0f);
    } else {
        // 各テンポ区間の時間を計算
        for (int i = 0; i <= lastValidIndex; i++) {
            uint32_t currentTempoTick = tempoMap.events[i].tick;

            // 前の区間の時間を計算
            if (i > 0) {
                uint32_t prevTempoTick = tempoMap.events[i-1].tick;
                uint32_t ticksInSegment = currentTempoTick - prevTempoTick;
                uint32_t segmentTempo = tempoMap.events[i-1].tempo;
                seconds += (float)ticksInSegment * segmentTempo / (division * 1000000.0f);
            } else if (currentTempoTick > 0) {
                // 最初のテンポイベントが0ティックでない場合、0からそのイベントまでをデフォルトテンポで計算
                seconds += (float)currentTempoTick * tempo / (division * 1000000.0f);
            }
        }

        // 最後のテンポ区間（最後のテンポ変更から目標ティックまで）
        uint32_t lastTempoTick = tempoMap.events[lastValidIndex].tick;
        uint32_t lastTempoValue = tempoMap.events[lastValidIndex].tempo;
        if (tick > lastTempoTick) {
            uint32_t ticksInLastSegment = tick - lastTempoTick;
            seconds += (float)ticksInLastSegment * lastTempoValue / (division * 1000000.0f);
        }
    }

    // 結果をキャッシュ
    lastQueryTick = tick;
    lastQueryResult = seconds;
    cacheInitialized = true;

    return seconds;
}

// テンポイベントをスキャンする関数
void scanTempoEvents(MidiFile* midiFile) {
    if (!midiFile || !midiFile->tracks) return;

    // 各トラックを個別にスキャン
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        // 元のヘッドポインタを保存
        uint32_t originalPosition = track->position;
        uint32_t currentTick = 0;
        uint8_t runningStatus = 0;
        bool trackEnded = false;

        // トラックをスキャンしてテンポ変更イベントを探す
        while (!trackEnded && track->position < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = readVariableLengthValue(track->data, &track->position, track->length);

            // 現在のティック位置を更新
            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (track->position >= track->length) {
                trackEnded = true;
                break;
            }

            if (track->data[track->position] & 0x80) {
                statusByte = track->data[track->position++];
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // メタイベント処理
            if (statusByte == 0xFF) {
                if (track->position >= track->length) {
                    trackEnded = true;
                    break;
                }

                uint8_t metaType = track->data[track->position++];

                // 可変長バイト長を読み取る
                uint32_t length = readVariableLengthValue(track->data, &track->position, track->length);

                if (track->position + length > track->length) {
                    trackEnded = true;
                    break;
                }

                // テンポ変更イベント (0x51)
                if (metaType == 0x51 && length >= 3) {
                    // テンポ値を読み取る (24-bit値)
                    uint32_t tempo = ((uint32_t)track->data[track->position] << 16) |
                                     ((uint32_t)track->data[track->position + 1] << 8) |
                                     ((uint32_t)track->data[track->position + 2]);

                    if (tempo > 0) {
                        float bpm = 60000000.0f / (float)tempo;
                        // printf("Found tempo event at tick %u: %u (%.2f BPM)\n", currentTick, tempo, bpm);

                        // テンポマップにテンポ変更を記録
                        addTempoChange(currentTick, tempo);
                    }
                }

                // メタイベントのデータ部をスキップ
                track->position += length;
            }
            // SysExイベント
            else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // 長さを読み取る
                uint32_t length = readVariableLengthValue(track->data, &track->position, track->length);

                if (track->position + length > track->length) {
                    trackEnded = true;
                    break;
                }

                // データをスキップ
                track->position += length;
            }
            // チャンネルメッセージ
            else if (statusByte < 0xF0) {
                int msgType = statusByte & 0xF0;

                // データサイズに基づきスキップ
                if (msgType == 0xC0 || msgType == 0xD0) {
                    track->position += 1; // 1バイトデータ
                } else {
                    track->position += 2; // 2バイトデータ
                }

                if (track->position > track->length) {
                    trackEnded = true;
                    break;
                }
            }
        }

        // ヘッドポインタを元の位置に戻す
        track->position = originalPosition;
    }
}
