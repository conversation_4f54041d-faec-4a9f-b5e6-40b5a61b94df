#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../../include/note_processor.h"
#include "../../include/app_state.h"
#include "../../include/midi_utils.h" // テンポマップとtickToSeconds関数用

// ノート時間イベントを時間順にソートするための比較関数
int compareNoteTimeEvents(const void *a, const void *b) {
    const NoteTimeEvent *noteA = (const NoteTimeEvent *)a;
    const NoteTimeEvent *noteB = (const NoteTimeEvent *)b;

    if (noteA->time < noteB->time) return -1;
    if (noteA->time > noteB->time) return 1;
    return 0;
}

// 時間（秒）からティックを逆算する関数
// 二分探索を使用して効率化
uint32_t secondsToTick(float seconds, uint16_t division) {
    // 負の時間は0として扱う
    if (seconds < 0.0f) {
        seconds = 0.0f;
    }

    // テンポマップが空の場合はデフォルトテンポで計算
    if (tempoMap.count == 0) {
        uint32_t tempo = 500000; // デフォルトテンポ
        return (uint32_t)(seconds * division * 1000000.0f / tempo);
    }

    // 二分探索で指定した時間以下の最大のテンポイベントを見つける
    int left = 0;
    int right = tempoMap.count - 1;
    int lastValidIndex = -1;

    while (left <= right) {
        int mid = left + (right - left) / 2;
        float eventTime = tickToSeconds(tempoMap.events[mid].tick, division);

        if (eventTime <= seconds) {
            lastValidIndex = mid;
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    // 最初のテンポイベントより前の場合
    if (lastValidIndex == -1) {
        // デフォルトテンポで計算
        uint32_t tempo = 500000; // デフォルトテンポ
        return (uint32_t)(seconds * division * 1000000.0f / tempo);
    }

    // 最後に見つかったテンポイベントを使用
    uint32_t lastTick = tempoMap.events[lastValidIndex].tick;
    uint32_t tempo = tempoMap.events[lastValidIndex].tempo;
    float eventTime = tickToSeconds(lastTick, division);
    float timeSinceTempoChange = seconds - eventTime;

    // 経過時間をティックに変換して加算
    // オーバーフローを防ぐために64ビット整数を使用
    uint64_t tickOffset = (uint64_t)(timeSinceTempoChange * division * 1000000.0f / tempo);

    // 結果が32ビットの範囲を超える場合はクランプ
    if (lastTick > UINT32_MAX - tickOffset) {
        return UINT32_MAX;
    }

    return lastTick + (uint32_t)tickOffset;
}

// 指定した時間以下のノートオンイベント数を取得する関数
// このカウンターはノートオンイベントのみをカウントし、ノートオフイベントでは減少しない
uint32_t countNotesUpToTime(float targetTime) {
    if (appState.noteTimeEventCount == 0 || !appState.noteTimeEvents) return 0;

    // 二分探索で targetTime より大きい最初のノートを見つける
    int left = 0;
    int right = appState.noteTimeEventCount - 1;

    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (appState.noteTimeEvents[mid].time <= targetTime) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    // left は targetTime より大きい最初のノートのインデックス
    // つまり、targetTime 以下のノートの数は left
    uint32_t count = left;

    // 無効化されたノートの補正を行う
    // piano_roll.cで管理されている累積ノートカウントの補正値を取得
    extern uint32_t getPianoRollCumulativeNoteCountOffset(void);

    // 無効化されたノートの数を加算
    count += getPianoRollCumulativeNoteCountOffset();

    // 総ノート数を超えないようにする
    if (appState.midiFile && count > appState.midiFile->totalNotes) {
        count = appState.midiFile->totalNotes;
    }

    return count;
}

// 指定した時間ウィンドウ内のノート数を取得する関数
uint32_t countNotesInTimeWindow(float startTime, float endTime) {
    if (appState.noteTimeEventCount == 0 || !appState.noteTimeEvents) return 0;

    // 二分探索を使用して startTime 以上のノートと endTime より大きいノートのインデックスを見つける
    uint32_t startIndex = 0;
    uint32_t endIndex = 0;

    // startTime 以上の最初のノートを見つける
    int left = 0;
    int right = appState.noteTimeEventCount - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (appState.noteTimeEvents[mid].time < startTime) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    startIndex = left;

    // endTime より大きい最初のノートを見つける
    left = 0;
    right = appState.noteTimeEventCount - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (appState.noteTimeEvents[mid].time <= endTime) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    endIndex = left;

    // ウィンドウ内のノート数を計算
    return (endIndex > startIndex) ? (endIndex - startIndex) : 0;
}

// 指定した時間での同時発音数を計算する関数
uint16_t calculatePolyphonyAtTime(float currentTime) {
    if (appState.noteTimeEventCount == 0 || !appState.noteTimeEvents) return 0;

    uint16_t polyphony = 0;

    // 二分探索で currentTime より大きい最初のノートを見つける
    int upperBound = 0;
    {
        int left = 0;
        int right = appState.noteTimeEventCount - 1;
        while (left <= right) {
            int mid = left + (right - left) / 2;
            if (appState.noteTimeEvents[mid].time <= currentTime) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
        upperBound = left;
    }

    // currentTime 以前に開始したノートのみをチェック
    for (int i = 0; i < upperBound; i++) {
        float noteStartTime = appState.noteTimeEvents[i].time;
        float noteEndTime = noteStartTime + appState.noteTimeEvents[i].duration;

        // ノートが現在時刻に発音中かどうかチェック
        if (noteEndTime >= currentTime) {
            polyphony++;
        }
    }

    return polyphony;
}

// ノートイベントを収集する関数
bool collectNoteEvents(MidiFile* midiFile, NoteTimeEvent** outEvents, uint32_t* outCount) {
    // 進捗バー表示用の変数
    int totalTracks = midiFile->header.tracks;
    int processedTracks = 0;
    int progressBarWidth = 50; // 進捗バーの幅
    int lastProgressPercent = 0;

    // 一時的なノートイベント配列の初期容量を設定
    uint32_t tempCapacity;
    if (midiFile->totalNotes > 0) {
        // midiFile->totalNotes に基づいて初期容量を推定
        unsigned long long estimated_cap_ull = (unsigned long long)midiFile->totalNotes * 12 / 10 + 2048; // 20%増し + 2048バッファ
        if (MAX_NOTE_EVENTS > 0 && estimated_cap_ull > MAX_NOTE_EVENTS) {
            tempCapacity = MAX_NOTE_EVENTS;
        } else if (estimated_cap_ull > 0xFFFFFFFFUL) { // uint32_t の最大値を超える場合
            tempCapacity = 0xFFFFFFFFUL; // Clamp to max uint32_t
        } else {
            tempCapacity = (uint32_t)estimated_cap_ull;
        }
        // 最低容量保証
        if (tempCapacity < 1024 && MAX_NOTE_EVENTS > 1024) tempCapacity = 1024;
        else if (tempCapacity == 0 && MAX_NOTE_EVENTS == 0) tempCapacity = 1024;
    } else {
        // midiFile->totalNotes が0または信頼できない場合のフォールバック
        tempCapacity = 100000; // デフォルト初期容量
    }
    if (tempCapacity == 0 && MAX_NOTE_EVENTS > 0) tempCapacity = (MAX_NOTE_EVENTS < 100000) ? MAX_NOTE_EVENTS : 100000;
    else if (tempCapacity == 0) tempCapacity = 100000;

    // 一時的なノートイベント配列を確保
    TempNoteEvent* tempEvents = (TempNoteEvent*)malloc(tempCapacity * sizeof(TempNoteEvent));
    uint32_t tempCount = 0;

    if (!tempEvents) {
        fprintf(stderr, "Failed to allocate memory for temporary note events (initial capacity: %u)\n", tempCapacity);
        return false;
    }

    // 各トラックをスキャンしてノートイベントを収集
    for (int i = 0; i < midiFile->header.tracks; i++) {
        MidiTrack* track = &midiFile->tracks[i];
        if (track->ended) continue;

        uint32_t pos = 0;
        uint32_t tick = 0;
        uint8_t runStatus = 0;

        // ノートONイベントとノートOFFイベントを対応付けるための一時配列
        int noteOnIndices[16 * 128];
        memset(noteOnIndices, -1, sizeof(noteOnIndices)); // -1で初期化

        while (pos < track->length) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (pos >= track->length) break;
                byte = track->data[pos++];
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);
            if (pos > track->length && (byte & 0x80)) break; // 安全性のためデルタタイム読み取り後にもチェック

            tick += deltaTime;

            // ティックを時間（秒）に変換
            // 最適化: 時間変換はノートONとノートOFFイベントの場合のみ行う
            float tickTime = 0.0f;

            // ステータスバイト処理
            uint8_t statusByte;
            if (pos >= track->length) break;

            if (track->data[pos] & 0x80) {
                statusByte = track->data[pos++];
                runStatus = statusByte;
            } else {
                statusByte = runStatus;
            }
            if (pos > track->length && statusByte < 0xF0) { // ランニングステータスの場合、データがない可能性がある
                if (!((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0)) break; // 1バイトメッセージ以外
            }

            // ノートONイベント (0x90) でベロシティが0より大きい場合
            if ((statusByte & 0xF0) == 0x90) {
                if (pos + 1 < track->length) {
                    uint8_t note = track->data[pos++];
                    uint8_t velocity = track->data[pos++];
                    uint8_t channel = statusByte & 0x0F;
                    int noteIndex = channel * 128 + note;

                    if (velocity > 0) {
                        // ノートONイベントの場合のみ時間変換を行う
                        tickTime = tickToSeconds(tick, midiFile->header.division);

                        // 容量チェックと拡張ロジック
                        if (tempCount >= tempCapacity) {
                            if (MAX_NOTE_EVENTS > 0 && tempCapacity >= MAX_NOTE_EVENTS) {
                                fprintf(stderr, "Error: Reached MAX_NOTE_EVENTS limit (%u) while collecting notes. Cannot expand further.\n", MAX_NOTE_EVENTS);
                                free(tempEvents);
                                return false;
                            }
                            unsigned long long newCapacity_ull = (unsigned long long)tempCapacity * 2; // 2倍に増やす
                            if (newCapacity_ull == 0) newCapacity_ull = tempCapacity + 2048; // 0からの増加対策

                            uint32_t newCapacity;
                            if (MAX_NOTE_EVENTS > 0 && newCapacity_ull > MAX_NOTE_EVENTS) {
                                newCapacity = MAX_NOTE_EVENTS;
                            } else if (newCapacity_ull > 0xFFFFFFFFUL) {
                                newCapacity = 0xFFFFFFFFUL; // uint32_t の最大値にクランプ
                                fprintf(stderr, "Warning: tempEvents new capacity calculation overflowed.\n");
                            } else {
                                newCapacity = (uint32_t)newCapacity_ull;
                            }

                            if (newCapacity <= tempCapacity) { // 増やせなかった場合
                                fprintf(stderr, "Error: Failed to determine a valid new capacity for tempEvents (current: %u, new_calc: %llu, MAX: %u).\n",
                                        tempCapacity, newCapacity_ull, MAX_NOTE_EVENTS);
                                free(tempEvents);
                                return false;
                            }

                            TempNoteEvent* newTempEvents = (TempNoteEvent*)realloc(tempEvents, newCapacity * sizeof(TempNoteEvent));
                            if (!newTempEvents) {
                                fprintf(stderr, "Failed to reallocate memory for temporary note events (new capacity: %u)\n", newCapacity);
                                free(tempEvents);
                                return false;
                            }
                            tempEvents = newTempEvents;
                            tempCapacity = newCapacity;
                        }

                        // ノートONイベントを記録
                        tempEvents[tempCount].onTime = tickTime;
                        tempEvents[tempCount].offTime = tickTime + 0.2f; // デフォルト長さ
                        tempEvents[tempCount].onTick = tick;
                        tempEvents[tempCount].offTick = tick + (uint32_t)(0.2f * midiFile->header.division * 1000000.0f / 500000); // デフォルト長さのティック
                        tempEvents[tempCount].velocity = velocity;
                        tempEvents[tempCount].channel = channel;
                        tempEvents[tempCount].note = note;
                        tempEvents[tempCount].track = i; // トラック番号を設定
                        tempEvents[tempCount].hasOff = false;

                        // 前のノートONが終了していない場合は強制終了
                        if (noteOnIndices[noteIndex] >= 0) {
                            int prevIndex = noteOnIndices[noteIndex];
                            if (prevIndex < tempCount && !tempEvents[prevIndex].hasOff) { // prevIndex の妥当性チェック追加
                                tempEvents[prevIndex].offTime = tickTime;
                                tempEvents[prevIndex].offTick = tick;
                                tempEvents[prevIndex].hasOff = true;
                            }
                        }

                        // 新しいノートONのインデックスを記録
                        noteOnIndices[noteIndex] = tempCount;
                        tempCount++;
                    } else {
                        // ベロシティ0のノートONはノートOFFとして扱う
                        if (noteOnIndices[noteIndex] >= 0) {
                            // ノートOFFイベントの場合のみ時間変換を行う
                            tickTime = tickToSeconds(tick, midiFile->header.division);

                            int onIndex = noteOnIndices[noteIndex];
                            if (onIndex < tempCount && !tempEvents[onIndex].hasOff) { // onIndex の妥当性チェック追加
                                tempEvents[onIndex].offTime = tickTime;
                                tempEvents[onIndex].offTick = tick;
                                tempEvents[onIndex].hasOff = true;
                            }
                            noteOnIndices[noteIndex] = -1; // リセット
                        }
                    }
                } else {
                    // データが不足している場合はスキップ
                    pos = track->length; // ループを抜けるようにする
                }
            }
            // ノートOFFイベント (0x80)
            else if ((statusByte & 0xF0) == 0x80) {
                if (pos + 1 < track->length) {
                    uint8_t note = track->data[pos++];
                    uint8_t velocity = track->data[pos++]; // velocityは使わないが読み飛ばす
                    uint8_t channel = statusByte & 0x0F;
                    int noteIndex = channel * 128 + note;

                    // 対応するノートONを探す
                    if (noteOnIndices[noteIndex] >= 0) {
                        // ノートOFFイベントの場合のみ時間変換を行う
                        tickTime = tickToSeconds(tick, midiFile->header.division);

                        int onIndex = noteOnIndices[noteIndex];
                        if (onIndex < tempCount && !tempEvents[onIndex].hasOff) { // onIndex の妥当性チェック追加
                            tempEvents[onIndex].offTime = tickTime;
                            tempEvents[onIndex].offTick = tick;
                            tempEvents[onIndex].hasOff = true;
                        }
                        noteOnIndices[noteIndex] = -1; // リセット
                    }
                } else {
                    // データが不足している場合はスキップ
                    pos = track->length; // ループを抜けるようにする
                }
            }
            // その他のイベント
            else if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) { // 1バイトデータ
                    if (pos < track->length) pos++; else break;
                } else { // 2バイトデータ
                    if (pos + 1 < track->length) pos += 2; else break;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (pos >= track->length) break; // Meta type
                pos++;

                uint32_t length = 0;
                uint8_t meta_byte;
                do { // Varlen length
                    if (pos >= track->length) break;
                    meta_byte = track->data[pos++];
                    length = (length << 7) | (meta_byte & 0x7F);
                } while (meta_byte & 0x80);
                if (pos > track->length && (meta_byte & 0x80)) break;

                if (pos + length <= track->length) pos += length; else break;
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                uint8_t sysex_byte;
                do { // Varlen length
                    if (pos >= track->length) break;
                    sysex_byte = track->data[pos++];
                    length = (length << 7) | (sysex_byte & 0x7F);
                } while (sysex_byte & 0x80);
                if (pos > track->length && (sysex_byte & 0x80)) break;

                if (pos + length <= track->length) pos += length; else break;
            }
        }

        // トラック終了時に、まだ終了していないノートを処理
        for (int j = 0; j < 16 * 128; j++) {
            if (noteOnIndices[j] >= 0) {
                int onIndex = noteOnIndices[j];
                if (onIndex < tempCount && !tempEvents[onIndex].hasOff) {
                    // hasOff のみ true にする
                    tempEvents[onIndex].hasOff = true;

                    // 終了時間をトラックの最後の時間に設定
                    float endTime = tickToSeconds(tick, midiFile->header.division);
                    if (endTime > tempEvents[onIndex].onTime) {
                        tempEvents[onIndex].offTime = endTime;
                        tempEvents[onIndex].offTick = tick;
                    } else {
                        // 異常な場合はデフォルト長さを使用
                        tempEvents[onIndex].offTime = tempEvents[onIndex].onTime + 0.2f;
                        tempEvents[onIndex].offTick = tempEvents[onIndex].onTick + (uint32_t)(0.2f * midiFile->header.division * 1000000.0f / 500000);
                    }
                }
            }
        }

        // 進捗バーを更新
        processedTracks++;
        int progressPercent = (totalTracks > 0) ? (processedTracks * 100) / totalTracks : 100;

        // 進捗が変わった場合のみ表示を更新
        if (progressPercent != lastProgressPercent || processedTracks == totalTracks) {
            // 進捗バーを表示
            printf("\rCollecting note events: [");
            int barPos = progressBarWidth * progressPercent / 100;

            // 進捗バーの描画
            for (int k = 0; k < progressBarWidth; k++) {
                if (k < barPos) printf("=");
                else if (k == barPos && progressPercent < 100) printf(">");
                else printf(" ");
            }

            printf("] %d%%", progressPercent);
            fflush(stdout); // バッファをフラッシュして即座に表示

            lastProgressPercent = progressPercent;
        }
    }

    // 進捗バーの後に改行を入れる
    printf("\n");
    printf("Collected %u temporary note events\n", tempCount);

    // 一時的なノートイベントを実際のノート時間イベントに変換
    printf("Converting note events to final format...\n");
    if (tempCount > 0) {
        *outEvents = (NoteTimeEvent*)malloc(tempCount * sizeof(NoteTimeEvent));
        if (!*outEvents) {
            fprintf(stderr, "Failed to allocate memory for note time events\n");
            free(tempEvents);
            return false;
        }

        // データをコピー（進捗表示付き）
        uint32_t lastProgressPercent = 0;
        for (uint32_t i = 0; i < tempCount; i++) {
            (*outEvents)[i].time = tempEvents[i].onTime;
            (*outEvents)[i].duration = tempEvents[i].offTime - tempEvents[i].onTime;
            if ((*outEvents)[i].duration < 0) (*outEvents)[i].duration = 0; // 念のため

            // ティック情報を直接コピー（重い逆変換処理を削除）
            (*outEvents)[i].tick = tempEvents[i].onTick;

            // tickDurationが0にならないようにする
            if (tempEvents[i].offTick > tempEvents[i].onTick) {
                (*outEvents)[i].tickDuration = tempEvents[i].offTick - tempEvents[i].onTick;
            } else {
                // 最小のティック長を設定（1ティック）
                (*outEvents)[i].tickDuration = 1;
            }
            (*outEvents)[i].velocity = tempEvents[i].velocity;
            (*outEvents)[i].channel = tempEvents[i].channel;
            (*outEvents)[i].note = tempEvents[i].note;
            (*outEvents)[i].track = tempEvents[i].track;

            // 大量のノートの場合は進捗表示
            if (tempCount > 100000 && i % (tempCount / 100) == 0) {
                uint32_t progressPercent = (i * 100) / tempCount;
                if (progressPercent != lastProgressPercent) {
                    printf("\rConverting note events: %u%%", progressPercent);
                    fflush(stdout);
                    lastProgressPercent = progressPercent;
                }
            }
        }
        if (tempCount > 100000) {
            printf("\rConverting note events: 100%%\n");
        }
        *outCount = tempCount;
        printf("Note event conversion completed successfully\n");
    } else {
        *outEvents = NULL;
        *outCount = 0;
        printf("No note events found\n");
    }

    // 一時的なノートイベント配列を解放
    free(tempEvents);

    return true;
}

// ノートイベントをソートする関数
void sortNoteEvents(NoteTimeEvent* events, uint32_t count) {
    if (count > 0) {
        printf("Sorting note time events...\n");
        qsort(events, count, sizeof(NoteTimeEvent), compareNoteTimeEvents);
    }
}
