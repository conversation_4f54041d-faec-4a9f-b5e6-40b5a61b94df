{"$schema": "http://json-schema.org/draft-07/schema#", "title": "HachimitsuMIDIRenderer Configuration File", "description": "Schema defining color settings for HachimitsuMIDIRenderer", "type": "object", "properties": {"colors": {"type": "object", "description": "All color settings", "properties": {"color_mode": {"type": "string", "description": "Color mode for piano roll and piano keyboard", "enum": ["channel", "track"], "default": "channel", "examples": ["channel", "track"]}, "background": {"type": "string", "description": "Application background color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#000000", "000000"]}, "grid_line": {"type": "string", "description": "Grid line color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#444444", "444444"]}, "border": {"type": "string", "description": "Border color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#808080", "808080"]}, "graph_background": {"type": "string", "description": "Graph background color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#333333", "333333"]}, "graph_progress_bar": {"type": "string", "description": "Graph progress bar color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#00CC00", "00CC00"]}, "graph_center_line": {"type": "string", "description": "Graph center line color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#808080", "808080"]}, "counter_text": {"type": "string", "description": "Counter display text color (Time, Notes, NPS, Polyphony, etc.)", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#FFFFFF", "FFFFFF"]}, "graph_text": {"type": "string", "description": "Graph text color", "pattern": "^#?[0-9a-fA-F]{6}$", "examples": ["#FFFFFF", "FFFFFF"]}, "channel": {"type": "object", "description": "MIDI channel (0-15) color settings", "properties": {"ch0": {"type": "string", "description": "Channel 0 color (red family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch1": {"type": "string", "description": "Channel 1 color (green family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch2": {"type": "string", "description": "Channel 2 color (blue family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch3": {"type": "string", "description": "Channel 3 color (yellow family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch4": {"type": "string", "description": "Channel 4 color (magenta family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch5": {"type": "string", "description": "Channel 5 color (cyan family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch6": {"type": "string", "description": "Channel 6 color (orange family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch7": {"type": "string", "description": "Channel 7 color (purple family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch8": {"type": "string", "description": "Channel 8 color (light green family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch9": {"type": "string", "description": "Channel 9 color (light blue family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch10": {"type": "string", "description": "Channel 10 color (pink family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch11": {"type": "string", "description": "Channel 11 color (lime family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch12": {"type": "string", "description": "Channel 12 color (lavender family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch13": {"type": "string", "description": "Channel 13 color (light yellow family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch14": {"type": "string", "description": "Channel 14 color (light magenta family)", "pattern": "^#?[0-9a-fA-F]{6}$"}, "ch15": {"type": "string", "description": "Channel 15 color (light cyan family)", "pattern": "^#?[0-9a-fA-F]{6}$"}}, "additionalProperties": false}}, "required": ["background", "grid_line", "counter_text", "border", "graph_background", "graph_progress_bar", "graph_center_line", "channel"]}, "visual": {"type": "object", "description": "Visual element display settings", "properties": {"show_piano": {"type": "boolean", "description": "Show/hide piano keyboard", "default": true}, "show_pianoroll": {"type": "boolean", "description": "Show/hide piano roll", "default": true}, "show_highlight": {"type": "boolean", "description": "Show/hide note highlighting effect (brightness increase for active notes)", "default": true}, "show_graphs": {"type": "boolean", "description": "Show/hide all graphs (master toggle)", "default": true}, "show_counters": {"type": "boolean", "description": "Show/hide all counters (master toggle)", "default": true}, "show_text_events": {"type": "boolean", "description": "Show/hide all text events (master toggle)", "default": false}, "show_debug": {"type": "boolean", "description": "Show/hide debug information (master toggle)", "default": true}, "graphs": {"type": "object", "description": "Individual graph visibility settings", "properties": {"show_notes": {"type": "boolean", "description": "Show/hide notes graph", "default": true}, "show_polyphony": {"type": "boolean", "description": "Show/hide polyphony graph", "default": true}, "show_nps": {"type": "boolean", "description": "Show/hide notes per second graph", "default": true}, "show_bpm": {"type": "boolean", "description": "Show/hide BPM graph", "default": true}, "show_pan": {"type": "boolean", "description": "Show/hide pan graph", "default": true}, "show_pitch_bend": {"type": "boolean", "description": "Show/hide pitch bend graph", "default": true}, "show_volume": {"type": "boolean", "description": "Show/hide volume graph", "default": true}, "show_sustain": {"type": "boolean", "description": "Show/hide sustain graph", "default": true}, "show_filter_cutoff": {"type": "boolean", "description": "Show/hide filter cutoff graph", "default": true}, "show_filter_resonance": {"type": "boolean", "description": "Show/hide filter resonance graph", "default": true}, "show_release_time": {"type": "boolean", "description": "Show/hide release time graph", "default": true}}}, "counters": {"type": "object", "description": "Counter display settings", "properties": {"show_time": {"type": "boolean", "description": "Show/hide time counter", "default": true}, "show_notes": {"type": "boolean", "description": "Show/hide notes counter", "default": true}, "show_nps": {"type": "boolean", "description": "Show/hide notes per second counter", "default": true}, "show_polyphony": {"type": "boolean", "description": "Show/hide polyphony counter", "default": true}, "show_velocity": {"type": "boolean", "description": "Show/hide velocity counter", "default": true}}}, "text_events": {"type": "object", "description": "Text event display settings", "properties": {"show_markers": {"type": "boolean", "description": "Show/hide marker events", "default": true}, "show_text": {"type": "boolean", "description": "Show/hide text events", "default": false}, "show_lyrics": {"type": "boolean", "description": "Show/hide lyric events", "default": false}}}}}, "funny": {"type": "object", "description": "Funny/Debug mode settings", "properties": {"bug_mode": {"type": "boolean", "description": "Enable bug mode: causes intentional glitches and visual artifacts when vertex data bugs or invalid data occur", "default": false}}}}, "required": ["colors"]}