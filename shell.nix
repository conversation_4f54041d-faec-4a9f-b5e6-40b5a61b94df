{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "hmp7-video-renderer-dev";
  
  buildInputs = with pkgs; [
    # Build tools
    xmake
    gcc
    gdb
    pkg-config
    cmake
    ninja
    
    # Core dependencies
    ffmpeg_7-full
    skia
    freetype
    fontconfig
    libiconv
    
    # Graphics and OpenGL
    libGL
    libGLU
    mesa
    mesa-demos
    libglvnd
    
    # EGL and X11 for headless GPU rendering
    libEGL
    xorg.libX11
    xorg.libXext
    xorg.libXrender
    xorg.libXrandr
    xorg.libXi
    xorg.libXcursor
    xorg.libXinerama
    xorg.libXxf86vm
    
    # Image libraries
    libjpeg
    libpng
    libwebp
    
    # Audio libraries (for MIDI and audio processing)
    alsa-lib
    pulseaudio
    
    # Development tools
    valgrind
    strace
    ltrace
    
    # File dialog dependencies (Linux)
    zenity
    kdialog
    
    # Additional system libraries
    zlib
    bzip2
    xz
    
    # Threading and system libraries
    glibc
    
    # JSON and TOML parsing
    nlohmann_json
    # Note: toml++ is header-only and will be handled by xmake
  ];
  
  nativeBuildInputs = with pkgs; [
    pkg-config
    makeWrapper
  ];
  
  # Environment variables for proper library detection
  shellHook = ''
    echo "🎵 HMP7 Video Renderer Development Environment"
    echo "=============================================="
    echo ""
    echo "Available tools:"
    echo "  - xmake: Build system"
    echo "  - gcc: C/C++ compiler"
    echo "  - gdb: Debugger"
    echo "  - valgrind: Memory debugging"
    echo ""
    echo "Graphics libraries:"
    echo "  - Skia: 2D graphics library"
    echo "  - OpenGL/EGL: GPU rendering"
    echo "  - FFmpeg 7: Video encoding"
    echo ""
    echo "To build the project:"
    echo "  xmake config --plat=linux --arch=x86_64"
    echo "  xmake build"
    echo ""
    echo "To run in debug mode:"
    echo "  xmake config --mode=debug"
    echo "  xmake build"
    echo "  xmake run"
    echo ""
    
    # Set up environment variables for proper library detection
    export PKG_CONFIG_PATH="${pkgs.ffmpeg_7-full}/lib/pkgconfig:${pkgs.skia}/lib/pkgconfig:${pkgs.freetype}/lib/pkgconfig:${pkgs.fontconfig}/lib/pkgconfig:$PKG_CONFIG_PATH"
    export LD_LIBRARY_PATH="${pkgs.ffmpeg_7-full}/lib:${pkgs.skia}/lib:${pkgs.libGL}/lib:${pkgs.libEGL}/lib:$LD_LIBRARY_PATH"
    export C_INCLUDE_PATH="${pkgs.skia}/include:${pkgs.ffmpeg_7-full}/include:$C_INCLUDE_PATH"
    export CPLUS_INCLUDE_PATH="${pkgs.skia}/include:${pkgs.ffmpeg_7-full}/include:$CPLUS_INCLUDE_PATH"
    
    # Ensure X11 is available for headless rendering
    export DISPLAY=:0
    
    # Set up OpenGL
    export LIBGL_DRIVERS_PATH="${pkgs.mesa.drivers}/lib/dri"
    export __EGL_VENDOR_LIBRARY_DIRS="${pkgs.mesa.drivers}/share/glvnd/egl_vendor.d"
    
    echo "Environment configured for Linux development!"
    echo "PKG_CONFIG_PATH and library paths have been set."
  '';
  
  # Additional attributes for better NixOS integration
  meta = {
    description = "Development environment for HMP7 Video Renderer";
    platforms = pkgs.lib.platforms.linux;
  };
}
