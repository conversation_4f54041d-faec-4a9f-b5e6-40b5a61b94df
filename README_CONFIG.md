# HachimitsuMIDIPlayer 設定ガイド

## 色設定

HachimitsuMIDIPlayerでは、`config.toml`または`config.json`ファイルを使用して、アプリケーションの色設定をカスタマイズできます。

### 設定ファイルの場所

設定ファイルは、アプリケーションの実行ディレクトリにある`config.toml`または`config.json`です。

### 設定ファイルの書式

設定ファイルはTOML形式またはJSON形式で記述します。色は16進数形式（`#RRGGBB`または`RRGGBB`）で指定します。

#### TOML形式
```toml
# HachimitsuMIDIPlayer 色設定ファイル
# $schema = "./config.schema.json"

[colors]
# UI色
background = "#000000"        # 背景色
grid_line = "#444444"         # グリッド線の色
border = "#808080"            # ボーダー色
graph_background = "#333333"  # グラフ背景色
counter_text = "#FFFFFF"      # カウンター表示用テキスト色（時間、ノート数、NPS、ポリフォニー、マーカーなど）
graph_text = "#FFFFFF"        # グラフ内のテキスト色

# MIDIチャンネル色
[colors.channel]
ch0 = "#882222"   # チャンネル0: 赤
ch1 = "#228822"   # チャンネル1: 緑
ch2 = "#222288"   # チャンネル2: 青
ch3 = "#886622"   # チャンネル3: 黄
ch4 = "#882288"   # チャンネル4: マゼンタ
ch5 = "#228888"   # チャンネル5: シアン
ch6 = "#884422"   # チャンネル6: オレンジ
ch7 = "#552288"   # チャンネル7: 紫
ch8 = "#228844"   # チャンネル8: ライトグリーン
ch9 = "#225588"   # チャンネル9: ライトブルー
ch10 = "#882244"  # チャンネル10: ピンク
```

#### JSON形式
```json
{
  "$schema": "./config.schema.json",
  "colors": {
    "background": "#000000",
    "grid_line": "#444444",
    "border": "#808080",
    "graph_background": "#333333",
    "counter_text": "#FFFFFF",
    "graph_text": "#FFFFFF",
    "channel": {
      "ch0": "#882222",
      "ch1": "#228822",
      "ch2": "#222288",
      "ch3": "#886622",
      "ch4": "#882288",
      "ch5": "#228888",
      "ch6": "#884422",
      "ch7": "#552288",
      "ch8": "#228844",
      "ch9": "#225588",
      "ch10": "#882244",
      "ch11": "#448822",
      "ch12": "#444488",
      "ch13": "#888844",
      "ch14": "#884488",
      "ch15": "#448888"
    }
  }
}
```

### 設定可能な項目

#### UI色

- `background`: アプリケーション全体の背景色
- `grid_line`: グリッド線の色
- `border`: ボーダーの色
- `graph_background`: グラフの背景色
- `counter_text`: カウンター表示用テキスト色（時間、ノート数、NPS、ポリフォニー、マーカー、テキスト、歌詞など）
- `graph_text`: グラフ内のテキスト色

#### MIDIチャンネル色

- `ch0`～`ch15`: 各MIDIチャンネルの色（0～15）

### JSONスキーマ

`config.schema.json`ファイルには、設定ファイルのJSONスキーマが定義されています。このスキーマファイルを利用することで、VSCodeなどのエディタで設定ファイル編集時に入力補完や検証機能が利用できます。

### カスタム色設定の適用方法

1. `config.toml`または`config.json`ファイルをテキストエディタで開く
2. 色コードを希望の色に変更する
3. ファイルを保存する
4. アプリケーションを起動する

設定ファイルが見つからない場合や読み込みに失敗した場合は、デフォルトの色設定が使用されます。

## トラブルシューティング

### 設定ファイルが認識されない

- ファイル名が正確に `config.toml` または `config.json` であることを確認してください
- ファイルがアプリケーションの実行ディレクトリにあることを確認してください
- ファイルがUTF-8形式で保存されていることを確認してください

### 色が適用されない

- 色コードが有効な16進数形式（`#RRGGBB`または`RRGGBB`）で記述されていることを確認してください
- TOMLまたはJSONの構文が正しいことを確認してください

## フォーマットの変換

現在の設定をTOMLからJSONに、またはJSONからTOMLに変換したい場合は、アプリケーション内の設定保存機能を使うか、オンラインコンバーターを利用してください。