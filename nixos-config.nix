# NixOS Configuration for HMP7 Video Renderer
# Add this to your NixOS configuration.nix or as a separate module

{ config, pkgs, ... }:

{
  # Enable OpenGL and hardware acceleration
  hardware.opengl = {
    enable = true;
    driSupport = true;
    driSupport32Bit = true;
    
    # Enable additional GPU drivers
    extraPackages = with pkgs; [
      intel-media-driver  # Intel GPUs
      vaapiIntel         # Intel VA-API
      vaapiVdpau         # VDPAU support
      libvdpau-va-gl     # VDPAU to VA-API bridge
      mesa.drivers       # Mesa drivers
    ];
  };

  # Enable NVIDIA support (uncomment if you have NVIDIA GPU)
  # hardware.nvidia = {
  #   modesetting.enable = true;
  #   powerManagement.enable = false;
  #   powerManagement.finegrained = false;
  #   open = false;
  #   nvidiaSettings = true;
  #   package = config.boot.kernelPackages.nvidiaPackages.stable;
  # };

  # System packages needed for HMP7 Video Renderer
  environment.systemPackages = with pkgs; [
    # Build tools
    xmake
    gcc
    gdb
    pkg-config
    cmake
    ninja
    
    # Core dependencies
    ffmpeg_7-full
    skia
    freetype
    fontconfig
    libiconv
    
    # Graphics libraries
    libGL
    libGLU
    mesa
    libEGL
    
    # X11 libraries
    xorg.libX11
    xorg.libXext
    xorg.libXrender
    xorg.libXrandr
    xorg.libXi
    xorg.libXcursor
    xorg.libXinerama
    xorg.libXxf86vm
    
    # Image libraries
    libjpeg
    libpng
    libwebp
    
    # Audio libraries
    alsa-lib
    pulseaudio
    
    # File dialog support
    zenity
    kdialog
    
    # Development tools
    valgrind
    strace
    ltrace
    
    # JSON and TOML support
    nlohmann_json
    
    # System libraries
    zlib
    bzip2
    xz
  ];

  # Enable audio support
  sound.enable = true;
  hardware.pulseaudio.enable = true;
  # OR use PipeWire (comment out pulseaudio above)
  # security.rtkit.enable = true;
  # services.pipewire = {
  #   enable = true;
  #   alsa.enable = true;
  #   alsa.support32Bit = true;
  #   pulse.enable = true;
  # };

  # Enable X11 (needed for headless GPU rendering)
  services.xserver = {
    enable = true;
    # You can disable the display manager if running headless
    # displayManager.startx.enable = true;
  };

  # Enable development tools
  programs.gdb.enable = true;

  # Environment variables for development
  environment.variables = {
    # Ensure libraries can be found
    PKG_CONFIG_PATH = "${pkgs.ffmpeg_7-full}/lib/pkgconfig:${pkgs.skia}/lib/pkgconfig";
    
    # OpenGL configuration
    LIBGL_DRIVERS_PATH = "${pkgs.mesa.drivers}/lib/dri";
    __EGL_VENDOR_LIBRARY_DIRS = "${pkgs.mesa.drivers}/share/glvnd/egl_vendor.d";
  };

  # Fonts (useful for the application)
  fonts.packages = with pkgs; [
    noto-fonts
    noto-fonts-cjk
    noto-fonts-emoji
    liberation_ttf
    fira-code
    fira-code-symbols
  ];

  # Optional: Enable container support for testing
  # virtualisation.docker.enable = true;
  # virtualisation.podman.enable = true;

  # Optional: Enable development services
  # services.postgresql.enable = true;
  # services.redis.enable = true;
}
