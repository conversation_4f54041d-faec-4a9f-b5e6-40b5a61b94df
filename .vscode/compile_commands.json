[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/core/app_state.cpp.o", "src/core/app_state.cpp"], "file": "src/core/app_state.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/core/gpu_batch_renderer.cpp.o", "src/core/gpu_batch_renderer.cpp"], "file": "src/core/gpu_batch_renderer.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/core/gpu_context.cpp.o", "src/core/gpu_context.cpp"], "file": "src/core/gpu_context.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/core/main.cpp.o", "src/core/main.cpp"], "file": "src/core/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/midi/midi_analysis.cpp.o", "src/midi/midi_analysis.cpp"], "file": "src/midi/midi_analysis.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/midi/midi_file.cpp.o", "src/midi/midi_file.cpp"], "file": "src/midi/midi_file.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/midi/midi_tempo.cpp.o", "src/midi/midi_tempo.cpp"], "file": "src/midi/midi_tempo.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/midi/midi_text.cpp.o", "src/midi/midi_text.cpp"], "file": "src/midi/midi_text.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/midi/note_processor.cpp.o", "src/midi/note_processor.cpp"], "file": "src/midi/note_processor.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/video/frame_renderer.cpp.o", "src/video/frame_renderer.cpp"], "file": "src/video/frame_renderer.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/video/graphs.cpp.o", "src/video/graphs.cpp"], "file": "src/video/graphs.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/video/midi_utils.cpp.o", "src/video/midi_utils.cpp"], "file": "src/video/midi_utils.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/video/video_encoder.cpp.o", "src/video/video_encoder.cpp"], "file": "src/video/video_encoder.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/color.cpp.o", "src/utils/color.cpp"], "file": "src/utils/color.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/config_manager.cpp.o", "src/utils/config_manager.cpp"], "file": "src/utils/config_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/executable_path.cpp.o", "src/utils/executable_path.cpp"], "file": "src/utils/executable_path.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/performance.cpp.o", "src/utils/performance.cpp"], "file": "src/utils/performance.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/reload_config.cpp.o", "src/utils/reload_config.cpp"], "file": "src/utils/reload_config.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/string_utils.cpp.o", "src/utils/string_utils.cpp"], "file": "src/utils/string_utils.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/utils/texture_manager.cpp.o", "src/utils/texture_manager.cpp"], "file": "src/utils/texture_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/threading.cpp.o", "src/threading.cpp"], "file": "src/threading.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/ui/file_dialog.cpp.o", "src/ui/file_dialog.cpp"], "file": "src/ui/file_dialog.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/ui/font_manager.cpp.o", "src/ui/font_manager.cpp"], "file": "src/ui/font_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/ui/piano_roll.cpp.o", "src/ui/piano_roll.cpp"], "file": "src/ui/piano_roll.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/ui/piano_keyboard.cpp.o", "src/ui/piano_keyboard.cpp"], "file": "src/ui/piano_keyboard.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HMP-Devs/hmp7-video-renderer(HMR)", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Ibuild/.gens/counter_animation/linux/x86_64/rules/utils/bin2c", "-DSK_GL", "-DSK_GANESH", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-o", "build/.objs/counter_animation/linux/x86_64/src/ui/piano_roll_thread.cpp.o", "src/ui/piano_roll_thread.cpp"], "file": "src/ui/piano_roll_thread.cpp"}]