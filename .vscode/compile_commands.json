[{"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\core\\app_state.cpp.obj", "src\\core\\app_state.cpp"], "file": "src\\core\\app_state.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\core\\gpu_batch_renderer.cpp.obj", "src\\core\\gpu_batch_renderer.cpp"], "file": "src\\core\\gpu_batch_renderer.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\core\\gpu_context.cpp.obj", "src\\core\\gpu_context.cpp"], "file": "src\\core\\gpu_context.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\core\\main.cpp.obj", "src\\core\\main.cpp"], "file": "src\\core\\main.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\midi\\midi_analysis.cpp.obj", "src\\midi\\midi_analysis.cpp"], "file": "src\\midi\\midi_analysis.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\midi\\midi_file.cpp.obj", "src\\midi\\midi_file.cpp"], "file": "src\\midi\\midi_file.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\midi\\midi_tempo.cpp.obj", "src\\midi\\midi_tempo.cpp"], "file": "src\\midi\\midi_tempo.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\midi\\midi_text.cpp.obj", "src\\midi\\midi_text.cpp"], "file": "src\\midi\\midi_text.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\midi\\note_processor.cpp.obj", "src\\midi\\note_processor.cpp"], "file": "src\\midi\\note_processor.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\video\\frame_renderer.cpp.obj", "src\\video\\frame_renderer.cpp"], "file": "src\\video\\frame_renderer.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\video\\graphs.cpp.obj", "src\\video\\graphs.cpp"], "file": "src\\video\\graphs.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\video\\midi_utils.cpp.obj", "src\\video\\midi_utils.cpp"], "file": "src\\video\\midi_utils.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\video\\video_encoder.cpp.obj", "src\\video\\video_encoder.cpp"], "file": "src\\video\\video_encoder.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\color.cpp.obj", "src\\utils\\color.cpp"], "file": "src\\utils\\color.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\config_manager.cpp.obj", "src\\utils\\config_manager.cpp"], "file": "src\\utils\\config_manager.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\executable_path.cpp.obj", "src\\utils\\executable_path.cpp"], "file": "src\\utils\\executable_path.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\performance.cpp.obj", "src\\utils\\performance.cpp"], "file": "src\\utils\\performance.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\reload_config.cpp.obj", "src\\utils\\reload_config.cpp"], "file": "src\\utils\\reload_config.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\string_utils.cpp.obj", "src\\utils\\string_utils.cpp"], "file": "src\\utils\\string_utils.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\utils\\texture_manager.cpp.obj", "src\\utils\\texture_manager.cpp"], "file": "src\\utils\\texture_manager.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\threading.cpp.obj", "src\\threading.cpp"], "file": "src\\threading.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\ui\\file_dialog.cpp.obj", "src\\ui\\file_dialog.cpp"], "file": "src\\ui\\file_dialog.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\ui\\font_manager.cpp.obj", "src\\ui\\font_manager.cpp"], "file": "src\\ui\\font_manager.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\ui\\piano_roll.cpp.obj", "src\\ui\\piano_roll.cpp"], "file": "src\\ui\\piano_roll.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\ui\\piano_keyboard.cpp.obj", "src\\ui\\piano_keyboard.cpp"], "file": "src\\ui\\piano_keyboard.cpp"}, {"directory": "c:\\Users\\<USER>\\Documents\\HachimitsuMIDIPlayer\\hmp7-test", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-IC:\\msys64\\mingw64\\include\\skia", "-IC:\\msys64\\mingw64\\include\\skia\\include", "-IC:\\msys64\\mingw64\\include\\skia\\include\\core", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu", "-IC:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl", "-Ibuild\\.gens\\counter_animation\\mingw\\x86_64\\debug\\rules\\utils\\bin2c", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.0\\4e63b890cbba4216952b03d26e51c8a8\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include", "-IC:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\n\\nlohmann_json\\v3.12.0\\942e578ec9634b1c912e6e1a23434b35\\include", "-DSK_GL", "-DSK_GANESH", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-D_GNU_SOURCE", "-D_DEFAULT_SOURCE", "-DHEADLESS_MODE", "-DGPU_RENDERING_ENABLED", "-DWINDOWS_PLATFORM", "-DX264_API_IMPORTS", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-gdwarf-4", "-o", "build\\.objs\\counter_animation\\mingw\\x86_64\\debug\\src\\ui\\piano_roll_thread.cpp.obj", "src\\ui\\piano_roll_thread.cpp"], "file": "src\\ui\\piano_roll_thread.cpp"}]