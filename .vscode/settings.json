{"files.associations": {"app_state.h": "c", "stdio.h": "c", "stdlib.h": "c", "keyblack.png.h": "c", "xloctime": "cpp"}, "C_Cpp.default.includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src", "${workspaceFolder}/build/.gens/counter_animation/mingw/x86_64/debug/rules/utils/bin2c", "${workspaceFolder}/build/.gens/counter_animation/mingw/x86_64/release/rules/utils/bin2c", "C:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\f\\ffmpeg\\7.1\\b60a947ad14a4f8a9888e3deecf1e86f\\include", "C:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\i\\imgui\\v1.91.8\\2c099577ff1944d48ad6b4aba528f4a9\\include", "C:\\Users\\<USER>\\AppData\\Local\\.xmake\\packages\\t\\toml++\\v3.4.0\\5bab05658a644f03b9963fe16090a30b\\include"]}