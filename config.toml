# HachimitsuMIDIRenderer Color Configuration File
# $schema = "./config.schema.json"

[colors]
# Color Mode Settings
color_mode = "channel"        # Color mode: "channel" or "track"
                              # "channel": Use MIDI channel colors (colors.channel)
                              # "track": Use track-based colors (first 16 from colors.channel, then random)
                              # This mode applies only to piano roll and piano keyboard

# UI Colors
background = "#000000"        # Background color
grid_line = "#444444"         # Grid line color
border = "#808080"            # Border color
graph_background = "#333333"  # Graph background color
counter_text = "#FFFFFF"      # Counter display text color (Time, Notes, NPS, Polyphony, etc.)
graph_text = "#FFFFFF"        # Graph text color
graph_progress_bar = "#00CC00" # Graph progress bar color
graph_center_line = "#808080"  # Graph center line color

# MIDI Channel Colors
[colors.channel]
ch0 = "#CC4444"   # Channel 0: Red
ch1 = "#44CC44"   # Channel 1: Green
ch2 = "#4444CC"   # Channel 2: Blue
ch3 = "#CCAA44"   # Channel 3: Yellow
ch4 = "#CC44CC"   # Channel 4: Magenta
ch5 = "#44CCCC"   # Channel 5: <PERSON>an
ch6 = "#CC6644"   # Channel 6: Orange
ch7 = "#8844CC"   # Channel 7: Purple
ch8 = "#44CC66"   # Channel 8: Light green
ch9 = "#4488CC"   # Channel 9: Light blue
ch10 = "#CC4466"  # Channel 10: Pink
ch11 = "#66CC44"  # Channel 11: Lime
ch12 = "#6666CC"  # Channel 12: Lavender
ch13 = "#CCCC66"  # Channel 13: Light yellow
ch14 = "#CC66CC"  # Channel 14: Light magenta
ch15 = "#66CCCC"  # Channel 15: Light cyan

# Visual Elements Display Settings
[visual]
# Master toggle for all visual elements
show_all = true               # Master toggle for all visual elements

# Main UI components visibility
show_piano = true             # Show/hide piano keyboard
show_pianoroll = true         # Show/hide piano roll
show_highlight = true         # Show/hide note highlighting effect (brightness increase for active notes)
show_graphs = false            # Show/hide all graphs (master toggle)
show_counters = false         # Show/hide all counters (master toggle)
show_text_events = false      # Show/hide all text events (master toggle)
show_debug = true             # Show/hide debug information (master toggle)

# Individual graph visibility settings
[visual.graphs]
show_notes = false            # Show/hide notes graph
show_polyphony = false        # Show/hide polyphony graph
show_nps = false              # Show/hide notes per second graph
show_bpm = true               # Show/hide BPM graph
show_pan = false              # Show/hide pan graph
show_pitch_bend = false       # Show/hide pitch bend graph
show_volume = true            # Show/hide volume graph
show_sustain = true           # Show/hide sustain graph
show_filter_cutoff = true     # Show/hide filter cutoff graph
show_filter_resonance = true  # Show/hide filter resonance graph
show_release_time = true      # Show/hide release time graph

# Counter display settings
[visual.counters]
show_time = true              # Show/hide time counter
show_notes = true             # Show/hide notes counter
show_nps = true               # Show/hide notes per second counter
show_polyphony = true         # Show/hide polyphony counter

# Text event display settings
[visual.text_events]
show_markers = true           # Show/hide marker events
show_text = false             # Show/hide text events
show_lyrics = false           # Show/hide lyric events

# Funny/Debug Mode Settings
[funny]
bug_mode = false              # Enable bug mode: causes intentional glitches and visual artifacts when vertex data bugs or invalid data occur
