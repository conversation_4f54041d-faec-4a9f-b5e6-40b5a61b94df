add_rules("mode.debug", "mode.release")

-- プロジェクト設定
set_project("CounterAnimation")
set_version("1.0.0")
set_languages("c11", "cxx17")

-- コンパイラの設定
if is_plat("windows") then
    set_toolchains("mingw")
elseif is_plat("linux") then
    -- Linuxでのコンパイラ設定
    set_toolchains("gcc")

    -- GCC固有の最適化フラグ
    if is_mode("release") then
        add_cxflags("-march=native", "-mtune=native")  -- CPU固有の最適化
        add_cxflags("-flto")  -- Link Time Optimization
        add_ldflags("-flto")
    end
end

-- 必要なライブラリの追加
add_requires("ffmpeg", {
    version = "7.0", -- 必要であればバージョン指定
    configs = {
        libx264 = true,  -- libx264を有効にする
        nvenc = true,    -- nvencを有効にする（NVIDIA GPU）
        libx265 = true,  -- libx265を有効にする
        vaapi = true,    -- VA-API（Linux GPU加速）
        vdpau = true,    -- VDPAU（Linux GPU加速）
        shared = true
    }
})
-- システムにインストールされたskiaを使用
-- add_requires("skia")
add_requires("libiconv")
add_requires("freetype")
add_requires("fontconfig")
add_requires("toml++")
add_requires("nlohmann_json")
-- ヘッドレスモードのため、GUI関連ライブラリは不要
-- add_requires("imgui")
-- add_requires("glfw")
-- add_requires("opengl")
-- add_requires("glew")
-- add_requires("egl-headers", {version = "2023.12.16"})

if is_mode("debug") then
    set_config("debugger", "gdb") -- 新しい構文
    print("Debug mode: debugger should be called, using gdb.")
end

-- タスクの説明
set_description("List all dependency DLL files for the target")

-- アプリケーションのターゲット設定
target("counter_animation")
    set_kind("binary")
    add_rules("utils.bin2c", {extensions = {".png", ".vs", ".fs", ".ttf"}})

    -- Warning and optimization settings
    add_cflags("-O3")    add_cxxflags("-O3")
    add_ldflags("-O3")
    -- Skiaの最適化設定（GPUレンダリング用）
    add_defines("SK_GL", "SK_GANESH")
    
    -- ビルド後の設定ファイルコピー
    after_build(function (target)
        local configs = {"config.toml", "config.schema.json"}
        local targetdir = target:targetdir()
        for _, config in ipairs(configs) do
            if os.exists(config) then
                os.cp(config, targetdir)
                print("Copied " .. config .. " to " .. targetdir)
            end
        end
    end)

    -- Settings for GDB debugging
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_cflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_cxxflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_ldflags("-g3")
        add_defines("DEBUG", "DEBUG_GL", "GL_DEBUG")
        print("Debug mode: target debug mode called.")

        -- デバッグ情報を強化
        if is_plat("windows", "mingw") then
            add_cxflags("-gdwarf-4")  -- DWARF形式のデバッグ情報
            add_ldflags("-Wl,--export-all-symbols") -- すべてのシンボルをエクスポート
        end
    end

    -- ソースファイルの追加（GPUレンダリング対応ヘッドレスモード用）
    add_files("src/core/*.cpp")
    add_files("src/midi/*.cpp")
    add_files("src/video/*.cpp")
    add_files("src/utils/*.cpp")
    -- ルートディレクトリのファイル
    add_files("src/*.cpp")
    -- 必要なUIファイルのみ追加（ImGuiマネージャー以外）
    add_files("src/ui/file_dialog.cpp")
    add_files("src/ui/font_manager.cpp")
    add_files("src/ui/piano_roll.cpp")
    add_files("src/ui/piano_keyboard.cpp")
    add_files("src/ui/piano_roll_thread.cpp")
    -- ImGuiファイルは除外
    -- add_files("src/imgui/*.cpp")
    -- add_files("src/ui/imgui_manager.cpp")
    add_files("resources/textures/*.png")    add_files("resources/fonts/*.ttf")

    -- インクルードパスとライブラリ設定（ヘッドレスモード用）
    add_packages("ffmpeg", "libiconv", "freetype", "fontconfig", "toml++", "nlohmann_json")
    add_includedirs("include")

    -- プラットフォーム別のインクルードパス設定
    if is_plat("windows", "mingw") then
        add_includedirs("C:\\msys64\\mingw64\\include\\skia")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\core")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\gpu")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl")
        -- ライブラリパスを追加
        add_linkdirs("C:\\msys64\\mingw64\\lib")
    elseif is_plat("linux") then
        -- Linuxでのインクルードパス設定（複数のディストリビューションに対応）
        add_includedirs("/usr/include/skia")
        add_includedirs("/usr/local/include/skia")
        add_includedirs("/usr/include/skia/include")
        add_includedirs("/usr/local/include/skia/include")
        add_includedirs("/usr/include/skia/include/core")
        add_includedirs("/usr/local/include/skia/include/core")
        add_includedirs("/usr/include/skia/include/gpu")
        add_includedirs("/usr/local/include/skia/include/gpu")
        add_includedirs("/usr/include/skia/include/gpu/gl")
        add_includedirs("/usr/local/include/skia/include/gpu/gl")

        -- NixOS対応のインクルードパス
        if os.getenv("NIX_STORE") then
            local nix_skia_path = os.getenv("C_INCLUDE_PATH")
            if nix_skia_path then
                for path in string.gmatch(nix_skia_path, "([^:]+)") do
                    add_includedirs(path)
                end
            end
        end

        -- Arch Linux対応
        add_includedirs("/usr/include/skia")
        add_includedirs("/usr/include")

        -- Ubuntu/Debian対応
        add_includedirs("/usr/include/x86_64-linux-gnu")

        -- Fedora/CentOS対応
        add_includedirs("/usr/include")
        add_includedirs("/usr/local/include")

        -- Linuxでのライブラリパス設定（複数のディストリビューションに対応）
        add_linkdirs("/usr/lib")
        add_linkdirs("/usr/local/lib")
        add_linkdirs("/usr/lib/x86_64-linux-gnu")
        add_linkdirs("/usr/lib64")
        add_linkdirs("/lib/x86_64-linux-gnu")
        add_linkdirs("/lib64")

        -- NixOS対応のライブラリパス
        if os.getenv("NIX_STORE") then
            local nix_lib_path = os.getenv("LD_LIBRARY_PATH")
            if nix_lib_path then
                for path in string.gmatch(nix_lib_path, "([^:]+)") do
                    add_linkdirs(path)
                end
            end
        end
    end

    -- ライブラリを追加（GPUレンダリング用 - SkiaのGPU処理に必要なOpenGL/EGLライブラリを追加）
    if is_plat("windows", "mingw") then
        add_linkdirs("C:\\msys64\\mingw64\\lib")
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux", "opengl32", "gdi32")
    elseif is_plat("linux") then
        -- Linuxでのライブラリリンク（GPU対応）
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux")

        -- OpenGL/EGL ライブラリ
        add_links("GL", "EGL", "GLU")

        -- FFmpeg ライブラリ
        add_links("avformat", "avcodec", "avutil", "swscale", "swresample", "avfilter")

        -- X11 ライブラリ（ヘッドレスモード用）
        add_links("X11", "Xext", "Xrender", "Xrandr", "Xi", "Xcursor", "Xinerama", "Xxf86vm")

        -- システムライブラリ
        add_links("z", "bz2", "lzma")

        -- 数学ライブラリ
        add_links("m")

        -- 動的リンクライブラリ
        add_links("dl")
    elseif is_plat("macosx") then
        add_frameworks("OpenGL")
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux", "avformat", "avcodec", "avutil", "swscale", "swresample")
    end
    -- プラットフォーム別の定義設定（GPUレンダリング用ヘッドレスモード）
    add_defines(
        "PLATFORM_DESKTOP",
        "_GNU_SOURCE",
        "_DEFAULT_SOURCE",
        "HEADLESS_MODE",
        "GPU_RENDERING_ENABLED"
    )

    if is_plat("windows", "mingw") then
        add_defines(
            "WINDOWS_PLATFORM" -- Windows対応
        )
    elseif is_plat("linux") then
        add_defines(
            "LINUX_PLATFORM" -- Linux対応
        )
    end

    -- プラットフォーム別設定（ヘッドレスモード用）
    if is_plat("windows", "mingw") then
        add_syslinks("ole32", "user32", "gdi32", "comdlg32", "pthread", "uuid")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    elseif is_plat("linux") then
        -- Linuxでのシステムライブラリ（GPU処理対応）
        add_syslinks("pthread", "dl", "m", "rt")

        -- X11関連ライブラリ（ヘッドレスモード用）
        add_syslinks("X11", "Xext", "Xrender")

        -- フォント関連ライブラリ
        add_links("fontconfig", "freetype")

        -- OpenMP対応（利用可能な場合のみ）
        if has_config("openmp") or os.getenv("OPENMP_ENABLED") then
            add_cflags("-fopenmp")
            add_ldflags("-fopenmp")
            add_links("gomp")  -- GNU OpenMP
        end

        -- デバッグモード時の追加設定
        if is_mode("debug") then
            add_cflags("-fsanitize=address", "-fno-omit-frame-pointer")
            add_ldflags("-fsanitize=address")
        end
    else
        -- その他のプラットフォーム（macOS等）
        add_syslinks("pthread")

        -- OpenMP対応（利用可能な場合のみ）
        if has_config("openmp") or os.getenv("OPENMP_ENABLED") then
            add_cflags("-fopenmp")
            add_ldflags("-fopenmp")
        end
    end

task("list_dlls")
    -- タスク実行時の処理
    on_run(function ()
        -- ターゲットを明示的にロードする
        import("core.project.project")
        project.load()

        -- "counter_animation" ターゲットを取得
        local target = target("counter_animation")
        if not target then
            print("Target 'counter_animation' not found.")
            return
        end

        print("Dependency DLLs for '%s':", target:name())

        -- 依存パッケージからDLLファイルを取得する
        local dlls_found = {}
        -- target:dep("packages") を使って依存パッケージを取得する
        for _, dep in ipairs(target:dep("packages")) do
            local pkg = dep:pkg()
            if pkg then
                -- fetch()でパッケージの詳細情報を取得
                local fetchinfo = pkg:fetch()
                if fetchinfo and fetchinfo.bindirs then
                    for _, bindir in ipairs(fetchinfo.bindirs) do
                        for _, file in ipairs(os.files(path.join(bindir, "*.dll"))) do
                            dlls_found[path.filename(file)] = file
                        end
                    end
                end
                if fetchinfo and fetchinfo.libdirs then
                     for _, libdir in ipairs(fetchinfo.libdirs) do
                        for _, file in ipairs(os.files(path.join(libdir, "*.dll"))) do
                            dlls_found[path.filename(file)] = file
                        end
                    end
                end
            end
        end

        -- 見つかったDLLをソートして表示
        local sorted_names = {}
        for name, _ in pairs(dlls_found) do
            table.insert(sorted_names, name)
        end
        table.sort(sorted_names)

        if #sorted_names > 0 then
            for _, name in ipairs(sorted_names) do
                print(" - %s (%s)", name, dlls_found[name])
            end
        else
            print("No dependency DLLs found.")
        end
    end)