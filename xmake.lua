add_rules("mode.debug", "mode.release")

-- プロジェクト設定
set_project("CounterAnimation")
set_version("1.0.0")
set_languages("c11", "cxx17")

-- コンパイラの設定（Mingw）
if is_plat("windows") then
    set_toolchains("mingw")
end

-- 必要なライブラリの追加
add_requires("ffmpeg", {
    version = "7.0", -- 必要であればバージョン指定
    configs = {
        libx264 = true,  -- libx264を有効にする
        nvenc = true,    -- nvencを有効にする
        libx265 = true,  -- libx265を有効にする
        shared = true
    }
})
-- システムにインストールされたskiaを使用
-- add_requires("skia")
add_requires("libiconv")
add_requires("freetype")
add_requires("fontconfig")
add_requires("toml++")
add_requires("nlohmann_json")
-- ヘッドレスモードのため、GUI関連ライブラリは不要
-- add_requires("imgui")
-- add_requires("glfw")
-- add_requires("opengl")
-- add_requires("glew")
-- add_requires("egl-headers", {version = "2023.12.16"})

if is_mode("debug") then
    set_config("debugger", "gdb") -- 新しい構文
    print("Debug mode: debugger should be called, using gdb.")
end

-- タスクの説明
set_description("List all dependency DLL files for the target")

-- アプリケーションのターゲット設定
target("counter_animation")
    set_kind("binary")
    add_rules("utils.bin2c", {extensions = {".png", ".vs", ".fs", ".ttf"}})

    -- Warning and optimization settings
    add_cflags("-O3")    add_cxxflags("-O3")
    add_ldflags("-O3")
    -- Skiaの最適化設定（GPUレンダリング用）
    add_defines("SK_GL", "SK_GANESH")
    
    -- ビルド後の設定ファイルコピー
    after_build(function (target)
        local configs = {"config.toml", "config.schema.json"}
        local targetdir = target:targetdir()
        for _, config in ipairs(configs) do
            if os.exists(config) then
                os.cp(config, targetdir)
                print("Copied " .. config .. " to " .. targetdir)
            end
        end
    end)

    -- Settings for GDB debugging
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_cflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_cxxflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_ldflags("-g3")
        add_defines("DEBUG", "DEBUG_GL", "GL_DEBUG")
        print("Debug mode: target debug mode called.")

        -- デバッグ情報を強化
        if is_plat("windows", "mingw") then
            add_cxflags("-gdwarf-4")  -- DWARF形式のデバッグ情報
            add_ldflags("-Wl,--export-all-symbols") -- すべてのシンボルをエクスポート
        end
    end

    -- ソースファイルの追加（GPUレンダリング対応ヘッドレスモード用）
    add_files("src/core/*.cpp")
    add_files("src/midi/*.cpp")
    add_files("src/video/*.cpp")
    add_files("src/utils/*.cpp")
    -- ルートディレクトリのファイル
    add_files("src/*.cpp")
    -- 必要なUIファイルのみ追加（ImGuiマネージャー以外）
    add_files("src/ui/file_dialog.cpp")
    add_files("src/ui/font_manager.cpp")
    add_files("src/ui/piano_roll.cpp")
    add_files("src/ui/piano_keyboard.cpp")
    add_files("src/ui/piano_roll_thread.cpp")
    -- ImGuiファイルは除外
    -- add_files("src/imgui/*.cpp")
    -- add_files("src/ui/imgui_manager.cpp")
    add_files("resources/textures/*.png")    add_files("resources/fonts/*.ttf")

    -- インクルードパスとライブラリ設定（ヘッドレスモード用）
    add_packages("ffmpeg", "libiconv", "freetype", "fontconfig", "toml++", "nlohmann_json")
    add_includedirs("include")

    -- プラットフォーム別のインクルードパス設定
    if is_plat("windows", "mingw") then
        add_includedirs("C:\\msys64\\mingw64\\include\\skia")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\core")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\gpu")
        add_includedirs("C:\\msys64\\mingw64\\include\\skia\\include\\gpu\\gl")
        -- ライブラリパスを追加
        add_linkdirs("C:\\msys64\\mingw64\\lib")
    elseif is_plat("linux") then
        -- Linuxでのインクルードパス設定
        add_includedirs("/usr/include/skia")
        add_includedirs("/usr/local/include/skia")
        add_includedirs("/usr/include/skia/include")
        add_includedirs("/usr/local/include/skia/include")
        add_includedirs("/usr/include/skia/include/core")
        add_includedirs("/usr/local/include/skia/include/core")
        add_includedirs("/usr/include/skia/include/gpu")
        add_includedirs("/usr/local/include/skia/include/gpu")
        add_includedirs("/usr/include/skia/include/gpu/gl")
        add_includedirs("/usr/local/include/skia/include/gpu/gl")


        -- Linuxでのライブラリパス設定
        add_linkdirs("/usr/lib")
        add_linkdirs("/usr/local/lib")
        add_linkdirs("/usr/lib/x86_64-linux-gnu")
    end

    -- ライブラリを追加（GPUレンダリング用 - SkiaのGPU処理に必要なOpenGL/EGLライブラリを追加）
    if is_plat("windows", "mingw") then
        add_linkdirs("C:\\msys64\\mingw64\\lib")
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux", "opengl32", "gdi32")
    elseif is_plat("linux") then
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux", "GL", "EGL", "avformat", "avcodec", "avutil", "swscale", "swresample")
    elseif is_plat("macosx") then
        add_frameworks("OpenGL")
        add_links("skia", "jpeg", "png", "webp", "webpdemux", "webpmux", "avformat", "avcodec", "avutil", "swscale", "swresample")
    end
    -- プラットフォーム別の定義設定（GPUレンダリング用ヘッドレスモード）
    add_defines(
        "PLATFORM_DESKTOP",
        "_GNU_SOURCE",
        "_DEFAULT_SOURCE",
        "HEADLESS_MODE",
        "GPU_RENDERING_ENABLED"
    )

    if is_plat("windows", "mingw") then
        add_defines(
            "WINDOWS_PLATFORM" -- Windows対応
        )
    elseif is_plat("linux") then
        add_defines(
            "LINUX_PLATFORM" -- Linux対応
        )
    end

    -- プラットフォーム別設定（ヘッドレスモード用）
    if is_plat("windows", "mingw") then
        add_syslinks("ole32", "user32", "gdi32", "comdlg32", "pthread", "uuid")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    elseif is_plat("linux") then
        -- Linuxでのシステムライブラリ（GPU処理対応）
        add_syslinks("pthread", "dl", "m", "X11")
        -- その他のLinux固有ライブラリ
        add_links("rt", "fontconfig", "freetype")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    else
        add_syslinks("pthread")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    end

task("list_dlls")
    -- タスク実行時の処理
    on_run(function ()
        -- ターゲットを明示的にロードする
        import("core.project.project")
        project.load()

        -- "counter_animation" ターゲットを取得
        local target = target("counter_animation")
        if not target then
            print("Target 'counter_animation' not found.")
            return
        end

        print("Dependency DLLs for '%s':", target:name())

        -- 依存パッケージからDLLファイルを取得する
        local dlls_found = {}
        -- target:dep("packages") を使って依存パッケージを取得する
        for _, dep in ipairs(target:dep("packages")) do
            local pkg = dep:pkg()
            if pkg then
                -- fetch()でパッケージの詳細情報を取得
                local fetchinfo = pkg:fetch()
                if fetchinfo and fetchinfo.bindirs then
                    for _, bindir in ipairs(fetchinfo.bindirs) do
                        for _, file in ipairs(os.files(path.join(bindir, "*.dll"))) do
                            dlls_found[path.filename(file)] = file
                        end
                    end
                end
                if fetchinfo and fetchinfo.libdirs then
                     for _, libdir in ipairs(fetchinfo.libdirs) do
                        for _, file in ipairs(os.files(path.join(libdir, "*.dll"))) do
                            dlls_found[path.filename(file)] = file
                        end
                    end
                end
            end
        end

        -- 見つかったDLLをソートして表示
        local sorted_names = {}
        for name, _ in pairs(dlls_found) do
            table.insert(sorted_names, name)
        end
        table.sort(sorted_names)

        if #sorted_names > 0 then
            for _, name in ipairs(sorted_names) do
                print(" - %s (%s)", name, dlls_found[name])
            end
        else
            print("No dependency DLLs found.")
        end
    end)